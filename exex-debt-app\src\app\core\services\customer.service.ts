import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class CustomerService {
    constructor() {}

    getCustomerData() {
        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                customerId: 'COD' + i,
                customerName: 'Customer' + i,
                phoneNumber: '039876549' + i,
                email: 'nguyenvan' + i + '@gmail.com',
                address: 'HCM',
                status: 'Active',
                creditLimit: 1000000,
                currentBalance: 4900 + i,
                currencyId: 'Cur' + i,
            };
        });

        return mockData;
    }

    getInvoiceData() {
        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                invoiceId: 'Inv' + i,
                customerId: 'Customer' + i,
                invoiceDate: '02/02/2025',
                dueDate: '02/02/2025',
                totalAmount: 4900 + i,
                paidAmount: 2900 + i,
                remainingAmount: 1000000,
                status: 'Active',
                currencyId: 'Cur' + i,
            };
        });

        return mockData;
    }

    getCustomers() {
        return Promise.resolve(this.getCustomerData());
    }

    getInvoices() {
        return Promise.resolve(this.getInvoiceData());
    }
}
