import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class CustomerService {
    constructor() {}

    getCustomerData() {
        const statuses = ['Active', 'Inactive', 'Pending', 'Suspended', 'VIP'];
        const addresses = ['HCM', '<PERSON><PERSON>', '<PERSON> Nang', '<PERSON> Tho', 'Hai Phong'];

        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                customerId: 'COD' + (i + 1).toString().padStart(3, '0'),
                customerName: 'Customer ' + (i + 1),
                phoneNumber: '0398765' + (400 + i).toString().padStart(3, '0'),
                email: 'customer' + (i + 1) + '@gmail.com',
                address: addresses[i % addresses.length],
                status: statuses[i % statuses.length],
                creditLimit: (i + 1) * 100000,
                currentBalance: 4900 + (i * 150),
                currencyId: 'VND',
            };
        });

        return mockData;
    }

    getInvoiceData() {
        const mockData = Array.from({ length: 11 }).map((_, i) => {
            return {
                id: i,
                invoiceId: 'Inv' + i,
                customerId: 'Customer' + i,
                invoiceDate: '02/02/2025',
                dueDate: '02/02/2025',
                totalAmount: 4900 + i,
                paidAmount: 2900 + i,
                remainingAmount: 1000000,
                status: 'Active',
                currencyId: 'Cur' + i,
            };
        });

        return mockData;
    }

    getCustomers() {
        return Promise.resolve(this.getCustomerData());
    }

    getInvoices() {
        return Promise.resolve(this.getInvoiceData());
    }
}
