{"ast": null, "code": "import { catchError, switchMap } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../service/auth.service\";\nexport class JwtRefreshInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      if (error.status === 401 && error.error && error.error.message === 'Token expired') {\n        // Token expired; attempt to refresh it\n        return this.authService.refreshToken().pipe(switchMap(() => {\n          // Retry the original request with the new token\n          const updatedRequest = request.clone({\n            setHeaders: {\n              Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`\n            }\n          });\n          return next.handle(updatedRequest);\n        }), catchError(() => {\n          // Refresh token failed; log out the user or handle the error\n          // For example, you can redirect to the login page\n          this.authService.logout();\n          return throwError('Token refresh failed');\n        }));\n      }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function JwtRefreshInterceptor_Factory(t) {\n    return new (t || JwtRefreshInterceptor)(i0.ɵɵinject(i1.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtRefreshInterceptor,\n    factory: JwtRefreshInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["catchError", "switchMap", "throwError", "<PERSON><PERSON>", "JwtRefreshInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "status", "message", "refreshToken", "updatedRequest", "clone", "setHeaders", "Authorization", "localStorage", "getItem", "ACCESS_TOKEN", "logout", "_", "i0", "ɵɵinject", "i1", "AuthService", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\jwt-refresh.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n  HttpErrorResponse,\n} from '@angular/common/http';\nimport { catchError, switchMap } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport { AuthService } from '../service/auth.service';\nimport { Auth } from '../enums/auth.enum';\n\n@Injectable()\nexport class JwtRefreshInterceptor implements HttpInterceptor {\n  constructor(private authService: AuthService) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler) {\n    return next.handle(request).pipe(\n      catchError((error: HttpErrorResponse) => {\n        if (error.status === 401 && error.error && error.error.message === 'Token expired') {\n          // Token expired; attempt to refresh it\n          return this.authService.refreshToken().pipe(\n            switchMap(() => {\n              // Retry the original request with the new token\n              const updatedRequest = request.clone({\n                setHeaders: {\n                  Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`,\n                },\n              });\n              return next.handle(updatedRequest);\n            }),\n            catchError(() => {\n              // Refresh token failed; log out the user or handle the error\n              // For example, you can redirect to the login page\n              this.authService.logout();\n              return throwError('Token refresh failed');\n            })\n          );\n        }\n        return throwError(error);\n      })\n    );\n  }\n}"], "mappings": "AAOA,SAASA,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,SAASC,UAAU,QAAQ,MAAM;AAEjC,SAASC,IAAI,QAAQ,oBAAoB;;;AAGzC,OAAM,MAAOC,qBAAqB;EAChCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9BX,UAAU,CAAEY,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACE,OAAO,KAAK,eAAe,EAAE;QAClF;QACA,OAAO,IAAI,CAACR,WAAW,CAACS,YAAY,EAAE,CAACJ,IAAI,CACzCV,SAAS,CAAC,MAAK;UACb;UACA,MAAMe,cAAc,GAAGR,OAAO,CAACS,KAAK,CAAC;YACnCC,UAAU,EAAE;cACVC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAClB,IAAI,CAACmB,YAAY,CAAC;;WAEnE,CAAC;UACF,OAAOb,IAAI,CAACC,MAAM,CAACM,cAAc,CAAC;QACpC,CAAC,CAAC,EACFhB,UAAU,CAAC,MAAK;UACd;UACA;UACA,IAAI,CAACM,WAAW,CAACiB,MAAM,EAAE;UACzB,OAAOrB,UAAU,CAAC,sBAAsB,CAAC;QAC3C,CAAC,CAAC,CACH;;MAEH,OAAOA,UAAU,CAACU,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAAC,QAAAY,CAAA,G;qBA7BUpB,qBAAqB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAArBzB,qBAAqB;IAAA0B,OAAA,EAArB1B,qBAAqB,CAAA2B;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}