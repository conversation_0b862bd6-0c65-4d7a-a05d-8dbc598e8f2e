{"ast": null, "code": "import { effect, signal } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LayoutService {\n  constructor() {\n    this._config = {\n      ripple: false,\n      inputStyle: 'outlined',\n      menuMode: 'static',\n      colorScheme: 'light',\n      theme: 'lara-light-indigo',\n      scale: 14\n    };\n    this.config = signal(this._config);\n    this.state = {\n      staticMenuDesktopInactive: false,\n      overlayMenuActive: false,\n      profileSidebarVisible: false,\n      configSidebarVisible: false,\n      staticMenuMobileActive: false,\n      menuHoverActive: false\n    };\n    this.configUpdate = new Subject();\n    this.overlayOpen = new Subject();\n    this.configUpdate$ = this.configUpdate.asObservable();\n    this.overlayOpen$ = this.overlayOpen.asObservable();\n    effect(() => {\n      const config = this.config();\n      if (this.updateStyle(config)) {\n        this.changeTheme();\n      }\n      this.changeScale(config.scale);\n      this.onConfigUpdate();\n    });\n  }\n  updateStyle(config) {\n    return config.theme !== this._config.theme || config.colorScheme !== this._config.colorScheme;\n  }\n  onMenuToggle() {\n    if (this.isOverlay()) {\n      this.state.overlayMenuActive = !this.state.overlayMenuActive;\n      if (this.state.overlayMenuActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n    if (this.isDesktop()) {\n      this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;\n    } else {\n      this.state.staticMenuMobileActive = !this.state.staticMenuMobileActive;\n      if (this.state.staticMenuMobileActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n  }\n  showProfileSidebar() {\n    this.state.profileSidebarVisible = !this.state.profileSidebarVisible;\n    if (this.state.profileSidebarVisible) {\n      this.overlayOpen.next(null);\n    }\n  }\n  showConfigSidebar() {\n    this.state.configSidebarVisible = true;\n  }\n  isOverlay() {\n    return this.config().menuMode === 'overlay';\n  }\n  isDesktop() {\n    return window.innerWidth > 991;\n  }\n  isMobile() {\n    return !this.isDesktop();\n  }\n  onConfigUpdate() {\n    this._config = {\n      ...this.config()\n    };\n    this.configUpdate.next(this.config());\n  }\n  changeTheme() {\n    const config = this.config();\n    const themeLink = document.getElementById('theme-css');\n    const themeLinkHref = themeLink.getAttribute('href');\n    const newHref = themeLinkHref.split('/').map(el => el == this._config.theme ? el = config.theme : el == `theme-${this._config.colorScheme}` ? el = `theme-${config.colorScheme}` : el).join('/');\n    this.replaceThemeLink(newHref);\n  }\n  replaceThemeLink(href) {\n    const id = 'theme-css';\n    let themeLink = document.getElementById(id);\n    const cloneLinkElement = themeLink.cloneNode(true);\n    cloneLinkElement.setAttribute('href', href);\n    cloneLinkElement.setAttribute('id', id + '-clone');\n    themeLink.parentNode.insertBefore(cloneLinkElement, themeLink.nextSibling);\n    cloneLinkElement.addEventListener('load', () => {\n      themeLink.remove();\n      cloneLinkElement.setAttribute('id', id);\n    });\n  }\n  changeScale(value) {\n    document.documentElement.style.fontSize = `${value}px`;\n  }\n  static #_ = this.ɵfac = function LayoutService_Factory(t) {\n    return new (t || LayoutService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LayoutService,\n    factory: LayoutService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["effect", "signal", "Subject", "LayoutService", "constructor", "_config", "ripple", "inputStyle", "menuMode", "colorScheme", "theme", "scale", "config", "state", "staticMenuDesktopInactive", "overlayMenuActive", "profileSidebarVisible", "configSidebarVisible", "staticMenuMobileActive", "menuHoverActive", "configUpdate", "overlayOpen", "configUpdate$", "asObservable", "overlayOpen$", "updateStyle", "changeTheme", "changeScale", "onConfigUpdate", "onMenuToggle", "isOverlay", "next", "isDesktop", "showProfileSidebar", "showConfigSidebar", "window", "innerWidth", "isMobile", "themeLink", "document", "getElementById", "themeLinkHref", "getAttribute", "newHref", "split", "map", "el", "join", "replaceThemeLink", "href", "id", "cloneLinkElement", "cloneNode", "setAttribute", "parentNode", "insertBefore", "nextS<PERSON>ling", "addEventListener", "remove", "value", "documentElement", "style", "fontSize", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.layout.service.ts"], "sourcesContent": ["import { Injectable, effect, signal } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\nexport interface AppConfig {\r\n    inputStyle: string;\r\n    colorScheme: string;\r\n    theme: string;\r\n    ripple: boolean;\r\n    menuMode: string;\r\n    scale: number;\r\n}\r\n\r\ninterface LayoutState {\r\n    staticMenuDesktopInactive: boolean;\r\n    overlayMenuActive: boolean;\r\n    profileSidebarVisible: boolean;\r\n    configSidebarVisible: boolean;\r\n    staticMenuMobileActive: boolean;\r\n    menuHoverActive: boolean;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class LayoutService {\r\n    _config: AppConfig = {\r\n        ripple: false,\r\n        inputStyle: 'outlined',\r\n        menuMode: 'static',\r\n        colorScheme: 'light',\r\n        theme: 'lara-light-indigo',\r\n        scale: 14,\r\n    };\r\n\r\n    config = signal<AppConfig>(this._config);\r\n\r\n    state: LayoutState = {\r\n        staticMenuDesktopInactive: false,\r\n        overlayMenuActive: false,\r\n        profileSidebarVisible: false,\r\n        configSidebarVisible: false,\r\n        staticMenuMobileActive: false,\r\n        menuHoverActive: false,\r\n    };\r\n\r\n    private configUpdate = new Subject<AppConfig>();\r\n\r\n    private overlayOpen = new Subject<any>();\r\n\r\n    configUpdate$ = this.configUpdate.asObservable();\r\n\r\n    overlayOpen$ = this.overlayOpen.asObservable();\r\n\r\n    constructor() {\r\n        effect(() => {\r\n            const config = this.config();\r\n            if (this.updateStyle(config)) {\r\n                this.changeTheme();\r\n            }\r\n            this.changeScale(config.scale);\r\n            this.onConfigUpdate();\r\n        });\r\n    }\r\n\r\n    updateStyle(config: AppConfig) {\r\n        return (\r\n            config.theme !== this._config.theme ||\r\n            config.colorScheme !== this._config.colorScheme\r\n        );\r\n    }\r\n\r\n    onMenuToggle() {\r\n        if (this.isOverlay()) {\r\n            this.state.overlayMenuActive = !this.state.overlayMenuActive;\r\n            if (this.state.overlayMenuActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n\r\n        if (this.isDesktop()) {\r\n            this.state.staticMenuDesktopInactive =\r\n                !this.state.staticMenuDesktopInactive;\r\n        } else {\r\n            this.state.staticMenuMobileActive =\r\n                !this.state.staticMenuMobileActive;\r\n\r\n            if (this.state.staticMenuMobileActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n    }\r\n\r\n    showProfileSidebar() {\r\n        this.state.profileSidebarVisible = !this.state.profileSidebarVisible;\r\n        if (this.state.profileSidebarVisible) {\r\n            this.overlayOpen.next(null);\r\n        }\r\n    }\r\n\r\n    showConfigSidebar() {\r\n        this.state.configSidebarVisible = true;\r\n    }\r\n\r\n    isOverlay() {\r\n        return this.config().menuMode === 'overlay';\r\n    }\r\n\r\n    isDesktop() {\r\n        return window.innerWidth > 991;\r\n    }\r\n\r\n    isMobile() {\r\n        return !this.isDesktop();\r\n    }\r\n\r\n    onConfigUpdate() {\r\n        this._config = { ...this.config() };\r\n        this.configUpdate.next(this.config());\r\n    }\r\n\r\n    changeTheme() {\r\n        const config = this.config();\r\n        const themeLink = <HTMLLinkElement>document.getElementById('theme-css');\r\n        const themeLinkHref = themeLink.getAttribute('href')!;\r\n        const newHref = themeLinkHref\r\n            .split('/')\r\n            .map((el) =>\r\n                el == this._config.theme\r\n                    ? (el = config.theme)\r\n                    : el == `theme-${this._config.colorScheme}`\r\n                    ? (el = `theme-${config.colorScheme}`)\r\n                    : el\r\n            )\r\n            .join('/');\r\n\r\n        this.replaceThemeLink(newHref);\r\n    }\r\n    replaceThemeLink(href: string) {\r\n        const id = 'theme-css';\r\n        let themeLink = <HTMLLinkElement>document.getElementById(id);\r\n        const cloneLinkElement = <HTMLLinkElement>themeLink.cloneNode(true);\r\n\r\n        cloneLinkElement.setAttribute('href', href);\r\n        cloneLinkElement.setAttribute('id', id + '-clone');\r\n\r\n        themeLink.parentNode!.insertBefore(\r\n            cloneLinkElement,\r\n            themeLink.nextSibling\r\n        );\r\n        cloneLinkElement.addEventListener('load', () => {\r\n            themeLink.remove();\r\n            cloneLinkElement.setAttribute('id', id);\r\n        });\r\n    }\r\n\r\n    changeScale(value: number) {\r\n        document.documentElement.style.fontSize = `${value}px`;\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAC1D,SAASC,OAAO,QAAQ,MAAM;;AAuB9B,OAAM,MAAOC,aAAa;EA6BtBC,YAAA;IA5BA,KAAAC,OAAO,GAAc;MACjBC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,OAAO;MACpBC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE;KACV;IAED,KAAAC,MAAM,GAAGX,MAAM,CAAY,IAAI,CAACI,OAAO,CAAC;IAExC,KAAAQ,KAAK,GAAgB;MACjBC,yBAAyB,EAAE,KAAK;MAChCC,iBAAiB,EAAE,KAAK;MACxBC,qBAAqB,EAAE,KAAK;MAC5BC,oBAAoB,EAAE,KAAK;MAC3BC,sBAAsB,EAAE,KAAK;MAC7BC,eAAe,EAAE;KACpB;IAEO,KAAAC,YAAY,GAAG,IAAIlB,OAAO,EAAa;IAEvC,KAAAmB,WAAW,GAAG,IAAInB,OAAO,EAAO;IAExC,KAAAoB,aAAa,GAAG,IAAI,CAACF,YAAY,CAACG,YAAY,EAAE;IAEhD,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;IAG1CvB,MAAM,CAAC,MAAK;MACR,MAAMY,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACa,WAAW,CAACb,MAAM,CAAC,EAAE;QAC1B,IAAI,CAACc,WAAW,EAAE;;MAEtB,IAAI,CAACC,WAAW,CAACf,MAAM,CAACD,KAAK,CAAC;MAC9B,IAAI,CAACiB,cAAc,EAAE;IACzB,CAAC,CAAC;EACN;EAEAH,WAAWA,CAACb,MAAiB;IACzB,OACIA,MAAM,CAACF,KAAK,KAAK,IAAI,CAACL,OAAO,CAACK,KAAK,IACnCE,MAAM,CAACH,WAAW,KAAK,IAAI,CAACJ,OAAO,CAACI,WAAW;EAEvD;EAEAoB,YAAYA,CAAA;IACR,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MAClB,IAAI,CAACjB,KAAK,CAACE,iBAAiB,GAAG,CAAC,IAAI,CAACF,KAAK,CAACE,iBAAiB;MAC5D,IAAI,IAAI,CAACF,KAAK,CAACE,iBAAiB,EAAE;QAC9B,IAAI,CAACM,WAAW,CAACU,IAAI,CAAC,IAAI,CAAC;;;IAInC,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MAClB,IAAI,CAACnB,KAAK,CAACC,yBAAyB,GAChC,CAAC,IAAI,CAACD,KAAK,CAACC,yBAAyB;KAC5C,MAAM;MACH,IAAI,CAACD,KAAK,CAACK,sBAAsB,GAC7B,CAAC,IAAI,CAACL,KAAK,CAACK,sBAAsB;MAEtC,IAAI,IAAI,CAACL,KAAK,CAACK,sBAAsB,EAAE;QACnC,IAAI,CAACG,WAAW,CAACU,IAAI,CAAC,IAAI,CAAC;;;EAGvC;EAEAE,kBAAkBA,CAAA;IACd,IAAI,CAACpB,KAAK,CAACG,qBAAqB,GAAG,CAAC,IAAI,CAACH,KAAK,CAACG,qBAAqB;IACpE,IAAI,IAAI,CAACH,KAAK,CAACG,qBAAqB,EAAE;MAClC,IAAI,CAACK,WAAW,CAACU,IAAI,CAAC,IAAI,CAAC;;EAEnC;EAEAG,iBAAiBA,CAAA;IACb,IAAI,CAACrB,KAAK,CAACI,oBAAoB,GAAG,IAAI;EAC1C;EAEAa,SAASA,CAAA;IACL,OAAO,IAAI,CAAClB,MAAM,EAAE,CAACJ,QAAQ,KAAK,SAAS;EAC/C;EAEAwB,SAASA,CAAA;IACL,OAAOG,MAAM,CAACC,UAAU,GAAG,GAAG;EAClC;EAEAC,QAAQA,CAAA;IACJ,OAAO,CAAC,IAAI,CAACL,SAAS,EAAE;EAC5B;EAEAJ,cAAcA,CAAA;IACV,IAAI,CAACvB,OAAO,GAAG;MAAE,GAAG,IAAI,CAACO,MAAM;IAAE,CAAE;IACnC,IAAI,CAACQ,YAAY,CAACW,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE,CAAC;EACzC;EAEAc,WAAWA,CAAA;IACP,MAAMd,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;IAC5B,MAAM0B,SAAS,GAAoBC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;IACvE,MAAMC,aAAa,GAAGH,SAAS,CAACI,YAAY,CAAC,MAAM,CAAE;IACrD,MAAMC,OAAO,GAAGF,aAAa,CACxBG,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,EAAE,IACJA,EAAE,IAAI,IAAI,CAACzC,OAAO,CAACK,KAAK,GACjBoC,EAAE,GAAGlC,MAAM,CAACF,KAAK,GAClBoC,EAAE,IAAI,SAAS,IAAI,CAACzC,OAAO,CAACI,WAAW,EAAE,GACxCqC,EAAE,GAAG,SAASlC,MAAM,CAACH,WAAW,EAAE,GACnCqC,EAAE,CACX,CACAC,IAAI,CAAC,GAAG,CAAC;IAEd,IAAI,CAACC,gBAAgB,CAACL,OAAO,CAAC;EAClC;EACAK,gBAAgBA,CAACC,IAAY;IACzB,MAAMC,EAAE,GAAG,WAAW;IACtB,IAAIZ,SAAS,GAAoBC,QAAQ,CAACC,cAAc,CAACU,EAAE,CAAC;IAC5D,MAAMC,gBAAgB,GAAoBb,SAAS,CAACc,SAAS,CAAC,IAAI,CAAC;IAEnED,gBAAgB,CAACE,YAAY,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAC3CE,gBAAgB,CAACE,YAAY,CAAC,IAAI,EAAEH,EAAE,GAAG,QAAQ,CAAC;IAElDZ,SAAS,CAACgB,UAAW,CAACC,YAAY,CAC9BJ,gBAAgB,EAChBb,SAAS,CAACkB,WAAW,CACxB;IACDL,gBAAgB,CAACM,gBAAgB,CAAC,MAAM,EAAE,MAAK;MAC3CnB,SAAS,CAACoB,MAAM,EAAE;MAClBP,gBAAgB,CAACE,YAAY,CAAC,IAAI,EAAEH,EAAE,CAAC;IAC3C,CAAC,CAAC;EACN;EAEAvB,WAAWA,CAACgC,KAAa;IACrBpB,QAAQ,CAACqB,eAAe,CAACC,KAAK,CAACC,QAAQ,GAAG,GAAGH,KAAK,IAAI;EAC1D;EAAC,QAAAI,CAAA,G;qBArIQ5D,aAAa;EAAA;EAAA,QAAA6D,EAAA,G;WAAb7D,aAAa;IAAA8D,OAAA,EAAb9D,aAAa,CAAA+D,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}