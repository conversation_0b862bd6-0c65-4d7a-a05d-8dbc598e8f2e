import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { FileUploadModule } from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { KeyFilterModule } from 'primeng/keyfilter';
import { SkeletonModule } from 'primeng/skeleton';
import { ToastModule } from 'primeng/toast';
import { ToolbarModule } from 'primeng/toolbar';
import { CustomerComponent } from './customer/customer.component';
import { DashboardsRoutingModule } from './dashboard-routing.module';
import { InvoiceComponent } from './invoice/invoice.component';
import { UserComponent } from './user/user.component';
import { ExexCommonModule } from '@app/core/components/common/exex-common.module';

@NgModule({
    declarations: [CustomerComponent, UserComponent, InvoiceComponent],
    imports: [
        CommonModule,
        ExexCommonModule,
        DashboardsRoutingModule,
        FormsModule,
        ReactiveFormsModule,
        ToastModule,
        ToolbarModule,
        DialogModule,
        ConfirmDialogModule,
        InputTextModule,
        FileUploadModule,
        InputTextareaModule,
        KeyFilterModule,
        AvatarModule,
        SkeletonModule,
        ButtonModule,
    ],
})
export class DashboardModule {}
