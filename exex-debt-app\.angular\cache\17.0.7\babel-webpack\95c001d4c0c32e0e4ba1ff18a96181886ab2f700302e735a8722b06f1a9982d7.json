{"ast": null, "code": "import { MENU_LIST } from './menu-list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./app.menuitem.component\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const item_r1 = ctx_r4.$implicit;\n    const i_r2 = ctx_r4.index;\n    i0.ɵɵproperty(\"item\", item_r1)(\"index\", i_r2)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r1.separator);\n  }\n}\nexport class AppMenuComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n    this.model = [];\n  }\n  ngOnInit() {\n    this.model = MENU_LIST;\n  }\n  static #_ = this.ɵfac = function AppMenuComponent_Factory(t) {\n    return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppMenuComponent,\n    selectors: [[\"app-menu\"]],\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"]],\n    template: function AppMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ul\", 0);\n        i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.AppMenuitemComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MENU_LIST", "i0", "ɵɵelement", "ɵɵproperty", "item_r1", "i_r2", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_ng_container_1_li_1_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "separator", "AppMenuComponent", "constructor", "layoutService", "model", "ngOnInit", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "AppMenuComponent_ng_container_1_Template", "ɵɵelementEnd"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.menu.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { LayoutService } from './app.layout.service';\r\nimport { MENU_LIST } from './menu-list';\r\n\r\n@Component({\r\n    selector: 'app-menu',\r\n    template: `<ul class=\"layout-menu\">\r\n        <ng-container *ngFor=\"let item of model; let i = index\">\r\n            <li app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n        </ng-container>\r\n    </ul>`,\r\n})\r\nexport class AppMenuComponent implements OnInit {\r\n    model: any[] = [];\r\n\r\n    constructor(public layoutService: LayoutService) {}\r\n\r\n    ngOnInit() {\r\n        this.model = MENU_LIST;\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,SAAS,QAAQ,aAAa;;;;;;;IAM3BC,EAAA,CAAAC,SAAA,YAAsF;;;;;;IAA7CD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA;;;;;IAD1DJ,EAAA,CAAAK,uBAAA,GAAwD;IACpDL,EAAA,CAAAM,UAAA,IAAAC,6CAAA,gBAAsF;IAC1FP,EAAA,CAAAQ,qBAAA,EAAe;;;;IADOR,EAAA,CAAAS,SAAA,GAAqB;IAArBT,EAAA,CAAAE,UAAA,UAAAC,OAAA,CAAAO,SAAA,CAAqB;;;AAInD,OAAM,MAAOC,gBAAgB;EAGzBC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAFhC,KAAAC,KAAK,GAAU,EAAE;EAEiC;EAElDC,QAAQA,CAAA;IACJ,IAAI,CAACD,KAAK,GAAGf,SAAS;EAC1B;EAAC,QAAAiB,CAAA,G;qBAPQL,gBAAgB,EAAAX,EAAA,CAAAiB,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBT,gBAAgB;IAAAU,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QANd3B,EAAA,CAAA6B,cAAA,YAAwB;QAC/B7B,EAAA,CAAAM,UAAA,IAAAwB,wCAAA,0BAEe;QACnB9B,EAAA,CAAA+B,YAAA,EAAK;;;QAH8B/B,EAAA,CAAAS,SAAA,GAAU;QAAVT,EAAA,CAAAE,UAAA,YAAA0B,GAAA,CAAAd,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}