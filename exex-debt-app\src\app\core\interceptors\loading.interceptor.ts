import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs/operators';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
    constructor(private spinner: NgxSpinnerService) {}

    intercept(request: HttpRequest<any>, next: HttpHandler) {
        this.spinner.show();
        return next.handle(request).pipe(
            finalize(() => {
                this.spinner.hide();
            }),
        );
    }
}
