import { AuthService } from '@app/core/services/auth.service';
import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Path } from '../enums/path.enum';

export const authGuard: CanActivateFn = (route, state) => {
    const isAuth = inject(AuthService).isAuthenticated();
    const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);
    return isAuth ? true : redirectToLogin;
};
