{"ast": null, "code": "import { Path } from \"../core/enums/path.enum\";\nexport const MENU_LIST = [{\n  label: 'Dashboard',\n  items: [{\n    label: 'User',\n    icon: 'pi pi-fw pi-user',\n    routerLink: [Path.DASHBOARD_USER]\n  }, {\n    label: 'Submenu 1',\n    icon: 'pi pi-fw pi-bookmark',\n    items: [{\n      label: 'Submenu 1.1',\n      icon: 'pi pi-fw pi-bookmark',\n      items: [{\n        label: 'Submenu 1.1.1',\n        icon: 'pi pi-fw pi-bookmark',\n        routerLink: ['/uikit/misc']\n      }, {\n        label: 'Submenu 1.1.2',\n        icon: 'pi pi-fw pi-bookmark'\n      }, {\n        label: 'Submenu 1.1.3',\n        icon: 'pi pi-fw pi-bookmark'\n      }]\n    }, {\n      label: 'Submenu 1.2',\n      icon: 'pi pi-fw pi-bookmark',\n      items: [{\n        label: 'Submenu 1.2.1',\n        icon: 'pi pi-fw pi-bookmark'\n      }]\n    }]\n  }]\n}];", "map": {"version": 3, "names": ["Path", "MENU_LIST", "label", "items", "icon", "routerLink", "DASHBOARD_USER"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\menu-list.ts"], "sourcesContent": ["import { Path } from \"../core/enums/path.enum\";\r\n\r\nexport const MENU_LIST = [\r\n    {\r\n        label: 'Dashboard',\r\n        items: [\r\n            {\r\n                label: 'User',\r\n                icon: 'pi pi-fw pi-user',\r\n                routerLink: [Path.DASHBOARD_USER],\r\n            },\r\n            {\r\n                label: 'Submenu 1',\r\n                icon: 'pi pi-fw pi-bookmark',\r\n                items: [\r\n                    {\r\n                        label: 'Submenu 1.1',\r\n                        icon: 'pi pi-fw pi-bookmark',\r\n                        items: [\r\n                            {\r\n                                label: 'Submenu 1.1.1',\r\n                                icon: 'pi pi-fw pi-bookmark',\r\n                                routerLink: ['/uikit/misc'],\r\n                            },\r\n                            {\r\n                                label: 'Submenu 1.1.2',\r\n                                icon: 'pi pi-fw pi-bookmark',\r\n                            },\r\n                            {\r\n                                label: 'Submenu 1.1.3',\r\n                                icon: 'pi pi-fw pi-bookmark',\r\n                            },\r\n                        ],\r\n                    },\r\n                    {\r\n                        label: 'Submenu 1.2',\r\n                        icon: 'pi pi-fw pi-bookmark',\r\n                        items: [\r\n                            {\r\n                                label: 'Submenu 1.2.1',\r\n                                icon: 'pi pi-fw pi-bookmark',\r\n                            },\r\n                        ],\r\n                    },\r\n                ],\r\n            },\r\n        ],\r\n    },\r\n];\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAyB;AAE9C,OAAO,MAAMC,SAAS,GAAG,CACrB;EACIC,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,CACH;IACID,KAAK,EAAE,MAAM;IACbE,IAAI,EAAE,kBAAkB;IACxBC,UAAU,EAAE,CAACL,IAAI,CAACM,cAAc;GACnC,EACD;IACIJ,KAAK,EAAE,WAAW;IAClBE,IAAI,EAAE,sBAAsB;IAC5BD,KAAK,EAAE,CACH;MACID,KAAK,EAAE,aAAa;MACpBE,IAAI,EAAE,sBAAsB;MAC5BD,KAAK,EAAE,CACH;QACID,KAAK,EAAE,eAAe;QACtBE,IAAI,EAAE,sBAAsB;QAC5BC,UAAU,EAAE,CAAC,aAAa;OAC7B,EACD;QACIH,KAAK,EAAE,eAAe;QACtBE,IAAI,EAAE;OACT,EACD;QACIF,KAAK,EAAE,eAAe;QACtBE,IAAI,EAAE;OACT;KAER,EACD;MACIF,KAAK,EAAE,aAAa;MACpBE,IAAI,EAAE,sBAAsB;MAC5BD,KAAK,EAAE,CACH;QACID,KAAK,EAAE,eAAe;QACtBE,IAAI,EAAE;OACT;KAER;GAER;CAER,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}