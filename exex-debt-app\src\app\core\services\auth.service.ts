import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, tap } from 'rxjs';
import { Auth } from '../enums/auth.enum';
import { Path } from '../enums/path.enum';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    private jwtToken;
    redirectTo = inject(Router);

    constructor(
        private http: HttpClient,
        private spinner: NgxSpinnerService,
    ) {
        const token: any = localStorage.getItem(Auth.ACCESS_TOKEN);
        if (token) {
            this.jwtToken = token;
        }
    }

    refreshToken(): Observable<any> {
        const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);
        return this.http.post<any>(`/refresh`, { refreshToken }).pipe(
            tap((response) => {
                localStorage.setItem(Auth.ACCESS_TOKEN, response.access_token);
            }),
        );
    }

    public isAuthenticated(): boolean {
        return this.jwtToken != null;
    }

    redirectToDashboard() {
        this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);
        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
    }

    login() {
        this.spinner.show();
        this.jwtToken = 'ponynguyen';
        localStorage.setItem(Auth.ACCESS_TOKEN, this.jwtToken);
        this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
        setTimeout(() => {
            this.spinner.hide();
        }, 1000);
    }

    logout() {
        this.jwtToken = null;
        localStorage.removeItem(Auth.ACCESS_TOKEN);
        localStorage.removeItem(Auth.REFRESH_TOKEN);
        this.redirectTo.navigate([Path.AUTH_LOGIN]);
    }

    mockUser(): Observable<any> {
        return this.http.get('https://jsonplaceholder.typicode.com/users');
    }
}
