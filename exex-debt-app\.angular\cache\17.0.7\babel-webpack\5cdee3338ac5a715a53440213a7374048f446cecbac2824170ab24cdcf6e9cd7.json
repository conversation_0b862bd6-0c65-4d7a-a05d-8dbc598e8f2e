{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DashboardsRoutingModule } from './dashboard-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class DashboardModule {\n  static #_ = this.ɵfac = function DashboardModule_Factory(t) {\n    return new (t || DashboardModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, DashboardsRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    imports: [CommonModule, DashboardsRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DashboardsRoutingModule", "DashboardModule", "_", "_2", "_3", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { DashboardsRoutingModule } from './dashboard-routing.module';\r\n\r\n@NgModule({\r\n    imports: [CommonModule, DashboardsRoutingModule],\r\n})\r\nexport class DashboardModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,4BAA4B;;AAKpE,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAFdL,YAAY,EAAEC,uBAAuB;EAAA;;;2EAEtCC,eAAe;IAAAI,OAAA,GAFdN,YAAY,EAAEC,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}