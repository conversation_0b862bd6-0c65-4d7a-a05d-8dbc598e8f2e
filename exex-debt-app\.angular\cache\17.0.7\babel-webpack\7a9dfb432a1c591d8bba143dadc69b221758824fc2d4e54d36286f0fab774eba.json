{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nexport class ExexCommonService {\n  constructor(messageService, confirmationService) {\n    this.messageService = messageService;\n    this.confirmationService = confirmationService;\n  }\n  showToastSuccess(detail) {\n    this.messageService.add({\n      severity: 'success',\n      summary: 'Successful',\n      detail\n    });\n  }\n  showToastError(detail) {\n    this.messageService.add({\n      severity: 'error',\n      summary: 'Error',\n      detail\n    });\n  }\n  showToastInfo(detail) {\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Info',\n      detail\n    });\n  }\n  showToastWarning(detail) {\n    this.messageService.add({\n      severity: 'warn',\n      summary: 'Warn',\n      detail\n    });\n  }\n  showDialogConfirm(acceptCallback, rejectCallback) {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to proceed with this action?',\n      header: 'Confirmation',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        if (acceptCallback) acceptCallback();\n      },\n      reject: () => {\n        if (rejectCallback) rejectCallback();\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ExexCommonService_Factory(t) {\n    return new (t || ExexCommonService)(i0.ɵɵinject(i1.MessageService), i0.ɵɵinject(i1.ConfirmationService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ExexCommonService,\n    factory: ExexCommonService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["ExexCommonService", "constructor", "messageService", "confirmationService", "showToastSuccess", "detail", "add", "severity", "summary", "showToastError", "showToastInfo", "showToastWarning", "showDialogConfirm", "acceptCallback", "<PERSON><PERSON><PERSON><PERSON>", "confirm", "message", "header", "icon", "accept", "reject", "_", "i0", "ɵɵinject", "i1", "MessageService", "ConfirmationService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\exex-common.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class ExexCommonService {\r\n    constructor(\r\n        private messageService: MessageService,\r\n        private confirmationService: ConfirmationService,\r\n    ) {}\r\n\r\n    showToastSuccess(detail: string) {\r\n        this.messageService.add({ severity: 'success', summary: 'Successful', detail });\r\n    }\r\n\r\n    showToastError(detail: string) {\r\n        this.messageService.add({ severity: 'error', summary: 'Error', detail });\r\n    }\r\n\r\n    showToastInfo(detail: string) {\r\n        this.messageService.add({ severity: 'info', summary: 'Info', detail });\r\n    }\r\n\r\n    showToastWarning(detail: string) {\r\n        this.messageService.add({ severity: 'warn', summary: 'Warn', detail });\r\n    }\r\n\r\n    showDialogConfirm(acceptCallback?: () => void, rejectCallback?: () => void) {\r\n        this.confirmationService.confirm({\r\n            message: 'Are you sure you want to proceed with this action?',\r\n            header: 'Confirmation',\r\n            icon: 'pi pi-exclamation-triangle',\r\n            accept: () => {\r\n                if (acceptCallback) acceptCallback();\r\n            },\r\n            reject: () => {\r\n                if (rejectCallback) rejectCallback();\r\n            },\r\n        });\r\n    }\r\n}\r\n"], "mappings": ";;AAMA,OAAM,MAAOA,iBAAiB;EAC1BC,YACYC,cAA8B,EAC9BC,mBAAwC;IADxC,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;EAC5B;EAEHC,gBAAgBA,CAACC,MAAc;IAC3B,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAEC,OAAO,EAAE,YAAY;MAAEH;IAAM,CAAE,CAAC;EACnF;EAEAI,cAAcA,CAACJ,MAAc;IACzB,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC;MAAEC,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE,OAAO;MAAEH;IAAM,CAAE,CAAC;EAC5E;EAEAK,aAAaA,CAACL,MAAc;IACxB,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAM,gBAAgBA,CAACN,MAAc;IAC3B,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC;MAAEC,QAAQ,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAO,iBAAiBA,CAACC,cAA2B,EAAEC,cAA2B;IACtE,IAAI,CAACX,mBAAmB,CAACY,OAAO,CAAC;MAC7BC,OAAO,EAAE,oDAAoD;MAC7DC,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACT,IAAIN,cAAc,EAAEA,cAAc,EAAE;MACxC,CAAC;MACDO,MAAM,EAAEA,CAAA,KAAK;QACT,IAAIN,cAAc,EAAEA,cAAc,EAAE;MACxC;KACH,CAAC;EACN;EAAC,QAAAO,CAAA,G;qBAlCQrB,iBAAiB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAE,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjB3B,iBAAiB;IAAA4B,OAAA,EAAjB5B,iBAAiB,CAAA6B,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}