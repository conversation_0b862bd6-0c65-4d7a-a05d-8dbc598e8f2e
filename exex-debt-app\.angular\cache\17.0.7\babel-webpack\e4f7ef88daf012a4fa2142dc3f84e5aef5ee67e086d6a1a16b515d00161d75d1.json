{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { AppConfigComponent } from './app.config.component';\nlet AppConfigModule = class AppConfigModule {};\nAppConfigModule = __decorate([NgModule({\n  imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule],\n  declarations: [AppConfigComponent],\n  exports: [AppConfigComponent]\n})], AppConfigModule);\nexport { AppConfigModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "SidebarModule", "RadioButtonModule", "ButtonModule", "InputSwitchModule", "AppConfigComponent", "AppConfigModule", "__decorate", "imports", "declarations", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\config\\config.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { AppConfigComponent } from './app.config.component';\r\n\r\n@NgModule({\r\n    imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule],\r\n    declarations: [AppConfigComponent],\r\n    exports: [AppConfigComponent],\r\n})\r\nexport class AppConfigModule {}\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,kBAAkB,QAAQ,wBAAwB;AAOpD,IAAMC,eAAe,GAArB,MAAMA,eAAe,GAAG;AAAlBA,eAAe,GAAAC,UAAA,EAL3BT,QAAQ,CAAC;EACNU,OAAO,EAAE,CAACT,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,iBAAiB,CAAC;EACvGK,YAAY,EAAE,CAACJ,kBAAkB,CAAC;EAClCK,OAAO,EAAE,CAACL,kBAAkB;CAC/B,CAAC,C,EACWC,eAAe,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}