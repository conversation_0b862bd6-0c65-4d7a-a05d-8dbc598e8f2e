{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class LoginRoutingModule {\n  static #_ = this.ɵfac = function LoginRoutingModule_Factory(t) {\n    return new (t || LoginRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LoginRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: '',\n      component: LoginComponent\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "LoginRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { LoginComponent } from './login.component';\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild([{ path: '', component: LoginComponent }])],\r\n    exports: [RouterModule],\r\n})\r\nexport class LoginRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;;;AAMlD,OAAM,MAAOC,kBAAkB;EAAA,QAAAC,CAAA,G;qBAAlBD,kBAAkB;EAAA;EAAA,QAAAE,EAAA,G;UAAlBF;EAAkB;EAAA,QAAAG,EAAA,G;cAHjBL,YAAY,CAACM,QAAQ,CAAC,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAEP;IAAc,CAAE,CAAC,CAAC,EAChED,YAAY;EAAA;;;2EAEbE,kBAAkB;IAAAO,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFjBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}