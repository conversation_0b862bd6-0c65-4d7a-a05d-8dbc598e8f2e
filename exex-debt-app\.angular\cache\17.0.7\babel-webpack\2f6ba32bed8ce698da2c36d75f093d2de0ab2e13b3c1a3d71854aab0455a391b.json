{"ast": null, "code": "export var Auth;\n(function (Auth) {\n  Auth[\"ACCESS_TOKEN\"] = \"access_token\";\n  Auth[\"REFRESH_TOKEN\"] = \"refresh_token\";\n})(Auth || (Auth = {}));", "map": {"version": 3, "names": ["<PERSON><PERSON>"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\enums\\auth.enum.ts"], "sourcesContent": ["export enum Auth {\r\n    ACCESS_TOKEN = 'access_token',\r\n    REFRESH_TOKEN = 'refresh_token'\r\n}\r\n"], "mappings": "AAAA,WAAYA,IAGX;AAHD,WAAYA,IAAI;EACZA,IAAA,iCAA6B;EAC7BA,IAAA,mCAA+B;AACnC,CAAC,EAHWA,IAAI,KAAJA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}