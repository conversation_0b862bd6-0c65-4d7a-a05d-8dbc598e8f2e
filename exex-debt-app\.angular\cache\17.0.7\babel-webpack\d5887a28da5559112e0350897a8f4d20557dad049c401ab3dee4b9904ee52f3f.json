{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\nimport { inject } from '@angular/core';\nimport { catchError } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ErrorInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      // Handle and log the error here\n      console.error('HTTP Error:', error);\n      // Optionally rethrow the error to propagate it\n      // if (error) {\n      //     if (error.status === 401 || error.status === 403) {\n      //         this.authService.logout();\n      //     }\n      //     if (error.status === 429) {\n      //         alert(\n      //             'Something went wrong, please try again after a few seconds!'\n      //         );\n      //     }\n      // }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthService", "inject", "catchError", "throwError", "ErrorInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "console", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\error.interceptor.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class ErrorInterceptor implements HttpInterceptor {\r\n    authService = inject(AuthService);\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        return next.handle(request).pipe(\r\n            catchError((error: HttpErrorResponse) => {\r\n                // Handle and log the error here\r\n                console.error('HTTP Error:', error);\r\n                // Optionally rethrow the error to propagate it\r\n\r\n                // if (error) {\r\n                //     if (error.status === 401 || error.status === 403) {\r\n                //         this.authService.logout();\r\n                //     }\r\n\r\n                //     if (error.status === 429) {\r\n                //         alert(\r\n                //             'Something went wrong, please try again after a few seconds!'\r\n                //         );\r\n                //     }\r\n                // }\r\n\r\n                return throwError(error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,MAAM,QAAoB,eAAe;AAElD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,UAAU,QAAQ,MAAM;;AAGjC,OAAM,MAAOC,gBAAgB;EAD7BC,YAAA;IAEI,KAAAC,WAAW,GAAGL,MAAM,CAACD,WAAW,CAAC;;EACjCO,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BT,UAAU,CAAEU,KAAwB,IAAI;MACpC;MACAC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC;MAEA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA,OAAOT,UAAU,CAACS,KAAK,CAAC;IAC5B,CAAC,CAAC,CACL;EACL;EAAC,QAAAE,CAAA,G;qBAxBQV,gBAAgB;EAAA;EAAA,QAAAW,EAAA,G;WAAhBX,gBAAgB;IAAAY,OAAA,EAAhBZ,gBAAgB,CAAAa;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}