{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/app.layout.service\";\nimport * as i2 from \"src/app/core/service/auth.service\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/checkbox\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/password\";\nexport class LoginComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.valCheck = ['remember'];\n    this.authService.redirectToDashboard();\n  }\n  login() {\n    this.authService.login();\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 22,\n    vars: 3,\n    consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", 2, \"border-radius\", \"53px\"], [1, \"text-center\", \"mb-5\"], [1, \"text-900\", \"text-3xl\", \"font-medium\", \"mb-3\"], [1, \"text-600\", \"font-medium\"], [\"for\", \"email1\", 1, \"block\", \"text-900\", \"text-xl\", \"font-medium\", \"mb-2\"], [\"id\", \"email1\", \"type\", \"text\", \"placeholder\", \"Email address\", \"pInputText\", \"\", 1, \"w-full\", \"md:w-30rem\", \"mb-5\", 2, \"padding\", \"1rem\"], [\"for\", \"password1\", 1, \"block\", \"text-900\", \"font-medium\", \"text-xl\", \"mb-2\"], [\"id\", \"password1\", \"placeholder\", \"Password\", \"styleClass\", \"mb-5\", \"inputStyleClass\", \"w-full p-3 md:w-30rem\", 3, \"ngModel\", \"toggleMask\", \"ngModelChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-5\", \"gap-5\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"rememberme1\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"rememberme1\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Sign In\", 1, \"w-full\", \"p-3\", \"text-xl\", 3, \"click\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵtext(6, \"Welcome, exex-debt!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"span\", 6);\n        i0.ɵɵtext(8, \"Sign in to continue\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\")(10, \"label\", 7);\n        i0.ɵɵtext(11, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(12, \"input\", 8);\n        i0.ɵɵelementStart(13, \"label\", 9);\n        i0.ɵɵtext(14, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"p-password\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_p_password_ngModelChange_15_listener($event) {\n          return ctx.password = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12);\n        i0.ɵɵelement(18, \"p-checkbox\", 13);\n        i0.ɵɵelementStart(19, \"label\", 14);\n        i0.ɵɵtext(20, \"Remember me\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_21_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(15);\n        i0.ɵɵproperty(\"ngModel\", ctx.password)(\"toggleMask\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"binary\", true);\n      }\n    },\n    dependencies: [i3.ButtonDirective, i4.Checkbox, i5.InputText, i6.NgControlStatus, i6.NgModel, i7.Password],\n    styles: [\"[_nghost-%COMP%]     .pi-eye, [_nghost-%COMP%]     .pi-eye-slash {\\n  transform: scale(1.6);\\n  margin-right: 1rem;\\n  color: var(--primary-color) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2F1dGgvbG9naW4vbG9naW4uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNZOztFQUVJLHFCQUFBO0VBQ0Esa0JBQUE7RUFDQSxzQ0FBQTtBQUFoQiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgICAgICAgICAgOmhvc3QgOjpuZy1kZWVwIC5waS1leWUsXG4gICAgICAgICAgICA6aG9zdCA6Om5nLWRlZXAgLnBpLWV5ZS1zbGFzaCB7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjYpO1xuICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMXJlbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "layoutService", "authService", "val<PERSON><PERSON><PERSON>", "redirectToDashboard", "login", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_p_password_ngModelChange_15_listener", "$event", "password", "LoginComponent_Template_button_click_21_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthService } from 'src/app/core/service/auth.service';\r\nimport { LayoutService } from 'src/app/layout/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styles: [\r\n        `\r\n            :host ::ng-deep .pi-eye,\r\n            :host ::ng-deep .pi-eye-slash {\r\n                transform: scale(1.6);\r\n                margin-right: 1rem;\r\n                color: var(--primary-color) !important;\r\n            }\r\n        `,\r\n    ],\r\n})\r\nexport class LoginComponent {\r\n    valCheck: string[] = ['remember'];\r\n\r\n    password!: string;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {\r\n        this.authService.redirectToDashboard();\r\n    }\r\n\r\n    login() {\r\n        this.authService.login();\r\n    }\r\n}\r\n", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\r\n    <div class=\"flex flex-column align-items-center justify-content-center\">\r\n        <div style=\"border-radius: 56px; padding: 0.3rem\">\r\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8\" style=\"border-radius: 53px\">\r\n                <div class=\"text-center mb-5\">\r\n                    <div class=\"text-900 text-3xl font-medium mb-3\">Welcome, exex-debt!</div>\r\n                    <span class=\"text-600 font-medium\">Sign in to continue</span>\r\n                </div>\r\n\r\n                <div>\r\n                    <label for=\"email1\" class=\"block text-900 text-xl font-medium mb-2\">Email</label>\r\n                    <input\r\n                        id=\"email1\"\r\n                        type=\"text\"\r\n                        placeholder=\"Email address\"\r\n                        pInputText\r\n                        class=\"w-full md:w-30rem mb-5\"\r\n                        style=\"padding: 1rem\" />\r\n\r\n                    <label for=\"password1\" class=\"block text-900 font-medium text-xl mb-2\">Password</label>\r\n                    <p-password\r\n                        id=\"password1\"\r\n                        [(ngModel)]=\"password\"\r\n                        placeholder=\"Password\"\r\n                        [toggleMask]=\"true\"\r\n                        styleClass=\"mb-5\"\r\n                        inputStyleClass=\"w-full p-3 md:w-30rem\"></p-password>\r\n\r\n                    <div class=\"flex align-items-center justify-content-between mb-5 gap-5\">\r\n                        <div class=\"flex align-items-center\">\r\n                            <p-checkbox id=\"rememberme1\" [binary]=\"true\" styleClass=\"mr-2\"></p-checkbox>\r\n                            <label for=\"rememberme1\">Remember me</label>\r\n                        </div>\r\n                        <!-- <a\r\n                            class=\"font-medium no-underline ml-2 text-right cursor-pointer\"\r\n                            style=\"color: var(--primary-color)\"\r\n                            >Forgot password?</a\r\n                        > -->\r\n                    </div>\r\n                    <button pButton pRipple label=\"Sign In\" class=\"w-full p-3 text-xl\" (click)=\"login()\"></button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;AAkBA,OAAM,MAAOA,cAAc;EAKvBC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IANvB,KAAAC,QAAQ,GAAa,CAAC,UAAU,CAAC;IAQ7B,IAAI,CAACD,WAAW,CAACE,mBAAmB,EAAE;EAC1C;EAEAC,KAAKA,CAAA;IACD,IAAI,CAACH,WAAW,CAACG,KAAK,EAAE;EAC5B;EAAC,QAAAC,CAAA,G;qBAdQP,cAAc,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdd,cAAc;IAAAe,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB3Bb,EAAA,CAAAe,cAAA,aAAqH;QAKjDf,EAAA,CAAAgB,MAAA,0BAAmB;QAAAhB,EAAA,CAAAiB,YAAA,EAAM;QACzEjB,EAAA,CAAAe,cAAA,cAAmC;QAAAf,EAAA,CAAAgB,MAAA,0BAAmB;QAAAhB,EAAA,CAAAiB,YAAA,EAAO;QAGjEjB,EAAA,CAAAe,cAAA,UAAK;QACmEf,EAAA,CAAAgB,MAAA,aAAK;QAAAhB,EAAA,CAAAiB,YAAA,EAAQ;QACjFjB,EAAA,CAAAkB,SAAA,gBAM4B;QAE5BlB,EAAA,CAAAe,cAAA,gBAAuE;QAAAf,EAAA,CAAAgB,MAAA,gBAAQ;QAAAhB,EAAA,CAAAiB,YAAA,EAAQ;QACvFjB,EAAA,CAAAe,cAAA,sBAM4C;QAJxCf,EAAA,CAAAmB,UAAA,2BAAAC,6DAAAC,MAAA;UAAA,OAAAP,GAAA,CAAAQ,QAAA,GAAAD,MAAA;QAAA,EAAsB;QAIkBrB,EAAA,CAAAiB,YAAA,EAAa;QAEzDjB,EAAA,CAAAe,cAAA,eAAwE;QAEhEf,EAAA,CAAAkB,SAAA,sBAA4E;QAC5ElB,EAAA,CAAAe,cAAA,iBAAyB;QAAAf,EAAA,CAAAgB,MAAA,mBAAW;QAAAhB,EAAA,CAAAiB,YAAA,EAAQ;QAQpDjB,EAAA,CAAAe,cAAA,kBAAqF;QAAlBf,EAAA,CAAAmB,UAAA,mBAAAI,iDAAA;UAAA,OAAST,GAAA,CAAAhB,KAAA,EAAO;QAAA,EAAC;QAACE,EAAA,CAAAiB,YAAA,EAAS;;;QAjB1FjB,EAAA,CAAAwB,SAAA,IAAsB;QAAtBxB,EAAA,CAAAyB,UAAA,YAAAX,GAAA,CAAAQ,QAAA,CAAsB;QAQWtB,EAAA,CAAAwB,SAAA,GAAe;QAAfxB,EAAA,CAAAyB,UAAA,gBAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}