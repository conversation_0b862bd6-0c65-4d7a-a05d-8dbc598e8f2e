{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, timeout } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class TimeoutInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    const timeoutDuration = 10000; // 10 seconds\n    return next.handle(request).pipe(timeout(timeoutDuration), catchError(error => {\n      if (error.name === 'TimeoutError') {\n        // Handle timeout error here\n        console.error('Request timed out:', request.url);\n        return throwError('Request timed out');\n      }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function TimeoutInterceptor_Factory(t) {\n    return new (t || TimeoutInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TimeoutInterceptor,\n    factory: TimeoutInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "timeout", "TimeoutInterceptor", "constructor", "intercept", "request", "next", "timeoutDuration", "handle", "pipe", "error", "name", "console", "url", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\timeout.interceptor.ts"], "sourcesContent": ["import { <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, timeout } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class TimeoutInterceptor implements HttpInterceptor {\r\n    constructor() {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<any> {\r\n        const timeoutDuration = 10000; // 10 seconds\r\n        return next.handle(request).pipe(\r\n            timeout(timeoutDuration),\r\n            catchError((error) => {\r\n                if (error.name === 'TimeoutError') {\r\n                    // Handle timeout error here\r\n                    console.error('Request timed out:', request.url);\r\n                    return throwError('Request timed out');\r\n                }\r\n                return throwError(error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;;AAGpD,OAAM,MAAOC,kBAAkB;EAC3BC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;IAC/B,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAC5BR,OAAO,CAACM,eAAe,CAAC,EACxBP,UAAU,CAAEU,KAAK,IAAI;MACjB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;QAC/B;QACAC,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEL,OAAO,CAACQ,GAAG,CAAC;QAChD,OAAOd,UAAU,CAAC,mBAAmB,CAAC;;MAE1C,OAAOA,UAAU,CAACW,KAAK,CAAC;IAC5B,CAAC,CAAC,CACL;EACL;EAAC,QAAAI,CAAA,G;qBAhBQZ,kBAAkB;EAAA;EAAA,QAAAa,EAAA,G;WAAlBb,kBAAkB;IAAAc,OAAA,EAAlBd,kBAAkB,CAAAe;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}