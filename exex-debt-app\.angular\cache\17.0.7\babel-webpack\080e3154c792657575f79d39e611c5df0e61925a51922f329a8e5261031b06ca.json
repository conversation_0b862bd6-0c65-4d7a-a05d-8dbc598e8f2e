{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../service/auth.service';\nimport { Path } from '../enums/path.enum';\nexport const authGuard = (route, state) => {\n  const isAuth = inject(AuthService).isAuthenticated();\n  const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\n  return isAuth ? true : redirectToLogin;\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthService", "Path", "<PERSON>th<PERSON><PERSON>", "route", "state", "isAuth", "isAuthenticated", "redirectToLogin", "createUrlTree", "AUTH_LOGIN"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { AuthService } from '../service/auth.service';\r\nimport { Path } from '../enums/path.enum';\r\n\r\nexport const authGuard: CanActivateFn = (route, state) => {\r\n    const isAuth = inject(AuthService).isAuthenticated();\r\n    const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\r\n    return isAuth ? true : redirectToLogin;\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,IAAI,QAAQ,oBAAoB;AAEzC,OAAO,MAAMC,SAAS,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACrD,MAAMC,MAAM,GAAGP,MAAM,CAACE,WAAW,CAAC,CAACM,eAAe,EAAE;EACpD,MAAMC,eAAe,GAAGT,MAAM,CAACC,MAAM,CAAC,CAACS,aAAa,CAAC,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAC;EACvE,OAAOJ,MAAM,GAAG,IAAI,GAAGE,eAAe;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}