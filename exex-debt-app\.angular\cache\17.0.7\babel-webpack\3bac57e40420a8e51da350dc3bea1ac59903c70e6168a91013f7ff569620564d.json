{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DialogModule } from 'primeng/dialog';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { TableModule } from 'primeng/table';\nimport { ToastModule } from 'primeng/toast';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { CustomerRoutingModule } from './customer-routing.module';\nimport { CustomerComponent } from './customer.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { KeyFilterModule } from 'primeng/keyfilter';\nimport * as i0 from \"@angular/core\";\nexport class CustomerModule {\n  static #_ = this.ɵfac = function CustomerModule_Factory(t) {\n    return new (t || CustomerModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CustomerModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [FormsModule, ReactiveFormsModule, CommonModule, CustomerRoutingModule, ToastModule, ToolbarModule, TableModule, DialogModule, ConfirmDialogModule, InputTextModule, FileUploadModule, InputTextareaModule, KeyFilterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerModule, {\n    declarations: [CustomerComponent],\n    imports: [FormsModule, ReactiveFormsModule, CommonModule, CustomerRoutingModule, ToastModule, ToolbarModule, TableModule, DialogModule, ConfirmDialogModule, InputTextModule, FileUploadModule, InputTextareaModule, KeyFilterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ConfirmDialogModule", "DialogModule", "FileUploadModule", "InputTextModule", "InputTextareaModule", "TableModule", "ToastModule", "ToolbarModule", "CustomerRoutingModule", "CustomerComponent", "FormsModule", "ReactiveFormsModule", "KeyFilterModule", "CustomerModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { FileUploadModule } from 'primeng/fileupload';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { CustomerRoutingModule } from './customer-routing.module';\r\nimport { CustomerComponent } from './customer.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { KeyFilterModule } from 'primeng/keyfilter';\r\n@NgModule({\r\n    declarations: [CustomerComponent],\r\n    imports: [\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        CommonModule,\r\n        CustomerRoutingModule,\r\n        ToastModule,\r\n        ToolbarModule,\r\n        TableModule,\r\n        DialogModule,\r\n        ConfirmDialogModule,\r\n        InputTextModule,\r\n        FileUploadModule,\r\n        InputTextareaModule,\r\n        KeyFilterModule,\r\n    ],\r\n})\r\nexport class CustomerModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,mBAAmB;;AAmBnD,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;cAfnBN,WAAW,EACXC,mBAAmB,EACnBZ,YAAY,EACZS,qBAAqB,EACrBF,WAAW,EACXC,aAAa,EACbF,WAAW,EACXJ,YAAY,EACZD,mBAAmB,EACnBG,eAAe,EACfD,gBAAgB,EAChBE,mBAAmB,EACnBQ,eAAe;EAAA;;;2EAGVC,cAAc;IAAAI,YAAA,GAjBRR,iBAAiB;IAAAS,OAAA,GAE5BR,WAAW,EACXC,mBAAmB,EACnBZ,YAAY,EACZS,qBAAqB,EACrBF,WAAW,EACXC,aAAa,EACbF,WAAW,EACXJ,YAAY,EACZD,mBAAmB,EACnBG,eAAe,EACfD,gBAAgB,EAChBE,mBAAmB,EACnBQ,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}