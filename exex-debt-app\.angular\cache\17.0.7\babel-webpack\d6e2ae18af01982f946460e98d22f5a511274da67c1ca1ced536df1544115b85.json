{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"../core/service/auth.service\";\nimport * as i3 from \"@jsverse/transloco\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"topbarmenubutton\"];\nconst _c2 = [\"topbarmenu\"];\nexport class AppTopBarComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n  }\n  signout() {\n    this.authService.logout();\n  }\n  static #_ = this.ɵfac = function AppTopBarComponent_Factory(t) {\n    return new (t || AppTopBarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppTopBarComponent,\n    selectors: [[\"app-topbar\"]],\n    viewQuery: function AppTopBarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.topbarMenuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n      }\n    },\n    decls: 12,\n    vars: 3,\n    consts: [[1, \"layout-topbar\", \"justify-content-between\"], [1, \"p-link\", \"layout-topbar-button\", 3, \"click\"], [\"menubutton\", \"\"], [1, \"pi\", \"pi-bars\"], [1, \"mb-0\"], [1, \"pi\", \"pi-fw\", \"pi-sign-in\"]],\n    template: function AppTopBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1, 2);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_1_listener() {\n          return ctx.layoutService.onMenuToggle();\n        });\n        i0.ɵɵelement(3, \"i\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"strong\")(5, \"h5\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"transloco\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"button\", 1);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_8_listener() {\n          return ctx.signout();\n        });\n        i0.ɵɵelement(9, \"i\", 5);\n        i0.ɵɵelementStart(10, \"span\");\n        i0.ɵɵtext(11, \"SignOut\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 1, \"title\"));\n      }\n    },\n    dependencies: [i3.TranslocoPipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppTopBarComponent", "constructor", "layoutService", "authService", "signout", "logout", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "viewQuery", "AppTopBarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppTopBarComponent_Template_button_click_1_listener", "onMenuToggle", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "AppTopBarComponent_Template_button_click_8_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.topbar.component.ts"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { LayoutService } from './app.layout.service';\r\nimport { AuthService } from '../core/service/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    template: `<div class=\"layout-topbar justify-content-between\">\r\n        <button\r\n            #menubutton\r\n            class=\"p-link layout-topbar-button\"\r\n            (click)=\"layoutService.onMenuToggle()\"\r\n        >\r\n            <i class=\"pi pi-bars\"></i>\r\n        </button>\r\n        <strong>\r\n            <h5 class=\"mb-0\">{{ 'title' | transloco }}</h5>\r\n        </strong>\r\n        <button class=\"p-link layout-topbar-button\" (click)=\"signout()\">\r\n            <i class=\"pi pi-fw pi-sign-in\"></i>\r\n            <span>SignOut</span>\r\n        </button>\r\n    </div>`,\r\n})\r\nexport class AppTopBarComponent {\r\n    items!: MenuItem[];\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenu') menu!: ElementRef;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService\r\n    ) {}\r\n\r\n    signout() {\r\n        this.authService.logout();\r\n    }\r\n}\r\n"], "mappings": ";;;;;;;AAwBA,OAAM,MAAOA,kBAAkB;EAS3BC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;EACpB;EAEHC,OAAOA,CAAA;IACH,IAAI,CAACD,WAAW,CAACE,MAAM,EAAE;EAC7B;EAAC,QAAAC,CAAA,G;qBAhBQN,kBAAkB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBb,kBAAkB;IAAAc,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;QAjBhBV,EAAA,CAAAY,cAAA,aAAmD;QAItDZ,EAAA,CAAAa,UAAA,mBAAAC,oDAAA;UAAA,OAASH,GAAA,CAAAhB,aAAA,CAAAoB,YAAA,EAA4B;QAAA,EAAC;QAEtCf,EAAA,CAAAgB,SAAA,WAA0B;QAC9BhB,EAAA,CAAAiB,YAAA,EAAS;QACTjB,EAAA,CAAAY,cAAA,aAAQ;QACaZ,EAAA,CAAAkB,MAAA,GAAyB;;QAAAlB,EAAA,CAAAiB,YAAA,EAAK;QAEnDjB,EAAA,CAAAY,cAAA,gBAAgE;QAApBZ,EAAA,CAAAa,UAAA,mBAAAM,oDAAA;UAAA,OAASR,GAAA,CAAAd,OAAA,EAAS;QAAA,EAAC;QAC3DG,EAAA,CAAAgB,SAAA,WAAmC;QACnChB,EAAA,CAAAY,cAAA,YAAM;QAAAZ,EAAA,CAAAkB,MAAA,eAAO;QAAAlB,EAAA,CAAAiB,YAAA,EAAO;;;QAJHjB,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAsB,WAAA,gBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}