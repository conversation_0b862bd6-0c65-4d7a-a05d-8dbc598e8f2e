{"ast": null, "code": "const {\n  isArray\n} = Array;\nconst {\n  getPrototypeOf,\n  prototype: objectProto,\n  keys: getKeys\n} = Object;\nexport function argsArgArrayOrObject(args) {\n  if (args.length === 1) {\n    const first = args[0];\n    if (isArray(first)) {\n      return {\n        args: first,\n        keys: null\n      };\n    }\n    if (isPOJO(first)) {\n      const keys = getKeys(first);\n      return {\n        args: keys.map(key => first[key]),\n        keys\n      };\n    }\n  }\n  return {\n    args: args,\n    keys: null\n  };\n}\nfunction isPOJO(obj) {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}", "map": {"version": 3, "names": ["isArray", "Array", "getPrototypeOf", "prototype", "objectProto", "keys", "get<PERSON><PERSON><PERSON>", "Object", "argsArgArrayOrObject", "args", "length", "first", "isPOJO", "map", "key", "obj"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/rxjs/dist/esm/internal/util/argsArgArrayOrObject.js"], "sourcesContent": ["const { isArray } = Array;\nconst { getPrototypeOf, prototype: objectProto, keys: getKeys } = Object;\nexport function argsArgArrayOrObject(args) {\n    if (args.length === 1) {\n        const first = args[0];\n        if (isArray(first)) {\n            return { args: first, keys: null };\n        }\n        if (isPOJO(first)) {\n            const keys = getKeys(first);\n            return {\n                args: keys.map((key) => first[key]),\n                keys,\n            };\n        }\n    }\n    return { args: args, keys: null };\n}\nfunction isPOJO(obj) {\n    return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}\n"], "mappings": "AAAA,MAAM;EAAEA;AAAQ,CAAC,GAAGC,KAAK;AACzB,MAAM;EAAEC,cAAc;EAAEC,SAAS,EAAEC,WAAW;EAAEC,IAAI,EAAEC;AAAQ,CAAC,GAAGC,MAAM;AACxE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACvC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACnB,MAAMC,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACrB,IAAIT,OAAO,CAACW,KAAK,CAAC,EAAE;MAChB,OAAO;QAAEF,IAAI,EAAEE,KAAK;QAAEN,IAAI,EAAE;MAAK,CAAC;IACtC;IACA,IAAIO,MAAM,CAACD,KAAK,CAAC,EAAE;MACf,MAAMN,IAAI,GAAGC,OAAO,CAACK,KAAK,CAAC;MAC3B,OAAO;QACHF,IAAI,EAAEJ,IAAI,CAACQ,GAAG,CAAEC,GAAG,IAAKH,KAAK,CAACG,GAAG,CAAC,CAAC;QACnCT;MACJ,CAAC;IACL;EACJ;EACA,OAAO;IAAEI,IAAI,EAAEA,IAAI;IAAEJ,IAAI,EAAE;EAAK,CAAC;AACrC;AACA,SAASO,MAAMA,CAACG,GAAG,EAAE;EACjB,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIb,cAAc,CAACa,GAAG,CAAC,KAAKX,WAAW;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}