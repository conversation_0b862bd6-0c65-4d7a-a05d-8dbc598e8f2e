{"ast": null, "code": "import { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class RequestTimingInterceptor {\n  intercept(request, next) {\n    const startTime = Date.now();\n    return next.handle(request).pipe(tap(() => {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      console.log(`Request to ${request.url} took ${duration}ms`);\n    }));\n  }\n  static #_ = this.ɵfac = function RequestTimingInterceptor_Factory(t) {\n    return new (t || RequestTimingInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RequestTimingInterceptor,\n    factory: RequestTimingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["tap", "RequestTimingInterceptor", "intercept", "request", "next", "startTime", "Date", "now", "handle", "pipe", "endTime", "duration", "console", "log", "url", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\request-timing.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n} from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\n\n@Injectable()\nexport class RequestTimingInterceptor implements HttpInterceptor {\n  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {\n    const startTime = Date.now();\n    return next.handle(request).pipe(\n      tap(() => {\n        const endTime = Date.now();\n        const duration = endTime - startTime;\n        console.log(`Request to ${request.url} took ${duration}ms`);\n      })\n    );\n  }\n}"], "mappings": "AAMA,SAASA,GAAG,QAAQ,gBAAgB;;AAGpC,OAAM,MAAOC,wBAAwB;EACnCC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,OAAOH,IAAI,CAACI,MAAM,CAACL,OAAO,CAAC,CAACM,IAAI,CAC9BT,GAAG,CAAC,MAAK;MACP,MAAMU,OAAO,GAAGJ,IAAI,CAACC,GAAG,EAAE;MAC1B,MAAMI,QAAQ,GAAGD,OAAO,GAAGL,SAAS;MACpCO,OAAO,CAACC,GAAG,CAAC,cAAcV,OAAO,CAACW,GAAG,SAASH,QAAQ,IAAI,CAAC;IAC7D,CAAC,CAAC,CACH;EACH;EAAC,QAAAI,CAAA,G;qBAVUd,wBAAwB;EAAA;EAAA,QAAAe,EAAA,G;WAAxBf,wBAAwB;IAAAgB,OAAA,EAAxBhB,wBAAwB,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}