{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport { authGuard } from './core/guards/auth.guard';\nimport { Path } from './core/enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot([{\n      path: '',\n      redirectTo: Path.DASHBOARD_CUSTOMER,\n      pathMatch: 'full'\n    }, {\n      path: '',\n      component: AppLayoutComponent,\n      children: [{\n        path: 'dashboard',\n        loadChildren: () => import('./core/components/dashboard/dashboard.module').then(m => m.DashboardModule),\n        canActivate: [authGuard]\n      }]\n    }, {\n      path: 'auth',\n      loadChildren: () => import('./core/components/auth/auth.module').then(m => m.AuthModule)\n    }, {\n      path: 'notfound',\n      component: NotfoundComponent\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }], {\n      scrollPositionRestoration: 'enabled',\n      anchorScrolling: 'enabled',\n      onSameUrlNavigation: 'reload'\n    }), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "NotfoundComponent", "AppLayoutComponent", "<PERSON>th<PERSON><PERSON>", "Path", "AppRoutingModule", "_", "_2", "_3", "forRoot", "path", "redirectTo", "DASHBOARD_CUSTOMER", "pathMatch", "component", "children", "loadChildren", "then", "m", "DashboardModule", "canActivate", "AuthModule", "scrollPositionRestoration", "anchorScrolling", "onSameUrlNavigation", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { RouterModule } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\nimport { authGuard } from './core/guards/auth.guard';\r\nimport { Path } from './core/enums/path.enum';\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forRoot(\r\n            [\r\n                {\r\n                    path: '',\r\n                    redirectTo: Path.DASHBOARD_CUSTOMER,\r\n                    pathMatch: 'full',\r\n                },\r\n                {\r\n                    path: '',\r\n                    component: AppLayoutComponent,\r\n                    children: [\r\n                        {\r\n                            path: 'dashboard',\r\n                            loadChildren: () =>\r\n                                import('./core/components/dashboard/dashboard.module').then((m) => m.DashboardModule),\r\n                            canActivate: [authGuard],\r\n                        },\r\n                    ],\r\n                },\r\n                {\r\n                    path: 'auth',\r\n                    loadChildren: () => import('./core/components/auth/auth.module').then((m) => m.AuthModule),\r\n                },\r\n                { path: 'notfound', component: NotfoundComponent },\r\n                { path: '**', redirectTo: '/notfound' },\r\n            ],\r\n            {\r\n                scrollPositionRestoration: 'enabled',\r\n                anchorScrolling: 'enabled',\r\n                onSameUrlNavigation: 'reload',\r\n            },\r\n        ),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,IAAI,QAAQ,wBAAwB;;;AAuC7C,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAnCrBR,YAAY,CAACS,OAAO,CAChB,CACI;MACIC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAEP,IAAI,CAACQ,kBAAkB;MACnCC,SAAS,EAAE;KACd,EACD;MACIH,IAAI,EAAE,EAAE;MACRI,SAAS,EAAEZ,kBAAkB;MAC7Ba,QAAQ,EAAE,CACN;QACIL,IAAI,EAAE,WAAW;QACjBM,YAAY,EAAEA,CAAA,KACV,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC;QACzFC,WAAW,EAAE,CAACjB,SAAS;OAC1B;KAER,EACD;MACIO,IAAI,EAAE,MAAM;MACZM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,UAAU;KAC5F,EACD;MAAEX,IAAI,EAAE,UAAU;MAAEI,SAAS,EAAEb;IAAiB,CAAE,EAClD;MAAES,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAW,CAAE,CAC1C,EACD;MACIW,yBAAyB,EAAE,SAAS;MACpCC,eAAe,EAAE,SAAS;MAC1BC,mBAAmB,EAAE;KACxB,CACJ,EAEKxB,YAAY;EAAA;;;2EAEbK,gBAAgB;IAAAoB,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFf3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}