{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { LoginComponent } from './login.component';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i0 from \"@angular/core\";\nexport class LoginModule {\n  static #_ = this.ɵfac = function LoginModule_Factory(t) {\n    return new (t || LoginModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LoginModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginModule, {\n    declarations: [LoginComponent],\n    imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "LoginRoutingModule", "LoginComponent", "ButtonModule", "CheckboxModule", "FormsModule", "PasswordModule", "InputTextModule", "LoginModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { LoginComponent } from './login.component';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        LoginRoutingModule,\n        ButtonModule,\n        CheckboxModule,\n        InputTextModule,\n        FormsModule,\n        PasswordModule\n    ],\n    declarations: [LoginComponent]\n})\nexport class LoginModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;;AAcnD,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA,G;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA,G;UAAXF;EAAW;EAAA,QAAAG,EAAA,G;cAVhBX,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdG,eAAe,EACfF,WAAW,EACXC,cAAc;EAAA;;;2EAITE,WAAW;IAAAI,YAAA,GAFLV,cAAc;IAAAW,OAAA,GARzBb,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdG,eAAe,EACfF,WAAW,EACXC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}