import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { IPropExexTable } from './exex-table.model';
import { WindowSizeService } from '@app/core/services/window-size.service';

@Component({
    selector: 'exex-table',
    template: `<p-table
        [rows]="propExexTable.rows || 10"
        [columns]="propExexTable.columns"
        [value]="propExexTable.value"
        [paginator]="true"
        [scrollable]="true"
        [resizableColumns]="true"
        [scrollHeight]="heightTableScroll + 'px'"
        [(selection)]="selectedList"
        (selectionChange)="onSelectionChange($event)"
        [tableStyle]="{ 'min-width': '75rem' }"
        dataKey="id"
        columnResizeMode="expand"
        styleClass="p-datatable-striped p-datatable-gridlines">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th style="width: 52px" *ngIf="!!!propExexTable.isHideChecked">
                    <p-tableHeaderCheckbox />
                </th>
                <ng-container *ngFor="let col of columns; trackBy: col">
                    <th [pSortableColumn]="col.field" [style.min-width]="col.width" pResizableColumn>
                        {{ col.title }} <p-sortIcon [field]="col.field" />
                    </th>
                </ng-container>
                <th style="width: 121px" *ngIf="!!!propExexTable.isHideEdit"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-row let-columns="columns" let-index="rowIndex">
            <tr>
                <td *ngIf="!!!propExexTable.isHideChecked">
                    <p-tableCheckbox [value]="row" />
                </td>
                <ng-container *ngFor="let col of columns; trackBy: col">
                    <td [ngClass]="row[col.field] | textAlign">
                        <span *ngIf="col.field === 'status'" [innerHTML]="row[col.field] | statusBadge"></span>
                        <span *ngIf="col.field !== 'status'">{{ row[col.field] | money }}</span>
                    </td>
                </ng-container>
                <td class="custom-group-button-edit" *ngIf="!!!propExexTable.isHideEdit">
                    <p-button
                        icon="pi pi-pencil"
                        class="mr-2"
                        severity="success"
                        size="small"
                        [raised]="true"
                        (onClick)="editRow(row)" />
                    <p-button
                        icon="pi pi-trash"
                        severity="danger"
                        size="small"
                        [raised]="true"
                        (onClick)="deleteRow(row)" />
                </td>
            </tr>
        </ng-template>
    </p-table> `,
})
export class ExexTableComponent {
    @Input() propExexTable!: IPropExexTable;
    @Output() editEvent = new EventEmitter<any>();
    @Output() deleteEvent = new EventEmitter<any>();
    @Output() selectedEvent = new EventEmitter<any>();

    selectedList!: any[] | null;
    heightTableScroll: number = 0;

    constructor(private windowSizeService: WindowSizeService) {}

    ngOnInit() {
        this.windowSizeService.heightTableSubject.subscribe((height) => {
            this.heightTableScroll = height;
        });
    }

    onSelectionChange(rows: any) {
        this.selectedEvent.emit(rows);
    }

    editRow(row) {
        this.editEvent.emit(row);
    }

    deleteRow(row) {
        this.deleteEvent.emit(row);
    }
}
