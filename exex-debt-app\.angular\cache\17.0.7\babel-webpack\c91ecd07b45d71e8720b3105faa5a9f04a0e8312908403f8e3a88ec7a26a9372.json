{"ast": null, "code": "import { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class RequestTimingInterceptor {\n  intercept(request, next) {\n    const startTime = Date.now();\n    return next.handle(request).pipe(tap(() => {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      console.log(`Request to ${request.url} took ${duration}ms`);\n    }));\n  }\n  static #_ = this.ɵfac = function RequestTimingInterceptor_Factory(t) {\n    return new (t || RequestTimingInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RequestTimingInterceptor,\n    factory: RequestTimingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["tap", "RequestTimingInterceptor", "intercept", "request", "next", "startTime", "Date", "now", "handle", "pipe", "endTime", "duration", "console", "log", "url", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\request-timing.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';\r\nimport { tap } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class RequestTimingInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>) {\r\n        const startTime = Date.now();\r\n        return next.handle(request).pipe(\r\n            tap(() => {\r\n                const endTime = Date.now();\r\n                const duration = endTime - startTime;\r\n                console.log(`Request to ${request.url} took ${duration}ms`);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAQ,gBAAgB;;AAGpC,OAAM,MAAOC,wBAAwB;EACjCC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,OAAOH,IAAI,CAACI,MAAM,CAACL,OAAO,CAAC,CAACM,IAAI,CAC5BT,GAAG,CAAC,MAAK;MACL,MAAMU,OAAO,GAAGJ,IAAI,CAACC,GAAG,EAAE;MAC1B,MAAMI,QAAQ,GAAGD,OAAO,GAAGL,SAAS;MACpCO,OAAO,CAACC,GAAG,CAAC,cAAcV,OAAO,CAACW,GAAG,SAASH,QAAQ,IAAI,CAAC;IAC/D,CAAC,CAAC,CACL;EACL;EAAC,QAAAI,CAAA,G;qBAVQd,wBAAwB;EAAA;EAAA,QAAAe,EAAA,G;WAAxBf,wBAAwB;IAAAgB,OAAA,EAAxBhB,wBAAwB,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}