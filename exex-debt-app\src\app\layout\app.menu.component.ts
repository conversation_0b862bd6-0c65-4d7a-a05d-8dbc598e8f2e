import { Component, OnInit } from '@angular/core';
import { LayoutService } from './app.layout.service';
import { MENU_LIST } from './menu-list';

@Component({
    selector: 'app-menu',
    template: `<ul class="layout-menu">
        <ng-container *ngFor="let item of model; let i = index">
            <li app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
        </ng-container>
    </ul>`,
})
export class AppMenuComponent implements OnInit {
    model: any[] = [];

    constructor(public layoutService: LayoutService) {}

    ngOnInit() {
        this.model = MENU_LIST;
    }
}
