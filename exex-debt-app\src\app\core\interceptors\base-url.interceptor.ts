import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable()
export class BaseUrlInterceptor implements HttpInterceptor {
    intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler) {
        const apiRequest = request.clone({
            url: this.prepareUrl(request.url),
        });
        return next.handle(apiRequest);
    }

    private isAbsoluteUrl(url: string): boolean {
        const absolutePattern = /^https?:\/\//i;
        return absolutePattern.test(url);
    }

    private prepareUrl(url: string): string {
        const conditions = ['assets'];
        url = conditions.some((val) => url.includes(val)) || this.isAbsoluteUrl(url) ? url : environment.urlApi + url;
        return url;
    }
}
