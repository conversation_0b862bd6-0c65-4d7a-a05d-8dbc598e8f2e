{"ast": null, "code": "export const CUSTOMER_COLS = [{\n  field: 'customerId',\n  title: 'Customer ID',\n  width: '50px'\n}, {\n  field: 'customerName',\n  title: 'Customer Name',\n  width: '150px'\n}, {\n  field: 'phoneNumber',\n  title: 'Phone Number',\n  width: '100px'\n}, {\n  field: 'email',\n  title: 'Email',\n  width: '100px'\n}, {\n  field: 'address',\n  title: 'Address',\n  width: '100px'\n}, {\n  field: 'status',\n  title: 'Status',\n  width: '100px'\n}, {\n  field: 'creditLimit',\n  title: 'Credit Limit',\n  width: '100px'\n}, {\n  field: 'currentBalance',\n  title: 'Current Balance',\n  width: '100px'\n}, {\n  field: 'currencyId',\n  title: 'Currency ID',\n  width: '100px'\n}];", "map": {"version": 3, "names": ["CUSTOMER_COLS", "field", "title", "width"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer-cols.ts"], "sourcesContent": ["export const CUSTOMER_COLS = [\r\n    { field: 'customerId', title: 'Customer ID', width: '50px' },\r\n    { field: 'customerName', title: 'Customer Name', width: '150px' },\r\n    { field: 'phoneNumber', title: 'Phone Number', width: '100px' },\r\n    { field: 'email', title: 'Email', width: '100px' },\r\n    { field: 'address', title: 'Address', width: '100px' },\r\n    { field: 'status', title: 'Status', width: '100px' },\r\n    { field: 'creditLimit', title: 'Credit Limit', width: '100px' },\r\n    { field: 'currentBalance', title: 'Current Balance', width: '100px' },\r\n    { field: 'currencyId', title: 'Currency ID', width: '100px' },\r\n];\r\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG,CACzB;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAM,CAAE,EAC5D;EAAEF,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAO,CAAE,EACjE;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAClD;EAAEF,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAO,CAAE,EACtD;EAAEF,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAO,CAAE,EACpD;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,gBAAgB;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAO,CAAE,EACrE;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAO,CAAE,CAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}