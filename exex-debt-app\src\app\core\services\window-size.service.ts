import { Injectable } from '@angular/core';
import { BehaviorSubject, fromEvent } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class WindowSizeService {
    private heightSubject = new BehaviorSubject<number>(window.innerHeight);
    height$ = this.heightSubject.asObservable();
    scale = Number(localStorage.getItem('scale')) || 14;
    heightOffsets = { 14: 346, 15: 366, 16: 391 };
    calcHeight = this.heightOffsets[this.scale];
    heightTableSubject = new BehaviorSubject<number>(window.innerHeight - this.calcHeight);
    constructor() {
        fromEvent(window, 'resize').subscribe(() => {
            this.heightSubject.next(window.innerHeight);
        });
    }

    getHeight(): number {
        return this.heightSubject.value;
    }

    getHeightTable(scale) {
        this.heightTableSubject.next(window.innerHeight - this.heightOffsets[scale]);
    }
}
