{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nlet AppConfigComponent = class AppConfigComponent {\n  constructor(layoutService, menuService, translocoService, windowSizeService) {\n    this.layoutService = layoutService;\n    this.menuService = menuService;\n    this.translocoService = translocoService;\n    this.windowSizeService = windowSizeService;\n    this.minimal = false;\n    this.scales = [12, 13, 14, 15, 16];\n    this.languages = [{\n      title: 'English',\n      value: 'en'\n    }, {\n      title: 'Vietnamese',\n      value: 'vi'\n    }];\n  }\n  get visible() {\n    return this.layoutService.state.configSidebarVisible;\n  }\n  set visible(_val) {\n    this.layoutService.state.configSidebarVisible = _val;\n  }\n  get scale() {\n    return this.layoutService.config().scale;\n  }\n  set scale(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      scale: _val\n    }));\n  }\n  get menuMode() {\n    return this.layoutService.config().menuMode;\n  }\n  set menuMode(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      menuMode: _val\n    }));\n  }\n  get inputStyle() {\n    return this.layoutService.config().inputStyle;\n  }\n  set inputStyle(_val) {\n    this.layoutService.config().inputStyle = _val;\n  }\n  get ripple() {\n    return this.layoutService.config().ripple;\n  }\n  set ripple(_val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      ripple: _val\n    }));\n  }\n  set theme(val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      theme: val\n    }));\n  }\n  get theme() {\n    return this.layoutService.config().theme;\n  }\n  set colorScheme(val) {\n    this.layoutService.config.update(config => ({\n      ...config,\n      colorScheme: val\n    }));\n  }\n  get colorScheme() {\n    return this.layoutService.config().colorScheme;\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  changeTheme(theme, colorScheme) {\n    this.theme = theme;\n    this.colorScheme = colorScheme;\n    localStorage.setItem('theme', theme);\n    localStorage.setItem('colorScheme', colorScheme);\n  }\n  changeLanguage(lg) {\n    this.translocoService.setActiveLang(lg);\n    localStorage.setItem('lg', lg);\n  }\n  decrementScale() {\n    this.scale--;\n    localStorage.setItem('scale', this.scale.toString());\n    this.windowSizeService.getHeightTable(this.scale);\n  }\n  incrementScale() {\n    this.scale++;\n    localStorage.setItem('scale', this.scale.toString());\n    this.windowSizeService.getHeightTable(this.scale);\n  }\n};\n__decorate([Input()], AppConfigComponent.prototype, \"minimal\", void 0);\nAppConfigComponent = __decorate([Component({\n  selector: 'app-config',\n  templateUrl: './app.config.component.html'\n})], AppConfigComponent);\nexport { AppConfigComponent };", "map": {"version": 3, "names": ["Component", "Input", "AppConfigComponent", "constructor", "layoutService", "menuService", "translocoService", "windowSizeService", "minimal", "scales", "languages", "title", "value", "visible", "state", "configSidebarVisible", "_val", "scale", "config", "update", "menuMode", "inputStyle", "ripple", "theme", "val", "colorScheme", "onConfigButtonClick", "showConfigSidebar", "changeTheme", "localStorage", "setItem", "changeLanguage", "lg", "setActiveLang", "decrementScale", "toString", "getHeightTable", "incrementScale", "__decorate", "selector", "templateUrl"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\config\\app.config.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { LayoutService } from '../app.layout.service';\r\nimport { MenuService } from '../app.menu.service';\r\nimport { TranslocoService } from '@jsverse/transloco';\r\nimport { WindowSizeService } from 'src/app/core/service/window-size.service';\r\n\r\n@Component({\r\n    selector: 'app-config',\r\n    templateUrl: './app.config.component.html',\r\n})\r\nexport class AppConfigComponent {\r\n    @Input() minimal: boolean = false;\r\n\r\n    scales: number[] = [12, 13, 14, 15, 16];\r\n\r\n    languages = [\r\n        {\r\n            title: 'English',\r\n            value: 'en',\r\n        },\r\n        {\r\n            title: 'Vietnamese',\r\n            value: 'vi',\r\n        },\r\n    ];\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public menuService: MenuService,\r\n        private translocoService: TranslocoService,\r\n        private windowSizeService: WindowSizeService,\r\n    ) {}\r\n\r\n    get visible(): boolean {\r\n        return this.layoutService.state.configSidebarVisible;\r\n    }\r\n    set visible(_val: boolean) {\r\n        this.layoutService.state.configSidebarVisible = _val;\r\n    }\r\n\r\n    get scale(): number {\r\n        return this.layoutService.config().scale;\r\n    }\r\n    set scale(_val: number) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            scale: _val,\r\n        }));\r\n    }\r\n\r\n    get menuMode(): string {\r\n        return this.layoutService.config().menuMode;\r\n    }\r\n    set menuMode(_val: string) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            menuMode: _val,\r\n        }));\r\n    }\r\n\r\n    get inputStyle(): string {\r\n        return this.layoutService.config().inputStyle;\r\n    }\r\n    set inputStyle(_val: string) {\r\n        this.layoutService.config().inputStyle = _val;\r\n    }\r\n\r\n    get ripple(): boolean {\r\n        return this.layoutService.config().ripple;\r\n    }\r\n    set ripple(_val: boolean) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            ripple: _val,\r\n        }));\r\n    }\r\n\r\n    set theme(val: string) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            theme: val,\r\n        }));\r\n    }\r\n    get theme(): string {\r\n        return this.layoutService.config().theme;\r\n    }\r\n\r\n    set colorScheme(val: string) {\r\n        this.layoutService.config.update((config) => ({\r\n            ...config,\r\n            colorScheme: val,\r\n        }));\r\n    }\r\n    get colorScheme(): string {\r\n        return this.layoutService.config().colorScheme;\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n\r\n    changeTheme(theme: string, colorScheme: string) {\r\n        this.theme = theme;\r\n        this.colorScheme = colorScheme;\r\n        localStorage.setItem('theme', theme);\r\n        localStorage.setItem('colorScheme', colorScheme);\r\n    }\r\n\r\n    changeLanguage(lg: string) {\r\n        this.translocoService.setActiveLang(lg);\r\n        localStorage.setItem('lg', lg);\r\n    }\r\n\r\n    decrementScale() {\r\n        this.scale--;\r\n        localStorage.setItem('scale', this.scale.toString());\r\n        this.windowSizeService.getHeightTable(this.scale);\r\n    }\r\n\r\n    incrementScale() {\r\n        this.scale++;\r\n        localStorage.setItem('scale', this.scale.toString());\r\n        this.windowSizeService.getHeightTable(this.scale);\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAUzC,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAgB3BC,YACWC,aAA4B,EAC5BC,WAAwB,EACvBC,gBAAkC,EAClCC,iBAAoC;IAHrC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAnBpB,KAAAC,OAAO,GAAY,KAAK;IAEjC,KAAAC,MAAM,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAEvC,KAAAC,SAAS,GAAG,CACR;MACIC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE;KACV,CACJ;EAOE;EAEH,IAAIC,OAAOA,CAAA;IACP,OAAO,IAAI,CAACT,aAAa,CAACU,KAAK,CAACC,oBAAoB;EACxD;EACA,IAAIF,OAAOA,CAACG,IAAa;IACrB,IAAI,CAACZ,aAAa,CAACU,KAAK,CAACC,oBAAoB,GAAGC,IAAI;EACxD;EAEA,IAAIC,KAAKA,CAAA;IACL,OAAO,IAAI,CAACb,aAAa,CAACc,MAAM,EAAE,CAACD,KAAK;EAC5C;EACA,IAAIA,KAAKA,CAACD,IAAY;IAClB,IAAI,CAACZ,aAAa,CAACc,MAAM,CAACC,MAAM,CAAED,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTD,KAAK,EAAED;KACV,CAAC,CAAC;EACP;EAEA,IAAII,QAAQA,CAAA;IACR,OAAO,IAAI,CAAChB,aAAa,CAACc,MAAM,EAAE,CAACE,QAAQ;EAC/C;EACA,IAAIA,QAAQA,CAACJ,IAAY;IACrB,IAAI,CAACZ,aAAa,CAACc,MAAM,CAACC,MAAM,CAAED,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTE,QAAQ,EAAEJ;KACb,CAAC,CAAC;EACP;EAEA,IAAIK,UAAUA,CAAA;IACV,OAAO,IAAI,CAACjB,aAAa,CAACc,MAAM,EAAE,CAACG,UAAU;EACjD;EACA,IAAIA,UAAUA,CAACL,IAAY;IACvB,IAAI,CAACZ,aAAa,CAACc,MAAM,EAAE,CAACG,UAAU,GAAGL,IAAI;EACjD;EAEA,IAAIM,MAAMA,CAAA;IACN,OAAO,IAAI,CAAClB,aAAa,CAACc,MAAM,EAAE,CAACI,MAAM;EAC7C;EACA,IAAIA,MAAMA,CAACN,IAAa;IACpB,IAAI,CAACZ,aAAa,CAACc,MAAM,CAACC,MAAM,CAAED,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTI,MAAM,EAAEN;KACX,CAAC,CAAC;EACP;EAEA,IAAIO,KAAKA,CAACC,GAAW;IACjB,IAAI,CAACpB,aAAa,CAACc,MAAM,CAACC,MAAM,CAAED,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTK,KAAK,EAAEC;KACV,CAAC,CAAC;EACP;EACA,IAAID,KAAKA,CAAA;IACL,OAAO,IAAI,CAACnB,aAAa,CAACc,MAAM,EAAE,CAACK,KAAK;EAC5C;EAEA,IAAIE,WAAWA,CAACD,GAAW;IACvB,IAAI,CAACpB,aAAa,CAACc,MAAM,CAACC,MAAM,CAAED,MAAM,KAAM;MAC1C,GAAGA,MAAM;MACTO,WAAW,EAAED;KAChB,CAAC,CAAC;EACP;EACA,IAAIC,WAAWA,CAAA;IACX,OAAO,IAAI,CAACrB,aAAa,CAACc,MAAM,EAAE,CAACO,WAAW;EAClD;EAEAC,mBAAmBA,CAAA;IACf,IAAI,CAACtB,aAAa,CAACuB,iBAAiB,EAAE;EAC1C;EAEAC,WAAWA,CAACL,KAAa,EAAEE,WAAmB;IAC1C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9BI,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEP,KAAK,CAAC;IACpCM,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEL,WAAW,CAAC;EACpD;EAEAM,cAAcA,CAACC,EAAU;IACrB,IAAI,CAAC1B,gBAAgB,CAAC2B,aAAa,CAACD,EAAE,CAAC;IACvCH,YAAY,CAACC,OAAO,CAAC,IAAI,EAAEE,EAAE,CAAC;EAClC;EAEAE,cAAcA,CAAA;IACV,IAAI,CAACjB,KAAK,EAAE;IACZY,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,IAAI,CAACb,KAAK,CAACkB,QAAQ,EAAE,CAAC;IACpD,IAAI,CAAC5B,iBAAiB,CAAC6B,cAAc,CAAC,IAAI,CAACnB,KAAK,CAAC;EACrD;EAEAoB,cAAcA,CAAA;IACV,IAAI,CAACpB,KAAK,EAAE;IACZY,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,IAAI,CAACb,KAAK,CAACkB,QAAQ,EAAE,CAAC;IACpD,IAAI,CAAC5B,iBAAiB,CAAC6B,cAAc,CAAC,IAAI,CAACnB,KAAK,CAAC;EACrD;CACH;AAjHYqB,UAAA,EAARrC,KAAK,EAAE,C,kDAA0B;AADzBC,kBAAkB,GAAAoC,UAAA,EAJ9BtC,SAAS,CAAC;EACPuC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE;CAChB,CAAC,C,EACWtC,kBAAkB,CAkH9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}