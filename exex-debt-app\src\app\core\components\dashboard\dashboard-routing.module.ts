import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CustomerComponent } from './customer/customer.component';
import { UserComponent } from './user/user.component';
import { InvoiceComponent } from './invoice/invoice.component';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: 'user',
                component: UserComponent,
            },
            {
                path: 'customer',
                component: CustomerComponent,
            },
            {
                path: 'invoice',
                component: InvoiceComponent,
            },
            { path: '**', redirectTo: '/notfound' },
        ]),
    ],
    exports: [RouterModule],
})
export class DashboardsRoutingModule {}
