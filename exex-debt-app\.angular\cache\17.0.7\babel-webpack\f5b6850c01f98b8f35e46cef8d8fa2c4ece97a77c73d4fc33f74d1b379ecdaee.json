{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class LoggingInterceptor {\n  intercept(request, next) {\n    return next.handle(request).pipe(tap(event => {\n      if (event instanceof HttpResponse) {\n        console.log('HTTP Response:', event);\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function LoggingInterceptor_Factory(t) {\n    return new (t || LoggingInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoggingInterceptor,\n    factory: LoggingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpResponse", "tap", "LoggingInterceptor", "intercept", "request", "next", "handle", "pipe", "event", "console", "log", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\logging.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpResponse } from '@angular/common/http';\r\nimport { tap } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class LoggingInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {\r\n        return next.handle(request).pipe(\r\n            tap((event) => {\r\n                if (event instanceof HttpResponse) {\r\n                    console.log('HTTP Response:', event);\r\n                }\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAoDA,YAAY,QAAQ,sBAAsB;AAC9F,SAASC,GAAG,QAAQ,gBAAgB;;AAGpC,OAAM,MAAOC,kBAAkB;EAC3BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BN,GAAG,CAAEO,KAAK,IAAI;MACV,IAAIA,KAAK,YAAYR,YAAY,EAAE;QAC/BS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;;IAE5C,CAAC,CAAC,CACL;EACL;EAAC,QAAAG,CAAA,G;qBATQT,kBAAkB;EAAA;EAAA,QAAAU,EAAA,G;WAAlBV,kBAAkB;IAAAW,OAAA,EAAlBX,kBAAkB,CAAAY;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}