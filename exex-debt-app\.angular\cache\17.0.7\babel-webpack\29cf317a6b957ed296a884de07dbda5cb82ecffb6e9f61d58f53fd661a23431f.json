{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"../core/service/auth.service\";\nimport * as i3 from \"primeng/image\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"topbarmenubutton\"];\nconst _c2 = [\"topbarmenu\"];\nexport class AppTopBarComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n  }\n  signout() {\n    this.authService.logout();\n  }\n  static #_ = this.ɵfac = function AppTopBarComponent_Factory(t) {\n    return new (t || AppTopBarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppTopBarComponent,\n    selectors: [[\"app-topbar\"]],\n    viewQuery: function AppTopBarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.topbarMenuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n      }\n    },\n    decls: 10,\n    vars: 0,\n    consts: [[1, \"layout-topbar\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\"], [1, \"p-link\", \"layout-topbar-button\", 3, \"click\"], [\"menubutton\", \"\"], [1, \"pi\", \"pi-bars\"], [\"src\", \"assets/images/exex.jpg\", \"alt\", \"Image\", \"width\", \"100\"], [1, \"pi\", \"pi-fw\", \"pi-sign-in\"]],\n    template: function AppTopBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2, 3);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_2_listener() {\n          return ctx.layoutService.onMenuToggle();\n        });\n        i0.ɵɵelement(4, \"i\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"p-image\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_6_listener() {\n          return ctx.signout();\n        });\n        i0.ɵɵelement(7, \"i\", 6);\n        i0.ɵɵelementStart(8, \"span\");\n        i0.ɵɵtext(9, \"SignOut\");\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [i3.Image],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppTopBarComponent", "constructor", "layoutService", "authService", "signout", "logout", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "viewQuery", "AppTopBarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppTopBarComponent_Template_button_click_2_listener", "onMenuToggle", "ɵɵelement", "ɵɵelementEnd", "AppTopBarComponent_Template_button_click_6_listener", "ɵɵtext"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.topbar.component.ts"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { LayoutService } from './app.layout.service';\r\nimport { AuthService } from '../core/service/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    template: `<div class=\"layout-topbar justify-content-between\">\r\n        <div class=\"flex align-items-center\">\r\n            <button #menubutton class=\"p-link layout-topbar-button\" (click)=\"layoutService.onMenuToggle()\">\r\n                <i class=\"pi pi-bars\"></i>\r\n            </button>\r\n            <p-image src=\"assets/images/exex.jpg\" alt=\"Image\" width=\"100\" />\r\n        </div>\r\n        <button class=\"p-link layout-topbar-button\" (click)=\"signout()\">\r\n            <i class=\"pi pi-fw pi-sign-in\"></i>\r\n            <span>SignOut</span>\r\n        </button>\r\n    </div>`,\r\n})\r\nexport class AppTopBarComponent {\r\n    items!: MenuItem[];\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenu') menu!: ElementRef;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {}\r\n\r\n    signout() {\r\n        this.authService.logout();\r\n    }\r\n}\r\n"], "mappings": ";;;;;;;AAoBA,OAAM,MAAOA,kBAAkB;EAS3BC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;EACpB;EAEHC,OAAOA,CAAA;IACH,IAAI,CAACD,WAAW,CAACE,MAAM,EAAE;EAC7B;EAAC,QAAAC,CAAA,G;qBAhBQN,kBAAkB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBb,kBAAkB;IAAAc,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;QAbhBV,EAAA,CAAAY,cAAA,aAAmD;QAEEZ,EAAA,CAAAa,UAAA,mBAAAC,oDAAA;UAAA,OAASH,GAAA,CAAAhB,aAAA,CAAAoB,YAAA,EAA4B;QAAA,EAAC;QAC1Ff,EAAA,CAAAgB,SAAA,WAA0B;QAC9BhB,EAAA,CAAAiB,YAAA,EAAS;QACTjB,EAAA,CAAAgB,SAAA,iBAAgE;QACpEhB,EAAA,CAAAiB,YAAA,EAAM;QACNjB,EAAA,CAAAY,cAAA,gBAAgE;QAApBZ,EAAA,CAAAa,UAAA,mBAAAK,oDAAA;UAAA,OAASP,GAAA,CAAAd,OAAA,EAAS;QAAA,EAAC;QAC3DG,EAAA,CAAAgB,SAAA,WAAmC;QACnChB,EAAA,CAAAY,cAAA,WAAM;QAAAZ,EAAA,CAAAmB,MAAA,cAAO;QAAAnB,EAAA,CAAAiB,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}