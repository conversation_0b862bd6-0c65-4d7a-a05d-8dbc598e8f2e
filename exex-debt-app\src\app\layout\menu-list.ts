import { Path } from '../core/enums/path.enum';

export const MENU_LIST = [
    {
        label: 'Dashboard',
        items: [
            {
                label: 'Customer',
                icon: 'pi pi-users',
                routerLink: [Path.DASHBOARD_CUSTOMER],
            },
            {
                label: 'Invoice',
                icon: 'pi pi-file',
                routerLink: [Path.DASHBOARD_INVOICE],
            },
            {
                label: 'User',
                icon: 'pi pi-user',
                routerLink: [Path.DASHBOARD_USER],
            },
            // {
            //     label: 'Submenu 1',
            //     icon: 'pi pi-fw pi-bookmark',
            //     items: [
            //         {
            //             label: 'Submenu 1.1',
            //             icon: 'pi pi-fw pi-bookmark',
            //             items: [
            //                 {
            //                     label: 'Submenu 1.1.1',
            //                     icon: 'pi pi-fw pi-bookmark',
            //                     routerLink: ['/uikit/misc'],
            //                 },
            //                 {
            //                     label: 'Submenu 1.1.2',
            //                     icon: 'pi pi-fw pi-bookmark',
            //                 },
            //                 {
            //                     label: 'Submenu 1.1.3',
            //                     icon: 'pi pi-fw pi-bookmark',
            //                 },
            //             ],
            //         },
            //         {
            //             label: 'Submenu 1.2',
            //             icon: 'pi pi-fw pi-bookmark',
            //             items: [
            //                 {
            //                     label: 'Submenu 1.2.1',
            //                     icon: 'pi pi-fw pi-bookmark',
            //                 },
            //             ],
            //         },
            //     ],
            // },
        ],
    },
];
