{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { LocationStrategy, PathLocationStrategy } from '@angular/common';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\nimport { AppLayoutModule } from './layout/app.layout.module';\nimport { TranslocoRootModule } from './transloco-root.module';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, NotfoundComponent],\n  imports: [AppRoutingModule, AppLayoutModule, NgxSpinnerModule, BrowserAnimationsModule, BrowserModule, HttpClientModule, TranslocoRootModule, ToastModule, ConfirmDialogModule],\n  providers: [MessageService, ConfirmationService, {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: ErrorInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: LoggingInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: HeadersInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: LoadingInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: TimeoutInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: BaseUrlInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: RetryInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: OfflineModeInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: JwtRefreshInterceptor,\n    multi: true\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: RequestTimingInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "names": ["LocationStrategy", "PathLocationStrategy", "HTTP_INTERCEPTORS", "HttpClientModule", "CUSTOM_ELEMENTS_SCHEMA", "NgModule", "BrowserModule", "BrowserAnimationsModule", "NgxSpinnerModule", "AppRoutingModule", "AppComponent", "NotfoundComponent", "AuthInterceptor", "BaseUrlInterceptor", "ErrorInterceptor", "HeadersInterceptor", "JwtRefreshInterceptor", "LoadingInterceptor", "LoggingInterceptor", "OfflineModeInterceptor", "RequestTimingInterceptor", "RetryInterceptor", "TimeoutInterceptor", "AppLayoutModule", "TranslocoRootModule", "ToastModule", "ConfirmationService", "MessageService", "ConfirmDialogModule", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap", "schemas"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\app.module.ts"], "sourcesContent": ["import { LocationStrategy, PathLocationStrategy } from '@angular/common';\r\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\r\nimport { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { NgxSpinnerModule } from 'ngx-spinner';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\r\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\r\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\r\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\r\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\r\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\r\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\r\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\r\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\r\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\r\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\r\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\r\nimport { AppLayoutModule } from './layout/app.layout.module';\r\nimport { TranslocoRootModule } from './transloco-root.module';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\n\r\n@NgModule({\r\n    declarations: [AppComponent, NotfoundComponent],\r\n    imports: [\r\n        AppRoutingModule,\r\n        AppLayoutModule,\r\n        NgxSpinnerModule,\r\n        BrowserAnimationsModule,\r\n        BrowserModule,\r\n        HttpClientModule,\r\n        TranslocoRootModule,\r\n        ToastModule,\r\n        ConfirmDialogModule,\r\n    ],\r\n    providers: [\r\n        MessageService,\r\n        ConfirmationService,\r\n        { provide: LocationStrategy, useClass: PathLocationStrategy },\r\n        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\r\n        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: LoggingInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: HeadersInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: LoadingInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: TimeoutInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: BaseUrlInterceptor,\r\n            multi: true,\r\n        },\r\n        { provide: HTTP_INTERCEPTORS, useClass: RetryInterceptor, multi: true },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: OfflineModeInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: JwtRefreshInterceptor,\r\n            multi: true,\r\n        },\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: RequestTimingInterceptor,\r\n            multi: true,\r\n        },\r\n    ],\r\n    bootstrap: [AppComponent],\r\n    schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class AppModule {}\r\n"], "mappings": ";AAAA,SAASA,gBAAgB,EAAEC,oBAAoB,QAAQ,iBAAiB;AACxE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,sBAAsB,EAAEC,QAAQ,QAAQ,eAAe;AAChE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,mBAAmB,QAAQ,uBAAuB;AAkEpD,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAG;AAAZA,SAAS,GAAAC,UAAA,EAhErBzB,QAAQ,CAAC;EACN0B,YAAY,EAAE,CAACrB,YAAY,EAAEC,iBAAiB,CAAC;EAC/CqB,OAAO,EAAE,CACLvB,gBAAgB,EAChBc,eAAe,EACff,gBAAgB,EAChBD,uBAAuB,EACvBD,aAAa,EACbH,gBAAgB,EAChBqB,mBAAmB,EACnBC,WAAW,EACXG,mBAAmB,CACtB;EACDK,SAAS,EAAE,CACPN,cAAc,EACdD,mBAAmB,EACnB;IAAEQ,OAAO,EAAElC,gBAAgB;IAAEmC,QAAQ,EAAElC;EAAoB,CAAE,EAC7D;IAAEiC,OAAO,EAAEhC,iBAAiB;IAAEiC,QAAQ,EAAEvB,eAAe;IAAEwB,KAAK,EAAE;EAAI,CAAE,EACtE;IAAEF,OAAO,EAAEhC,iBAAiB;IAAEiC,QAAQ,EAAErB,gBAAgB;IAAEsB,KAAK,EAAE;EAAI,CAAE,EACvE;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEjB,kBAAkB;IAC5BkB,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEpB,kBAAkB;IAC5BqB,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAElB,kBAAkB;IAC5BmB,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEb,kBAAkB;IAC5Bc,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEtB,kBAAkB;IAC5BuB,KAAK,EAAE;GACV,EACD;IAAEF,OAAO,EAAEhC,iBAAiB;IAAEiC,QAAQ,EAAEd,gBAAgB;IAAEe,KAAK,EAAE;EAAI,CAAE,EACvE;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEhB,sBAAsB;IAChCiB,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEnB,qBAAqB;IAC/BoB,KAAK,EAAE;GACV,EACD;IACIF,OAAO,EAAEhC,iBAAiB;IAC1BiC,QAAQ,EAAEf,wBAAwB;IAClCgB,KAAK,EAAE;GACV,CACJ;EACDC,SAAS,EAAE,CAAC3B,YAAY,CAAC;EACzB4B,OAAO,EAAE,CAAClC,sBAAsB;CACnC,CAAC,C,EACWyB,SAAS,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}