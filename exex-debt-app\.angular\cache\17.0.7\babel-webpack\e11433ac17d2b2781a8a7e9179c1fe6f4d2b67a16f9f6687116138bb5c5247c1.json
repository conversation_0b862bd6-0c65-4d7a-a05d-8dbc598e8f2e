{"ast": null, "code": "export const INVOICE_COLS = [{\n  field: 'invoiceId',\n  title: 'Invoice ID',\n  width: '100px'\n}, {\n  field: 'customerId',\n  title: 'Customer ID',\n  width: '100px'\n}, {\n  field: 'invoiceDate',\n  title: 'Invoice Date',\n  width: '100px'\n}, {\n  field: 'dueDate',\n  title: 'Due Date',\n  width: '100px'\n}, {\n  field: 'totalAmount',\n  title: 'Total Amount',\n  width: '100px'\n}, {\n  field: 'paidAmount',\n  title: 'Paid Amount',\n  width: '100px'\n}, {\n  field: 'remainingAmount',\n  title: 'Remaining Amount',\n  width: '100px'\n}, {\n  field: 'status',\n  title: 'Status',\n  width: '100px'\n}, {\n  field: 'currencyId',\n  title: 'Currency ID',\n  width: '100px'\n}];", "map": {"version": 3, "names": ["INVOICE_COLS", "field", "title", "width"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\invoice\\invoice-cols.ts"], "sourcesContent": ["export const INVOICE_COLS = [\r\n    { field: 'invoiceId', title: 'Invoice ID', width: '100px' },\r\n    { field: 'customerId', title: 'Customer ID', width: '100px' },\r\n    { field: 'invoiceDate', title: 'Invoice Date', width: '100px' },\r\n    { field: 'dueDate', title: 'Due Date', width: '100px' },\r\n    { field: 'totalAmount', title: 'Total Amount', width: '100px' },\r\n    { field: 'paidAmount', title: 'Paid Amount', width: '100px' },\r\n    { field: 'remainingAmount', title: 'Remaining Amount', width: '100px' },\r\n    { field: 'status', title: 'Status', width: '100px' },\r\n    { field: 'currencyId', title: 'Currency ID', width: '100px' },\r\n];\r\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAG,CACxB;EAAEC,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC3D;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC7D;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAO,CAAE,EACvD;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC7D;EAAEF,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAO,CAAE,EACvE;EAAEF,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAO,CAAE,EACpD;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAO,CAAE,CAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}