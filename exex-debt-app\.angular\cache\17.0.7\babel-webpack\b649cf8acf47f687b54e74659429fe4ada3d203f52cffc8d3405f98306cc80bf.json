{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Injector, Inject, Optional, Component, Input, TemplateRef, DestroyRef, ChangeDetectorRef, ElementRef, ViewContainerRef, Renderer2, Directive, Pipe, NgModule, makeEnvironmentProviders, APP_INITIALIZER } from '@angular/core';\nimport { of, take, from, map, Subject, BehaviorSubject, forkJoin, retry, tap, catchError, shareReplay, switchMap, combineLatest, EMPTY } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { unflatten as unflatten$1, flatten as flatten$1 } from 'flat';\nclass DefaultLoader {\n  translations;\n  constructor(translations) {\n    this.translations = translations;\n  }\n  getTranslation(lang) {\n    return of(this.translations.get(lang) || {});\n  }\n}\nconst TRANSLOCO_LOADER = new InjectionToken('TRANSLOCO_LOADER');\nfunction getValue(obj, path) {\n  if (!obj) {\n    return obj;\n  }\n  /* For cases where the key is like: 'general.something.thing' */\n  if (Object.prototype.hasOwnProperty.call(obj, path)) {\n    return obj[path];\n  }\n  return path.split('.').reduce((p, c) => p?.[c], obj);\n}\nfunction setValue(obj, prop, val) {\n  obj = {\n    ...obj\n  };\n  const split = prop.split('.');\n  const lastIndex = split.length - 1;\n  split.reduce((acc, part, index) => {\n    if (index === lastIndex) {\n      acc[part] = val;\n    } else {\n      acc[part] = Array.isArray(acc[part]) ? acc[part].slice() : {\n        ...acc[part]\n      };\n    }\n    return acc && acc[part];\n  }, obj);\n  return obj;\n}\nfunction size(collection) {\n  if (!collection) {\n    return 0;\n  }\n  if (Array.isArray(collection)) {\n    return collection.length;\n  }\n  if (isObject(collection)) {\n    return Object.keys(collection).length;\n  }\n  return collection ? collection.length : 0;\n}\nfunction isEmpty(collection) {\n  return size(collection) === 0;\n}\nfunction isFunction(val) {\n  return typeof val === 'function';\n}\nfunction isString(val) {\n  return typeof val === 'string';\n}\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\nfunction isObject(item) {\n  return !!item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nfunction toCamelCase(str) {\n  return str.replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\\s+|_|-|\\//g, '');\n}\nfunction isBrowser() {\n  return typeof window !== 'undefined';\n}\nfunction isNil(value) {\n  return value === null || value === undefined;\n}\nfunction isDefined(value) {\n  return isNil(value) === false;\n}\nfunction toNumber(value) {\n  if (isNumber(value)) return value;\n  if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  return null;\n}\nfunction isScopeObject(item) {\n  return item && typeof item.scope === 'string';\n}\nfunction hasInlineLoader(item) {\n  return item && isObject(item.loader);\n}\nfunction unflatten(obj) {\n  return unflatten$1(obj);\n}\nfunction flatten(obj) {\n  return flatten$1(obj, {\n    safe: true\n  });\n}\nconst TRANSLOCO_CONFIG = new InjectionToken('TRANSLOCO_CONFIG', {\n  providedIn: 'root',\n  factory: () => defaultConfig\n});\nconst defaultConfig = {\n  defaultLang: 'en',\n  reRenderOnLangChange: false,\n  prodMode: false,\n  failedRetries: 2,\n  fallbackLang: [],\n  availableLangs: [],\n  missingHandler: {\n    logMissingKey: true,\n    useFallbackTranslation: false,\n    allowEmpty: false\n  },\n  flatten: {\n    aot: false\n  },\n  interpolation: ['{{', '}}']\n};\nfunction translocoConfig(config = {}) {\n  return {\n    ...defaultConfig,\n    ...config,\n    missingHandler: {\n      ...defaultConfig.missingHandler,\n      ...config.missingHandler\n    },\n    flatten: {\n      ...defaultConfig.flatten,\n      ...config.flatten\n    }\n  };\n}\nconst TRANSLOCO_TRANSPILER = new InjectionToken('TRANSLOCO_TRANSPILER');\nclass DefaultTranspiler {\n  config = inject(TRANSLOCO_CONFIG, {\n    optional: true\n  }) ?? defaultConfig;\n  get interpolationMatcher() {\n    return resolveMatcher(this.config);\n  }\n  transpile({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    if (isString(value)) {\n      let paramMatch;\n      let parsedValue = value;\n      while ((paramMatch = this.interpolationMatcher.exec(parsedValue)) !== null) {\n        const [match, paramValue] = paramMatch;\n        parsedValue = parsedValue.replace(match, () => {\n          const match = paramValue.trim();\n          const param = getValue(params, match);\n          if (isDefined(param)) {\n            return param;\n          }\n          return isDefined(translation[match]) ? this.transpile({\n            params,\n            translation,\n            key,\n            value: translation[match]\n          }) : '';\n        });\n      }\n      return parsedValue;\n    } else if (params) {\n      if (isObject(value)) {\n        value = this.handleObject({\n          value: value,\n          params,\n          translation,\n          key\n        });\n      } else if (Array.isArray(value)) {\n        value = this.handleArray({\n          value,\n          params,\n          translation,\n          key\n        });\n      }\n    }\n    return value;\n  }\n  /**\n   *\n   * @example\n   *\n   * const en = {\n   *  a: {\n   *    b: {\n   *      c: \"Hello {{ value }}\"\n   *    }\n   *  }\n   * }\n   *\n   * const params =  {\n   *  \"b.c\": { value: \"Transloco \"}\n   * }\n   *\n   * service.selectTranslate('a', params);\n   *\n   * // the first param will be the result of `en.a`.\n   * // the second param will be `params`.\n   * parser.transpile(value, params, {});\n   *\n   *\n   */\n  handleObject({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    let result = value;\n    Object.keys(params).forEach(p => {\n      // transpile the value => \"Hello Transloco\"\n      const transpiled = this.transpile({\n        // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n        value: getValue(result, p),\n        // get the params of \"b.c\" => { value: \"Transloco\" }\n        params: getValue(params, p),\n        translation,\n        key\n      });\n      // set \"b.c\" to `transpiled`\n      result = setValue(result, p, transpiled);\n    });\n    return result;\n  }\n  handleArray({\n    value,\n    ...rest\n  }) {\n    return value.map(v => this.transpile({\n      value: v,\n      ...rest\n    }));\n  }\n  static ɵfac = function DefaultTranspiler_Factory(t) {\n    return new (t || DefaultTranspiler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultTranspiler,\n    factory: DefaultTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction resolveMatcher(config) {\n  const [start, end] = config.interpolation;\n  return new RegExp(`${start}([^${start}${end}]*?)${end}`, 'g');\n}\nfunction getFunctionArgs(argsString) {\n  const splitted = argsString ? argsString.split(',') : [];\n  const args = [];\n  for (let i = 0; i < splitted.length; i++) {\n    let value = splitted[i].trim();\n    while (value[value.length - 1] === '\\\\') {\n      i++;\n      value = value.replace('\\\\', ',') + splitted[i];\n    }\n    args.push(value);\n  }\n  return args;\n}\nclass FunctionalTranspiler extends DefaultTranspiler {\n  injector = inject(Injector);\n  transpile({\n    value,\n    ...rest\n  }) {\n    let transpiled = value;\n    if (isString(value)) {\n      transpiled = value.replace(/\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g, (match, functionName, args) => {\n        try {\n          const func = this.injector.get(functionName);\n          return func.transpile(...getFunctionArgs(args));\n        } catch (e) {\n          let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n          if (e.message.includes('NullInjectorError')) {\n            message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n          }\n          throw new Error(message);\n        }\n      });\n    }\n    return super.transpile({\n      value: transpiled,\n      ...rest\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFunctionalTranspiler_BaseFactory;\n    return function FunctionalTranspiler_Factory(t) {\n      return (ɵFunctionalTranspiler_BaseFactory || (ɵFunctionalTranspiler_BaseFactory = i0.ɵɵgetInheritedFactory(FunctionalTranspiler)))(t || FunctionalTranspiler);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FunctionalTranspiler,\n    factory: FunctionalTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FunctionalTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_MISSING_HANDLER = new InjectionToken('TRANSLOCO_MISSING_HANDLER');\nclass DefaultMissingHandler {\n  handle(key, config) {\n    if (config.missingHandler.logMissingKey && !config.prodMode) {\n      const msg = `Missing translation for '${key}'`;\n      console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n    }\n    return key;\n  }\n  static ɵfac = function DefaultMissingHandler_Factory(t) {\n    return new (t || DefaultMissingHandler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultMissingHandler,\n    factory: DefaultMissingHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultMissingHandler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_INTERCEPTOR = new InjectionToken('TRANSLOCO_INTERCEPTOR');\nclass DefaultInterceptor {\n  preSaveTranslation(translation) {\n    return translation;\n  }\n  preSaveTranslationKey(_, value) {\n    return value;\n  }\n  static ɵfac = function DefaultInterceptor_Factory(t) {\n    return new (t || DefaultInterceptor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultInterceptor,\n    factory: DefaultInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken('TRANSLOCO_FALLBACK_STRATEGY');\nclass DefaultFallbackStrategy {\n  userConfig;\n  constructor(userConfig) {\n    this.userConfig = userConfig;\n  }\n  getNextLangs() {\n    const fallbackLang = this.userConfig.fallbackLang;\n    if (!fallbackLang) {\n      throw new Error('When using the default fallback, a fallback language must be provided in the config!');\n    }\n    return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n  }\n  static ɵfac = function DefaultFallbackStrategy_Factory(t) {\n    return new (t || DefaultFallbackStrategy)(i0.ɵɵinject(TRANSLOCO_CONFIG));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultFallbackStrategy,\n    factory: DefaultFallbackStrategy.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultFallbackStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }], null);\n})();\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nfunction getScopeFromLang(lang) {\n  if (!lang) {\n    return '';\n  }\n  const split = lang.split('/');\n  split.pop();\n  return split.join('/');\n}\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nfunction getLangFromScope(lang) {\n  if (!lang) {\n    return '';\n  }\n  return lang.split('/').pop();\n}\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nfunction getPipeValue(str, value, char = '|') {\n  if (isString(str)) {\n    const splitted = str.split(char);\n    const lastItem = splitted.pop();\n    return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n  }\n  return [false, ''];\n}\nfunction shouldListenToLangChanges(service, lang) {\n  const [hasStatic] = getPipeValue(lang, 'static');\n  if (!hasStatic) {\n    // If we didn't get 'lang|static' check if it's set in the global level\n    return !!service.config.reRenderOnLangChange;\n  }\n  // We have 'lang|static' so don't listen to lang changes\n  return false;\n}\nfunction listenOrNotOperator(listenToLangChange) {\n  return listenToLangChange ? source => source : take(1);\n}\nfunction prependScope(inlineLoader, scope) {\n  return Object.keys(inlineLoader).reduce((acc, lang) => {\n    acc[`${scope}/${lang}`] = inlineLoader[lang];\n    return acc;\n  }, {});\n}\nfunction resolveInlineLoader(providerScope, scope) {\n  return hasInlineLoader(providerScope) ? prependScope(providerScope.loader, scope) : undefined;\n}\nfunction getEventPayload(lang) {\n  return {\n    scope: getScopeFromLang(lang) || null,\n    langName: getLangFromScope(lang)\n  };\n}\nfunction resolveLoader(options) {\n  const {\n    path,\n    inlineLoader,\n    mainLoader,\n    data\n  } = options;\n  if (inlineLoader) {\n    const pathLoader = inlineLoader[path];\n    if (isFunction(pathLoader) === false) {\n      throw `You're using an inline loader but didn't provide a loader for ${path}`;\n    }\n    return inlineLoader[path]().then(res => res.default ? res.default : res);\n  }\n  return mainLoader.getTranslation(path, data);\n}\nfunction getFallbacksLoaders({\n  mainLoader,\n  path,\n  data,\n  fallbackPath,\n  inlineLoader\n}) {\n  const paths = fallbackPath ? [path, fallbackPath] : [path];\n  return paths.map(path => {\n    const loader = resolveLoader({\n      path,\n      mainLoader,\n      inlineLoader,\n      data\n    });\n    return from(loader).pipe(map(translation => ({\n      translation,\n      lang: path\n    })));\n  });\n}\nlet service;\nfunction translate(key, params = {}, lang) {\n  return service.translate(key, params, lang);\n}\nfunction translateObject(key, params = {}, lang) {\n  return service.translateObject(key, params, lang);\n}\nclass TranslocoService {\n  loader;\n  parser;\n  missingHandler;\n  interceptor;\n  fallbackStrategy;\n  langChanges$;\n  translations = new Map();\n  cache = new Map();\n  firstFallbackLang;\n  defaultLang = '';\n  availableLangs = [];\n  isResolvedMissingOnce = false;\n  lang;\n  failedLangs = new Set();\n  events = new Subject();\n  events$ = this.events.asObservable();\n  config;\n  constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {\n    this.loader = loader;\n    this.parser = parser;\n    this.missingHandler = missingHandler;\n    this.interceptor = interceptor;\n    this.fallbackStrategy = fallbackStrategy;\n    if (!this.loader) {\n      this.loader = new DefaultLoader(this.translations);\n    }\n    service = this;\n    this.config = JSON.parse(JSON.stringify(userConfig));\n    this.setAvailableLangs(this.config.availableLangs || []);\n    this.setFallbackLangForMissingTranslation(this.config);\n    this.setDefaultLang(this.config.defaultLang);\n    this.lang = new BehaviorSubject(this.getDefaultLang());\n    // Don't use distinctUntilChanged as we need the ability to update\n    // the value when using setTranslation or setTranslationKeys\n    this.langChanges$ = this.lang.asObservable();\n    /**\n     * When we have a failure, we want to define the next language that succeeded as the active\n     */\n    this.events$.pipe(takeUntilDestroyed()).subscribe(e => {\n      if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n        this.setActiveLang(e.payload.langName);\n      }\n    });\n  }\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  setDefaultLang(lang) {\n    this.defaultLang = lang;\n  }\n  getActiveLang() {\n    return this.lang.getValue();\n  }\n  setActiveLang(lang) {\n    this.parser.onLangChanged?.(lang);\n    this.lang.next(lang);\n    this.events.next({\n      type: 'langChanged',\n      payload: getEventPayload(lang)\n    });\n    return this;\n  }\n  setAvailableLangs(langs) {\n    this.availableLangs = langs;\n  }\n  /**\n   * Gets the available languages.\n   *\n   * @returns\n   * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n   * depending on how the available languages are set in your module.\n   */\n  getAvailableLangs() {\n    return this.availableLangs;\n  }\n  load(path, options = {}) {\n    const cached = this.cache.get(path);\n    if (cached) {\n      return cached;\n    }\n    let loadTranslation;\n    const isScope = this._isLangScoped(path);\n    let scope;\n    if (isScope) {\n      scope = getScopeFromLang(path);\n    }\n    const loadersOptions = {\n      path,\n      mainLoader: this.loader,\n      inlineLoader: options.inlineLoader,\n      data: isScope ? {\n        scope: scope\n      } : undefined\n    };\n    if (this.useFallbackTranslation(path)) {\n      // if the path is scope the fallback should be `scope/fallbackLang`;\n      const fallback = isScope ? `${scope}/${this.firstFallbackLang}` : this.firstFallbackLang;\n      const loaders = getFallbacksLoaders({\n        ...loadersOptions,\n        fallbackPath: fallback\n      });\n      loadTranslation = forkJoin(loaders);\n    } else {\n      const loader = resolveLoader(loadersOptions);\n      loadTranslation = from(loader);\n    }\n    const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap(translation => {\n      if (Array.isArray(translation)) {\n        translation.forEach(t => {\n          this.handleSuccess(t.lang, t.translation);\n          // Save the fallback in cache so we'll not create a redundant request\n          if (t.lang !== path) {\n            this.cache.set(t.lang, of({}));\n          }\n        });\n        return;\n      }\n      this.handleSuccess(path, translation);\n    }), catchError(error => {\n      if (!this.config.prodMode) {\n        console.error(`Error while trying to load \"${path}\"`, error);\n      }\n      return this.handleFailure(path, options);\n    }), shareReplay(1));\n    this.cache.set(path, load$);\n    return load$;\n  }\n  /**\n   * Gets the instant translated value of a key\n   *\n   * @example\n   *\n   * translate<string>('hello')\n   * translate('hello', { value: 'value' })\n   * translate<string[]>(['hello', 'key'])\n   * translate('hello', { }, 'en')\n   * translate('scope.someKey', { }, 'en')\n   */\n  translate(key, params = {}, lang = this.getActiveLang()) {\n    if (!key) return key;\n    const {\n      scope,\n      resolveLang\n    } = this.resolveLangAndScope(lang);\n    if (Array.isArray(key)) {\n      return key.map(k => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));\n    }\n    key = scope ? `${scope}.${key}` : key;\n    const translation = this.getTranslation(resolveLang);\n    const value = translation[key];\n    if (!value) {\n      return this._handleMissingKey(key, value, params);\n    }\n    return this.parser.transpile({\n      value,\n      params,\n      translation,\n      key\n    });\n  }\n  /**\n   * Gets the translated value of a key as observable\n   *\n   * @example\n   *\n   * selectTranslate<string>('hello').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n   *\n   */\n  selectTranslate(key, params, lang, _isObject = false) {\n    let inlineLoader;\n    const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject ? this.translateObject(key, params, lang) : this.translate(key, params, lang)));\n    if (isNil(lang)) {\n      return this.langChanges$.pipe(switchMap(lang => load(lang)));\n    }\n    lang = Array.isArray(lang) ? lang[0] : lang;\n    if (isScopeObject(lang)) {\n      // it's a scope object.\n      const providerScope = lang;\n      lang = providerScope.scope;\n      inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n    }\n    lang = lang;\n    if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n      return load(lang);\n    }\n    // it's a scope\n    const scope = lang;\n    return this.langChanges$.pipe(switchMap(lang => load(`${scope}/${lang}`, {\n      inlineLoader\n    })));\n  }\n  /**\n   * Whether the scope with lang\n   *\n   * @example\n   *\n   * todos/en => true\n   * todos => false\n   */\n  isScopeWithLang(lang) {\n    return this.isLang(getLangFromScope(lang));\n  }\n  translateObject(key, params = {}, lang = this.getActiveLang()) {\n    if (isString(key) || Array.isArray(key)) {\n      const {\n        resolveLang,\n        scope\n      } = this.resolveLangAndScope(lang);\n      if (Array.isArray(key)) {\n        return key.map(k => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));\n      }\n      const translation = this.getTranslation(resolveLang);\n      key = scope ? `${scope}.${key}` : key;\n      const value = unflatten(this.getObjectByKey(translation, key));\n      /* If an empty object was returned we want to try and translate the key as a string and not an object */\n      return isEmpty(value) ? this.translate(key, params, lang) : this.parser.transpile({\n        value,\n        params: params,\n        translation,\n        key\n      });\n    }\n    const translations = [];\n    for (const [_key, _params] of this.getEntries(key)) {\n      translations.push(this.translateObject(_key, _params, lang));\n    }\n    return translations;\n  }\n  selectTranslateObject(key, params, lang) {\n    if (isString(key) || Array.isArray(key)) {\n      return this.selectTranslate(key, params, lang, true);\n    }\n    const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n    /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n     * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n    return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map(value => {\n      const translations = [value];\n      for (const [_key, _params] of rest) {\n        translations.push(this.translateObject(_key, _params, lang));\n      }\n      return translations;\n    }));\n  }\n  getTranslation(langOrScope) {\n    if (langOrScope) {\n      if (this.isLang(langOrScope)) {\n        return this.translations.get(langOrScope) || {};\n      } else {\n        // This is a scope, build the scope value from the translation object\n        const {\n          scope,\n          resolveLang\n        } = this.resolveLangAndScope(langOrScope);\n        const translation = this.translations.get(resolveLang) || {};\n        return this.getObjectByKey(translation, scope);\n      }\n    }\n    return this.translations;\n  }\n  /**\n   * Gets an object of translations for a given language\n   *\n   * @example\n   *\n   * selectTranslation().subscribe() - will return the current lang translation\n   * selectTranslation('es').subscribe()\n   * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n   * selectTranslation('admin-page/es').subscribe()\n   */\n  selectTranslation(lang) {\n    let language$ = this.langChanges$;\n    if (lang) {\n      const scopeLangSpecified = getLangFromScope(lang) !== lang;\n      if (this.isLang(lang) || scopeLangSpecified) {\n        language$ = of(lang);\n      } else {\n        language$ = this.langChanges$.pipe(map(currentLang => `${lang}/${currentLang}`));\n      }\n    }\n    return language$.pipe(switchMap(language => this.load(language).pipe(map(() => this.getTranslation(language)))));\n  }\n  /**\n   * Sets or merge a given translation object to current lang\n   *\n   * @example\n   *\n   * setTranslation({ ... })\n   * setTranslation({ ... }, 'en')\n   * setTranslation({ ... }, 'es', { merge: false } )\n   * setTranslation({ ... }, 'todos/en', { merge: false } )\n   */\n  setTranslation(translation, lang = this.getActiveLang(), options = {}) {\n    const defaults = {\n      merge: true,\n      emitChange: true\n    };\n    const mergedOptions = {\n      ...defaults,\n      ...options\n    };\n    const scope = getScopeFromLang(lang);\n    /**\n     * If this isn't a scope we use the whole translation as is\n     * otherwise we need to flat the scope and use it\n     */\n    let flattenScopeOrTranslation = translation;\n    // Merged the scoped language into the active language\n    if (scope) {\n      const key = this.getMappedScope(scope);\n      flattenScopeOrTranslation = flatten({\n        [key]: translation\n      });\n    }\n    const currentLang = scope ? getLangFromScope(lang) : lang;\n    const mergedTranslation = {\n      ...(mergedOptions.merge && this.getTranslation(currentLang)),\n      ...flattenScopeOrTranslation\n    };\n    const flattenTranslation = this.config.flatten.aot ? mergedTranslation : flatten(mergedTranslation);\n    const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);\n    this.translations.set(currentLang, withHook);\n    mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n  }\n  /**\n   * Sets translation key with given value\n   *\n   * @example\n   *\n   * setTranslationKey('key', 'value')\n   * setTranslationKey('key.nested', 'value')\n   * setTranslationKey('key.nested', 'value', 'en')\n   * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n   */\n  setTranslationKey(key, value, options = {}) {\n    const lang = options.lang || this.getActiveLang();\n    const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n    const newValue = {\n      [key]: withHook\n    };\n    this.setTranslation(newValue, lang, {\n      ...options,\n      merge: true\n    });\n  }\n  /**\n   * Sets the fallback lang for the currently active language\n   * @param fallbackLang\n   */\n  setFallbackLangForMissingTranslation({\n    fallbackLang\n  }) {\n    const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n    if (fallbackLang && this.useFallbackTranslation(lang)) {\n      this.firstFallbackLang = lang;\n    }\n  }\n  /**\n   * @internal\n   */\n  _handleMissingKey(key, value, params) {\n    if (this.config.missingHandler.allowEmpty && value === '') {\n      return '';\n    }\n    if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n      // We need to set it to true to prevent a loop\n      this.isResolvedMissingOnce = true;\n      const fallbackValue = this.translate(key, params, this.firstFallbackLang);\n      this.isResolvedMissingOnce = false;\n      return fallbackValue;\n    }\n    return this.missingHandler.handle(key, this.getMissingHandlerData(), params);\n  }\n  /**\n   * @internal\n   */\n  _isLangScoped(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) === -1;\n  }\n  /**\n   * Checks if a given string is one of the specified available languages.\n   * @returns\n   * True if the given string is an available language.\n   * False if the given string is not an available language.\n   */\n  isLang(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) !== -1;\n  }\n  /**\n   * @internal\n   *\n   * We always want to make sure the global lang is loaded\n   * before loading the scope since you can access both via the pipe/directive.\n   */\n  _loadDependencies(path, inlineLoader) {\n    const mainLang = getLangFromScope(path);\n    if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n      return combineLatest([this.load(mainLang), this.load(path, {\n        inlineLoader\n      })]);\n    }\n    return this.load(path, {\n      inlineLoader\n    });\n  }\n  /**\n   * @internal\n   */\n  _completeScopeWithLang(langOrScope) {\n    if (this._isLangScoped(langOrScope) && !this.isLang(getLangFromScope(langOrScope))) {\n      return `${langOrScope}/${this.getActiveLang()}`;\n    }\n    return langOrScope;\n  }\n  /**\n   * @internal\n   */\n  _setScopeAlias(scope, alias) {\n    if (!this.config.scopeMapping) {\n      this.config.scopeMapping = {};\n    }\n    this.config.scopeMapping[scope] = alias;\n  }\n  ngOnDestroy() {\n    // Caretaker note: since this is the root provider, it'll be destroyed when the `NgModuleRef.destroy()` is run.\n    // Cached values capture `this`, thus leading to a circular reference and preventing the `TranslocoService` from\n    // being GC'd. This would lead to a memory leak when server-side rendering is used since the service is created\n    // and destroyed per each HTTP request, but any service is not getting GC'd.\n    this.cache.clear();\n  }\n  isLoadedTranslation(lang) {\n    return size(this.getTranslation(lang));\n  }\n  getAvailableLangsIds() {\n    const first = this.getAvailableLangs()[0];\n    if (isString(first)) {\n      return this.getAvailableLangs();\n    }\n    return this.getAvailableLangs().map(l => l.id);\n  }\n  getMissingHandlerData() {\n    return {\n      ...this.config,\n      activeLang: this.getActiveLang(),\n      availableLangs: this.availableLangs,\n      defaultLang: this.defaultLang\n    };\n  }\n  /**\n   * Use a fallback translation set for missing keys of the primary language\n   * This is unrelated to the fallback language (which changes the active language)\n   */\n  useFallbackTranslation(lang) {\n    return this.config.missingHandler.useFallbackTranslation && lang !== this.firstFallbackLang;\n  }\n  handleSuccess(lang, translation) {\n    this.setTranslation(translation, lang, {\n      emitChange: false\n    });\n    this.events.next({\n      wasFailure: !!this.failedLangs.size,\n      type: 'translationLoadSuccess',\n      payload: getEventPayload(lang)\n    });\n    this.failedLangs.forEach(l => this.cache.delete(l));\n    this.failedLangs.clear();\n  }\n  handleFailure(lang, loadOptions) {\n    // When starting to load a first choice language, initialize\n    // the failed counter and resolve the fallback langs.\n    if (isNil(loadOptions.failedCounter)) {\n      loadOptions.failedCounter = 0;\n      if (!loadOptions.fallbackLangs) {\n        loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n      }\n    }\n    const splitted = lang.split('/');\n    const fallbacks = loadOptions.fallbackLangs;\n    const nextLang = fallbacks[loadOptions.failedCounter];\n    this.failedLangs.add(lang);\n    // This handles the case where a loaded fallback language is requested again\n    if (this.cache.has(nextLang)) {\n      this.handleSuccess(nextLang, this.getTranslation(nextLang));\n      return EMPTY;\n    }\n    const isFallbackLang = nextLang === splitted[splitted.length - 1];\n    if (!nextLang || isFallbackLang) {\n      let msg = `Unable to load translation and all the fallback languages`;\n      if (splitted.length > 1) {\n        msg += `, did you misspelled the scope name?`;\n      }\n      throw new Error(msg);\n    }\n    let resolveLang = nextLang;\n    // if it's scoped lang\n    if (splitted.length > 1) {\n      // We need to resolve it to:\n      // todos/langNotExists => todos/nextLang\n      splitted[splitted.length - 1] = nextLang;\n      resolveLang = splitted.join('/');\n    }\n    loadOptions.failedCounter++;\n    this.events.next({\n      type: 'translationLoadFailure',\n      payload: getEventPayload(lang)\n    });\n    return this.load(resolveLang, loadOptions);\n  }\n  getMappedScope(scope) {\n    const {\n      scopeMapping = {}\n    } = this.config;\n    return scopeMapping[scope] || toCamelCase(scope);\n  }\n  /**\n   * If lang is scope we need to check the following cases:\n   * todos/es => in this case we should take `es` as lang\n   * todos => in this case we should set the active lang as lang\n   */\n  resolveLangAndScope(lang) {\n    let resolveLang = lang;\n    let scope;\n    if (this._isLangScoped(lang)) {\n      // en for example\n      const langFromScope = getLangFromScope(lang);\n      // en is lang\n      const hasLang = this.isLang(langFromScope);\n      // take en\n      resolveLang = hasLang ? langFromScope : this.getActiveLang();\n      // find the scope\n      scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n    }\n    return {\n      scope,\n      resolveLang\n    };\n  }\n  getObjectByKey(translation, key) {\n    const result = {};\n    const prefix = `${key}.`;\n    for (const currentKey in translation) {\n      if (currentKey.startsWith(prefix)) {\n        result[currentKey.replace(prefix, '')] = translation[currentKey];\n      }\n    }\n    return result;\n  }\n  getEntries(key) {\n    return key instanceof Map ? key.entries() : Object.entries(key);\n  }\n  static ɵfac = function TranslocoService_Factory(t) {\n    return new (t || TranslocoService)(i0.ɵɵinject(TRANSLOCO_LOADER, 8), i0.ɵɵinject(TRANSLOCO_TRANSPILER), i0.ɵɵinject(TRANSLOCO_MISSING_HANDLER), i0.ɵɵinject(TRANSLOCO_INTERCEPTOR), i0.ɵɵinject(TRANSLOCO_CONFIG), i0.ɵɵinject(TRANSLOCO_FALLBACK_STRATEGY));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslocoService,\n    factory: TranslocoService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LOADER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TRANSPILER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_MISSING_HANDLER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_INTERCEPTOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_FALLBACK_STRATEGY]\n    }]\n  }], null);\n})();\nclass TranslocoLoaderComponent {\n  html;\n  static ɵfac = function TranslocoLoaderComponent_Factory(t) {\n    return new (t || TranslocoLoaderComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TranslocoLoaderComponent,\n    selectors: [[\"ng-component\"]],\n    inputs: {\n      html: \"html\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"transloco-loader-template\", 3, \"innerHTML\"]],\n    template: function TranslocoLoaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"innerHTML\", ctx.html, i0.ɵɵsanitizeHtml);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoLoaderComponent, [{\n    type: Component,\n    args: [{\n      template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    html: [{\n      type: Input\n    }]\n  });\n})();\nclass TemplateHandler {\n  view;\n  vcr;\n  constructor(view, vcr) {\n    this.view = view;\n    this.vcr = vcr;\n  }\n  attachView() {\n    if (this.view instanceof TemplateRef) {\n      this.vcr.createEmbeddedView(this.view);\n    } else if (isString(this.view)) {\n      const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n      componentRef.instance.html = this.view;\n      componentRef.hostView.detectChanges();\n    } else {\n      this.vcr.createComponent(this.view);\n    }\n  }\n  detachView() {\n    this.vcr.clear();\n  }\n}\nconst TRANSLOCO_LANG = new InjectionToken('TRANSLOCO_LANG');\nconst TRANSLOCO_LOADING_TEMPLATE = new InjectionToken('TRANSLOCO_LOADING_TEMPLATE');\nconst TRANSLOCO_SCOPE = new InjectionToken('TRANSLOCO_SCOPE');\nclass LangResolver {\n  initialized = false;\n  // inline => provider => active\n  resolve({\n    inline,\n    provider,\n    active\n  }) {\n    let lang = active;\n    /**\n     * When the user changes the lang we need to update\n     * the view. Otherwise, the lang will remain the inline/provided lang\n     */\n    if (this.initialized) {\n      lang = active;\n      return lang;\n    }\n    if (provider) {\n      const [, extracted] = getPipeValue(provider, 'static');\n      lang = extracted;\n    }\n    if (inline) {\n      const [, extracted] = getPipeValue(inline, 'static');\n      lang = extracted;\n    }\n    this.initialized = true;\n    return lang;\n  }\n  /**\n   *\n   * Resolve the lang\n   *\n   * @example\n   *\n   * resolveLangBasedOnScope('todos/en') => en\n   * resolveLangBasedOnScope('en') => en\n   *\n   */\n  resolveLangBasedOnScope(lang) {\n    const scope = getScopeFromLang(lang);\n    return scope ? getLangFromScope(lang) : lang;\n  }\n  /**\n   *\n   * Resolve the lang path for loading\n   *\n   * @example\n   *\n   * resolveLangPath('todos', 'en') => todos/en\n   * resolveLangPath('en') => en\n   *\n   */\n  resolveLangPath(lang, scope) {\n    return scope ? `${scope}/${lang}` : lang;\n  }\n}\nclass ScopeResolver {\n  service;\n  constructor(service) {\n    this.service = service;\n  }\n  // inline => provider\n  resolve(params) {\n    const {\n      inline,\n      provider\n    } = params;\n    if (inline) {\n      return inline;\n    }\n    if (provider) {\n      if (isScopeObject(provider)) {\n        const {\n          scope,\n          alias = toCamelCase(scope)\n        } = provider;\n        this.service._setScopeAlias(scope, alias);\n        return scope;\n      }\n      return provider;\n    }\n    return undefined;\n  }\n}\nclass TranslocoDirective {\n  destroyRef = inject(DestroyRef);\n  service = inject(TranslocoService);\n  tpl = inject(TemplateRef, {\n    optional: true\n  });\n  providerLang = inject(TRANSLOCO_LANG, {\n    optional: true\n  });\n  providerScope = inject(TRANSLOCO_SCOPE, {\n    optional: true\n  });\n  providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n    optional: true\n  });\n  cdr = inject(ChangeDetectorRef);\n  host = inject(ElementRef);\n  vcr = inject(ViewContainerRef);\n  renderer = inject(Renderer2);\n  view;\n  memo = new Map();\n  key;\n  params = {};\n  inlineScope;\n  /** @deprecated use prefix instead, will be removed in Transloco v8 */\n  inlineRead;\n  prefix;\n  inlineLang;\n  inlineTpl;\n  currentLang;\n  loaderTplHandler;\n  // Whether we already rendered the view once\n  initialized = false;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver = new ScopeResolver(this.service);\n  strategy = this.tpl === null ? 'attribute' : 'structural';\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  ngOnInit() {\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);\n    this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: this.inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);\n      this.strategy === 'attribute' ? this.attributeStrategy() : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);\n      this.cdr.markForCheck();\n      this.initialized = true;\n    });\n    if (!this.initialized) {\n      const loadingContent = this.resolveLoadingContent();\n      if (loadingContent) {\n        this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n        this.loaderTplHandler.attachView();\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    // We need to support dynamic keys/params, so if this is not the first change CD cycle\n    // we need to run the function again in order to update the value\n    if (this.strategy === 'attribute') {\n      const notInit = Object.keys(changes).some(v => !changes[v].firstChange);\n      notInit && this.attributeStrategy();\n    }\n  }\n  attributeStrategy() {\n    this.detachLoader();\n    this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));\n  }\n  structuralStrategy(lang, prefix) {\n    this.memo.clear();\n    const translateFn = this.getTranslateFn(lang, prefix);\n    if (this.view) {\n      // when the lang changes we need to change the reference so Angular will update the view\n      this.view.context['$implicit'] = translateFn;\n      this.view.context['currentLang'] = this.currentLang;\n    } else {\n      this.detachLoader();\n      this.view = this.vcr.createEmbeddedView(this.tpl, {\n        $implicit: translateFn,\n        currentLang: this.currentLang\n      });\n    }\n  }\n  getTranslateFn(lang, prefix) {\n    return (key, params) => {\n      const withPrefix = prefix ? `${prefix}.${key}` : key;\n      const memoKey = params ? `${withPrefix}${JSON.stringify(params)}` : withPrefix;\n      if (!this.memo.has(memoKey)) {\n        this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));\n      }\n      return this.memo.get(memoKey);\n    };\n  }\n  resolveLoadingContent() {\n    return this.inlineTpl || this.providedLoadingTpl;\n  }\n  ngOnDestroy() {\n    this.memo.clear();\n  }\n  detachLoader() {\n    this.loaderTplHandler?.detachView();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: this.inlineScope,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoDirective_Factory(t) {\n    return new (t || TranslocoDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TranslocoDirective,\n    selectors: [[\"\", \"transloco\", \"\"]],\n    inputs: {\n      key: [\"transloco\", \"key\"],\n      params: [\"translocoParams\", \"params\"],\n      inlineScope: [\"translocoScope\", \"inlineScope\"],\n      inlineRead: [\"translocoRead\", \"inlineRead\"],\n      prefix: [\"translocoPrefix\", \"prefix\"],\n      inlineLang: [\"translocoLang\", \"inlineLang\"],\n      inlineTpl: [\"translocoLoadingTpl\", \"inlineTpl\"]\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[transloco]',\n      standalone: true\n    }]\n  }], null, {\n    key: [{\n      type: Input,\n      args: ['transloco']\n    }],\n    params: [{\n      type: Input,\n      args: ['translocoParams']\n    }],\n    inlineScope: [{\n      type: Input,\n      args: ['translocoScope']\n    }],\n    inlineRead: [{\n      type: Input,\n      args: ['translocoRead']\n    }],\n    prefix: [{\n      type: Input,\n      args: ['translocoPrefix']\n    }],\n    inlineLang: [{\n      type: Input,\n      args: ['translocoLang']\n    }],\n    inlineTpl: [{\n      type: Input,\n      args: ['translocoLoadingTpl']\n    }]\n  });\n})();\nclass TranslocoPipe {\n  service;\n  providerScope;\n  providerLang;\n  cdr;\n  subscription = null;\n  lastValue = '';\n  lastKey;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver;\n  constructor(service, providerScope, providerLang, cdr) {\n    this.service = service;\n    this.providerScope = providerScope;\n    this.providerLang = providerLang;\n    this.cdr = cdr;\n    this.scopeResolver = new ScopeResolver(this.service);\n  }\n  // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311\n  // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488\n  transform(key, params, inlineLang) {\n    if (!key) {\n      return key;\n    }\n    const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n    if (keyName === this.lastKey) {\n      return this.lastValue;\n    }\n    this.lastKey = keyName;\n    this.subscription?.unsubscribe();\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);\n    this.subscription = this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange)).subscribe(() => this.updateValue(key, params));\n    return this.lastValue;\n  }\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n    // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n    // callback within its `destination` property, preventing classes from being GC'd.\n    this.subscription = null;\n  }\n  updateValue(key, params) {\n    const lang = this.langResolver.resolveLangBasedOnScope(this.path);\n    this.lastValue = this.service.translate(key, params, lang);\n    this.cdr.markForCheck();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: undefined,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoPipe_Factory(t) {\n    return new (t || TranslocoPipe)(i0.ɵɵdirectiveInject(TranslocoService, 16), i0.ɵɵdirectiveInject(TRANSLOCO_SCOPE, 24), i0.ɵɵdirectiveInject(TRANSLOCO_LANG, 24), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"transloco\",\n    type: TranslocoPipe,\n    pure: false,\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'transloco',\n      pure: false,\n      standalone: true\n    }]\n  }], () => [{\n    type: TranslocoService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_SCOPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LANG]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst decl = [TranslocoDirective, TranslocoPipe];\nclass TranslocoModule {\n  static ɵfac = function TranslocoModule_Factory(t) {\n    return new (t || TranslocoModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoModule, [{\n    type: NgModule,\n    args: [{\n      imports: decl,\n      exports: decl\n    }]\n  }], null, null);\n})();\nfunction provideTransloco(options) {\n  const providers = [provideTranslocoTranspiler(DefaultTranspiler), provideTranslocoMissingHandler(DefaultMissingHandler), provideTranslocoInterceptor(DefaultInterceptor), provideTranslocoFallbackStrategy(DefaultFallbackStrategy)];\n  if (options.config) {\n    providers.push(provideTranslocoConfig(options.config));\n  }\n  if (options.loader) {\n    providers.push(provideTranslocoLoader(options.loader));\n  }\n  return providers;\n}\nfunction provideTranslocoConfig(config) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_CONFIG,\n    useValue: translocoConfig(config)\n  }]);\n}\nfunction provideTranslocoLoader(loader) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_LOADER,\n    useClass: loader\n  }]);\n}\nfunction provideTranslocoScope(...scopes) {\n  return scopes.map(scope => ({\n    provide: TRANSLOCO_SCOPE,\n    useValue: scope,\n    multi: true\n  }));\n}\nfunction provideTranslocoLoadingTpl(content) {\n  return {\n    provide: TRANSLOCO_LOADING_TEMPLATE,\n    useValue: content\n  };\n}\nfunction provideTranslocoTranspiler(transpiler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_TRANSPILER,\n    useClass: transpiler,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoFallbackStrategy(strategy) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_FALLBACK_STRATEGY,\n    useClass: strategy,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoMissingHandler(handler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_MISSING_HANDLER,\n    useClass: handler\n  }]);\n}\nfunction provideTranslocoInterceptor(interceptor) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_INTERCEPTOR,\n    useClass: interceptor\n  }]);\n}\nfunction provideTranslocoLang(lang) {\n  return {\n    provide: TRANSLOCO_LANG,\n    useValue: lang\n  };\n}\nconst TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');\nclass TestingLoader {\n  langs;\n  constructor(langs) {\n    this.langs = langs;\n  }\n  getTranslation(lang) {\n    return of(this.langs[lang]);\n  }\n  static ɵfac = function TestingLoader_Factory(t) {\n    return new (t || TestingLoader)(i0.ɵɵinject(TRANSLOCO_TEST_LANGS));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TestingLoader,\n    factory: TestingLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestingLoader, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TEST_LANGS]\n    }]\n  }], null);\n})();\nfunction initTranslocoService(service, langs = {}, options) {\n  const preloadAllLangs = () => options.preloadLangs ? Promise.all(Object.keys(langs).map(lang => service.load(lang).toPromise())) : Promise.resolve();\n  return preloadAllLangs;\n}\nclass TranslocoTestingModule {\n  static forRoot(options) {\n    return {\n      ngModule: TranslocoTestingModule,\n      providers: [provideTransloco({\n        loader: TestingLoader,\n        config: {\n          prodMode: true,\n          missingHandler: {\n            logMissingKey: false\n          },\n          ...options.translocoConfig\n        }\n      }), {\n        provide: TRANSLOCO_TEST_LANGS,\n        useValue: options.langs\n      }, {\n        provide: TRANSLOCO_TEST_OPTIONS,\n        useValue: options\n      }, {\n        provide: APP_INITIALIZER,\n        useFactory: initTranslocoService,\n        deps: [TranslocoService, TRANSLOCO_TEST_LANGS, TRANSLOCO_TEST_OPTIONS],\n        multi: true\n      }]\n    };\n  }\n  static ɵfac = function TranslocoTestingModule_Factory(t) {\n    return new (t || TranslocoTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoTestingModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [TranslocoModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [TranslocoModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nfunction getBrowserLang() {\n  let browserLang = getBrowserCultureLang();\n  if (!browserLang || !isBrowser()) {\n    return undefined;\n  }\n  if (browserLang.indexOf('-') !== -1) {\n    browserLang = browserLang.split('-')[0];\n  }\n  if (browserLang.indexOf('_') !== -1) {\n    browserLang = browserLang.split('_')[0];\n  }\n  return browserLang;\n}\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nfunction getBrowserCultureLang() {\n  if (!isBrowser()) {\n    return '';\n  }\n  const navigator = window.navigator;\n  return navigator.languages?.[0] ?? navigator.language;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultFallbackStrategy, DefaultInterceptor, DefaultMissingHandler, DefaultTranspiler, FunctionalTranspiler, TRANSLOCO_CONFIG, TRANSLOCO_FALLBACK_STRATEGY, TRANSLOCO_INTERCEPTOR, TRANSLOCO_LANG, TRANSLOCO_LOADER, TRANSLOCO_LOADING_TEMPLATE, TRANSLOCO_MISSING_HANDLER, TRANSLOCO_SCOPE, TRANSLOCO_TRANSPILER, TestingLoader, TranslocoDirective, TranslocoModule, TranslocoPipe, TranslocoService, TranslocoTestingModule, coerceArray, defaultConfig, flatten, getBrowserCultureLang, getBrowserLang, getFunctionArgs, getLangFromScope, getPipeValue, getScopeFromLang, getValue, hasInlineLoader, isBrowser, isDefined, isEmpty, isFunction, isNil, isNumber, isObject, isScopeObject, isString, provideTransloco, provideTranslocoConfig, provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoLang, provideTranslocoLoader, provideTranslocoLoadingTpl, provideTranslocoMissingHandler, provideTranslocoScope, provideTranslocoTranspiler, setValue, size, toCamelCase, toNumber, translate, translateObject, translocoConfig, unflatten };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "Injectable", "Injector", "Inject", "Optional", "Component", "Input", "TemplateRef", "DestroyRef", "ChangeDetectorRef", "ElementRef", "ViewContainerRef", "Renderer2", "Directive", "<PERSON><PERSON>", "NgModule", "makeEnvironmentProviders", "APP_INITIALIZER", "of", "take", "from", "map", "Subject", "BehaviorSubject", "fork<PERSON><PERSON>n", "retry", "tap", "catchError", "shareReplay", "switchMap", "combineLatest", "EMPTY", "takeUntilDestroyed", "unflatten", "unflatten$1", "flatten", "flatten$1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translations", "constructor", "getTranslation", "lang", "get", "TRANSLOCO_LOADER", "getValue", "obj", "path", "Object", "prototype", "hasOwnProperty", "call", "split", "reduce", "p", "c", "setValue", "prop", "val", "lastIndex", "length", "acc", "part", "index", "Array", "isArray", "slice", "size", "collection", "isObject", "keys", "isEmpty", "isFunction", "isString", "isNumber", "item", "coerce<PERSON><PERSON><PERSON>", "value", "toCamelCase", "str", "replace", "word", "toLowerCase", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "window", "isNil", "undefined", "isDefined", "toNumber", "isNaN", "Number", "parseFloat", "isScopeObject", "scope", "hasInlineLoader", "loader", "safe", "TRANSLOCO_CONFIG", "providedIn", "factory", "defaultConfig", "defaultLang", "reRenderOnLangChange", "prodMode", "failedRetries", "fallback<PERSON><PERSON>", "availableLangs", "<PERSON><PERSON><PERSON><PERSON>", "logMissingKey", "useFallbackTranslation", "allowEmpty", "aot", "interpolation", "translocoConfig", "config", "TRANSLOCO_TRANSPILER", "DefaultTranspiler", "optional", "interpolationMatcher", "<PERSON><PERSON><PERSON><PERSON>", "transpile", "params", "translation", "key", "paramMatch", "parsedValue", "exec", "match", "paramValue", "trim", "param", "handleObject", "handleArray", "result", "for<PERSON>ach", "transpiled", "rest", "v", "ɵfac", "DefaultTranspiler_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "ngDevMode", "ɵsetClassMetadata", "type", "start", "end", "RegExp", "getFunctionArgs", "argsString", "splitted", "args", "i", "push", "FunctionalTranspiler", "injector", "functionName", "func", "e", "message", "includes", "Error", "ɵFunctionalTranspiler_BaseFactory", "FunctionalTranspiler_Factory", "ɵɵgetInheritedFactory", "TRANSLOCO_MISSING_HANDLER", "DefaultMissingHandler", "handle", "msg", "console", "warn", "DefaultMissingHandler_Factory", "TRANSLOCO_INTERCEPTOR", "DefaultInterceptor", "preSaveTranslation", "preSaveTranslationKey", "_", "DefaultInterceptor_Factory", "TRANSLOCO_FALLBACK_STRATEGY", "DefaultFallbackStrategy", "userConfig", "getNextLangs", "DefaultFallbackStrategy_Factory", "ɵɵinject", "decorators", "getScopeFromLang", "pop", "join", "getLangFromScope", "getPipeValue", "char", "lastItem", "toString", "shouldListenToLangChanges", "service", "hasStatic", "listenOrNotOperator", "listenToLangChange", "source", "prependScope", "inline<PERSON><PERSON>der", "resolveInlineLoader", "providerScope", "getEventPayload", "langName", "<PERSON><PERSON><PERSON><PERSON>", "options", "<PERSON><PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON>", "then", "res", "default", "getFallbacksLoaders", "fallback<PERSON><PERSON>", "paths", "pipe", "translate", "translateObject", "TranslocoService", "parser", "interceptor", "fallbackStrategy", "langChanges$", "Map", "cache", "firstFallbackLang", "isResolvedMissingOnce", "failed<PERSON>ang<PERSON>", "Set", "events", "events$", "asObservable", "JSON", "parse", "stringify", "setAvailableLangs", "setFallbackLangForMissingTranslation", "setDefaultLang", "getDefaultLang", "subscribe", "wasFailure", "setActiveLang", "payload", "getActiveLang", "onLangChanged", "next", "langs", "getAvailableLangs", "load", "cached", "loadTranslation", "isScope", "_isLangScoped", "loadersOptions", "fallback", "loaders", "load$", "handleSuccess", "set", "error", "handleFailure", "resolveLang", "resolveLangAndScope", "k", "_handleMissingKey", "selectTranslate", "_isObject", "isLang", "isScopeWithLang", "getObjectByKey", "_key", "_params", "getEntries", "selectTranslateObject", "firstKey", "firstParams", "langOrScope", "selectTranslation", "language$", "scopeLangSpecified", "currentLang", "language", "setTranslation", "defaults", "merge", "emitChange", "mergedOptions", "flattenScopeOrTranslation", "getMappedScope", "mergedTranslation", "flattenTranslation", "with<PERSON><PERSON>", "setTranslationKey", "newValue", "fallback<PERSON><PERSON><PERSON>", "getMissingHandlerData", "getAvailableLangsIds", "indexOf", "_loadDependencies", "mainLang", "isLoadedTranslation", "_completeScopeWithLang", "_setScopeAlias", "alias", "scopeMapping", "ngOnDestroy", "clear", "first", "l", "id", "activeLang", "delete", "loadOptions", "failedCounter", "fallback<PERSON><PERSON>s", "fallbacks", "nextLang", "add", "has", "isFallbackLang", "langFromScope", "hasLang", "prefix", "current<PERSON><PERSON>", "startsWith", "entries", "TranslocoService_Factory", "TranslocoLoaderComponent", "html", "TranslocoLoaderComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TranslocoLoaderComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "ɵɵsanitizeHtml", "encapsulation", "Template<PERSON><PERSON><PERSON>", "view", "vcr", "attachView", "createEmbeddedView", "componentRef", "createComponent", "instance", "<PERSON><PERSON><PERSON><PERSON>", "detectChanges", "detach<PERSON>iew", "TRANSLOCO_LANG", "TRANSLOCO_LOADING_TEMPLATE", "TRANSLOCO_SCOPE", "LangResolver", "initialized", "resolve", "inline", "provider", "active", "extracted", "resolveLangBasedOnScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ScopeResolver", "TranslocoDirective", "destroyRef", "tpl", "providerLang", "providedLoadingTpl", "cdr", "host", "renderer", "memo", "inlineScope", "inlineRead", "inlineLang", "inlineTpl", "loaderTplHandler", "langResolver", "scopeResolver", "strategy", "ngTemplateContextGuard", "dir", "ngOnInit", "resolveScope", "attributeStrategy", "structuralStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingContent", "resolveLoadingContent", "ngOnChanges", "changes", "notInit", "some", "firstChange", "deta<PERSON><PERSON><PERSON><PERSON>", "setProperty", "nativeElement", "translateFn", "getTranslateFn", "context", "$implicit", "withPrefix", "memoKey", "resolvedScope", "TranslocoDirective_Factory", "ɵdir", "ɵɵdefineDirective", "ɵɵNgOnChangesFeature", "selector", "TranslocoPipe", "subscription", "lastValue", "last<PERSON>ey", "transform", "keyName", "unsubscribe", "updateValue", "TranslocoPipe_Factory", "ɵɵdirectiveInject", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "decl", "TranslocoModule", "TranslocoModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "provideTransloco", "providers", "provideTranslocoTranspiler", "provideTranslocoMissingHandler", "provideTranslocoInterceptor", "provideTranslocoFallbackStrategy", "provideTranslocoConfig", "provideTranslocoLoader", "provide", "useValue", "useClass", "provideTranslocoScope", "scopes", "multi", "provideTranslocoLoadingTpl", "content", "transpiler", "deps", "handler", "provideTranslocoLang", "TRANSLOCO_TEST_LANGS", "TRANSLOCO_TEST_OPTIONS", "TestingLoader", "TestingLoader_Factory", "initTranslocoService", "preloadAllLangs", "preloadLangs", "Promise", "all", "to<PERSON>romise", "TranslocoTestingModule", "forRoot", "ngModule", "useFactory", "TranslocoTestingModule_Factory", "getBrowserLang", "browserLang", "getBrowserCultureLang", "navigator", "languages"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/@jsverse/transloco/fesm2022/jsverse-transloco.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Injector, Inject, Optional, Component, Input, TemplateRef, DestroyRef, ChangeDetectorRef, ElementRef, ViewContainerRef, Renderer2, Directive, Pipe, NgModule, makeEnvironmentProviders, APP_INITIALIZER } from '@angular/core';\nimport { of, take, from, map, Subject, BehaviorSubject, forkJoin, retry, tap, catchError, shareReplay, switchMap, combineLatest, EMPTY } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { unflatten as unflatten$1, flatten as flatten$1 } from 'flat';\n\nclass DefaultLoader {\n    translations;\n    constructor(translations) {\n        this.translations = translations;\n    }\n    getTranslation(lang) {\n        return of(this.translations.get(lang) || {});\n    }\n}\nconst TRANSLOCO_LOADER = new InjectionToken('TRANSLOCO_LOADER');\n\nfunction getValue(obj, path) {\n    if (!obj) {\n        return obj;\n    }\n    /* For cases where the key is like: 'general.something.thing' */\n    if (Object.prototype.hasOwnProperty.call(obj, path)) {\n        return obj[path];\n    }\n    return path.split('.').reduce((p, c) => p?.[c], obj);\n}\nfunction setValue(obj, prop, val) {\n    obj = { ...obj };\n    const split = prop.split('.');\n    const lastIndex = split.length - 1;\n    split.reduce((acc, part, index) => {\n        if (index === lastIndex) {\n            acc[part] = val;\n        }\n        else {\n            acc[part] = Array.isArray(acc[part])\n                ? acc[part].slice()\n                : { ...acc[part] };\n        }\n        return acc && acc[part];\n    }, obj);\n    return obj;\n}\nfunction size(collection) {\n    if (!collection) {\n        return 0;\n    }\n    if (Array.isArray(collection)) {\n        return collection.length;\n    }\n    if (isObject(collection)) {\n        return Object.keys(collection).length;\n    }\n    return collection ? collection.length : 0;\n}\nfunction isEmpty(collection) {\n    return size(collection) === 0;\n}\nfunction isFunction(val) {\n    return typeof val === 'function';\n}\nfunction isString(val) {\n    return typeof val === 'string';\n}\nfunction isNumber(val) {\n    return typeof val === 'number';\n}\nfunction isObject(item) {\n    return !!item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nfunction toCamelCase(str) {\n    return str\n        .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase())\n        .replace(/\\s+|_|-|\\//g, '');\n}\nfunction isBrowser() {\n    return typeof window !== 'undefined';\n}\nfunction isNil(value) {\n    return value === null || value === undefined;\n}\nfunction isDefined(value) {\n    return isNil(value) === false;\n}\nfunction toNumber(value) {\n    if (isNumber(value))\n        return value;\n    if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n        return Number(value);\n    }\n    return null;\n}\nfunction isScopeObject(item) {\n    return item && typeof item.scope === 'string';\n}\nfunction hasInlineLoader(item) {\n    return item && isObject(item.loader);\n}\nfunction unflatten(obj) {\n    return unflatten$1(obj);\n}\nfunction flatten(obj) {\n    return flatten$1(obj, { safe: true });\n}\n\nconst TRANSLOCO_CONFIG = new InjectionToken('TRANSLOCO_CONFIG', {\n    providedIn: 'root',\n    factory: () => defaultConfig,\n});\nconst defaultConfig = {\n    defaultLang: 'en',\n    reRenderOnLangChange: false,\n    prodMode: false,\n    failedRetries: 2,\n    fallbackLang: [],\n    availableLangs: [],\n    missingHandler: {\n        logMissingKey: true,\n        useFallbackTranslation: false,\n        allowEmpty: false,\n    },\n    flatten: {\n        aot: false,\n    },\n    interpolation: ['{{', '}}'],\n};\nfunction translocoConfig(config = {}) {\n    return {\n        ...defaultConfig,\n        ...config,\n        missingHandler: {\n            ...defaultConfig.missingHandler,\n            ...config.missingHandler,\n        },\n        flatten: {\n            ...defaultConfig.flatten,\n            ...config.flatten,\n        },\n    };\n}\n\nconst TRANSLOCO_TRANSPILER = new InjectionToken('TRANSLOCO_TRANSPILER');\nclass DefaultTranspiler {\n    config = inject(TRANSLOCO_CONFIG, { optional: true }) ?? defaultConfig;\n    get interpolationMatcher() {\n        return resolveMatcher(this.config);\n    }\n    transpile({ value, params = {}, translation, key }) {\n        if (isString(value)) {\n            let paramMatch;\n            let parsedValue = value;\n            while ((paramMatch = this.interpolationMatcher.exec(parsedValue)) !== null) {\n                const [match, paramValue] = paramMatch;\n                parsedValue = parsedValue.replace(match, () => {\n                    const match = paramValue.trim();\n                    const param = getValue(params, match);\n                    if (isDefined(param)) {\n                        return param;\n                    }\n                    return isDefined(translation[match])\n                        ? this.transpile({\n                            params,\n                            translation,\n                            key,\n                            value: translation[match],\n                        })\n                        : '';\n                });\n            }\n            return parsedValue;\n        }\n        else if (params) {\n            if (isObject(value)) {\n                value = this.handleObject({\n                    value: value,\n                    params,\n                    translation,\n                    key,\n                });\n            }\n            else if (Array.isArray(value)) {\n                value = this.handleArray({ value, params, translation, key });\n            }\n        }\n        return value;\n    }\n    /**\n     *\n     * @example\n     *\n     * const en = {\n     *  a: {\n     *    b: {\n     *      c: \"Hello {{ value }}\"\n     *    }\n     *  }\n     * }\n     *\n     * const params =  {\n     *  \"b.c\": { value: \"Transloco \"}\n     * }\n     *\n     * service.selectTranslate('a', params);\n     *\n     * // the first param will be the result of `en.a`.\n     * // the second param will be `params`.\n     * parser.transpile(value, params, {});\n     *\n     *\n     */\n    handleObject({ value, params = {}, translation, key, }) {\n        let result = value;\n        Object.keys(params).forEach((p) => {\n            // transpile the value => \"Hello Transloco\"\n            const transpiled = this.transpile({\n                // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n                value: getValue(result, p),\n                // get the params of \"b.c\" => { value: \"Transloco\" }\n                params: getValue(params, p),\n                translation,\n                key,\n            });\n            // set \"b.c\" to `transpiled`\n            result = setValue(result, p, transpiled);\n        });\n        return result;\n    }\n    handleArray({ value, ...rest }) {\n        return value.map((v) => this.transpile({\n            value: v,\n            ...rest,\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultTranspiler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultTranspiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultTranspiler, decorators: [{\n            type: Injectable\n        }] });\nfunction resolveMatcher(config) {\n    const [start, end] = config.interpolation;\n    return new RegExp(`${start}([^${start}${end}]*?)${end}`, 'g');\n}\nfunction getFunctionArgs(argsString) {\n    const splitted = argsString ? argsString.split(',') : [];\n    const args = [];\n    for (let i = 0; i < splitted.length; i++) {\n        let value = splitted[i].trim();\n        while (value[value.length - 1] === '\\\\') {\n            i++;\n            value = value.replace('\\\\', ',') + splitted[i];\n        }\n        args.push(value);\n    }\n    return args;\n}\nclass FunctionalTranspiler extends DefaultTranspiler {\n    injector = inject(Injector);\n    transpile({ value, ...rest }) {\n        let transpiled = value;\n        if (isString(value)) {\n            transpiled = value.replace(/\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g, (match, functionName, args) => {\n                try {\n                    const func = this.injector.get(functionName);\n                    return func.transpile(...getFunctionArgs(args));\n                }\n                catch (e) {\n                    let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n                    if (e.message.includes('NullInjectorError')) {\n                        message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n                    }\n                    throw new Error(message);\n                }\n            });\n        }\n        return super.transpile({ value: transpiled, ...rest });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: FunctionalTranspiler, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: FunctionalTranspiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: FunctionalTranspiler, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_MISSING_HANDLER = new InjectionToken('TRANSLOCO_MISSING_HANDLER');\nclass DefaultMissingHandler {\n    handle(key, config) {\n        if (config.missingHandler.logMissingKey && !config.prodMode) {\n            const msg = `Missing translation for '${key}'`;\n            console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n        }\n        return key;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultMissingHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultMissingHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultMissingHandler, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_INTERCEPTOR = new InjectionToken('TRANSLOCO_INTERCEPTOR');\nclass DefaultInterceptor {\n    preSaveTranslation(translation) {\n        return translation;\n    }\n    preSaveTranslationKey(_, value) {\n        return value;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultInterceptor, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken('TRANSLOCO_FALLBACK_STRATEGY');\nclass DefaultFallbackStrategy {\n    userConfig;\n    constructor(userConfig) {\n        this.userConfig = userConfig;\n    }\n    getNextLangs() {\n        const fallbackLang = this.userConfig.fallbackLang;\n        if (!fallbackLang) {\n            throw new Error('When using the default fallback, a fallback language must be provided in the config!');\n        }\n        return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultFallbackStrategy, deps: [{ token: TRANSLOCO_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultFallbackStrategy });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: DefaultFallbackStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_CONFIG]\n                }] }] });\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nfunction getScopeFromLang(lang) {\n    if (!lang) {\n        return '';\n    }\n    const split = lang.split('/');\n    split.pop();\n    return split.join('/');\n}\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nfunction getLangFromScope(lang) {\n    if (!lang) {\n        return '';\n    }\n    return lang.split('/').pop();\n}\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nfunction getPipeValue(str, value, char = '|') {\n    if (isString(str)) {\n        const splitted = str.split(char);\n        const lastItem = splitted.pop();\n        return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n    }\n    return [false, ''];\n}\nfunction shouldListenToLangChanges(service, lang) {\n    const [hasStatic] = getPipeValue(lang, 'static');\n    if (!hasStatic) {\n        // If we didn't get 'lang|static' check if it's set in the global level\n        return !!service.config.reRenderOnLangChange;\n    }\n    // We have 'lang|static' so don't listen to lang changes\n    return false;\n}\nfunction listenOrNotOperator(listenToLangChange) {\n    return listenToLangChange ? (source) => source : take(1);\n}\nfunction prependScope(inlineLoader, scope) {\n    return Object.keys(inlineLoader).reduce((acc, lang) => {\n        acc[`${scope}/${lang}`] = inlineLoader[lang];\n        return acc;\n    }, {});\n}\nfunction resolveInlineLoader(providerScope, scope) {\n    return hasInlineLoader(providerScope)\n        ? prependScope(providerScope.loader, scope)\n        : undefined;\n}\nfunction getEventPayload(lang) {\n    return {\n        scope: getScopeFromLang(lang) || null,\n        langName: getLangFromScope(lang),\n    };\n}\n\nfunction resolveLoader(options) {\n    const { path, inlineLoader, mainLoader, data } = options;\n    if (inlineLoader) {\n        const pathLoader = inlineLoader[path];\n        if (isFunction(pathLoader) === false) {\n            throw `You're using an inline loader but didn't provide a loader for ${path}`;\n        }\n        return inlineLoader[path]().then((res) => res.default ? res.default : res);\n    }\n    return mainLoader.getTranslation(path, data);\n}\n\nfunction getFallbacksLoaders({ mainLoader, path, data, fallbackPath, inlineLoader, }) {\n    const paths = fallbackPath ? [path, fallbackPath] : [path];\n    return paths.map((path) => {\n        const loader = resolveLoader({ path, mainLoader, inlineLoader, data });\n        return from(loader).pipe(map((translation) => ({\n            translation,\n            lang: path,\n        })));\n    });\n}\n\nlet service;\nfunction translate(key, params = {}, lang) {\n    return service.translate(key, params, lang);\n}\nfunction translateObject(key, params = {}, lang) {\n    return service.translateObject(key, params, lang);\n}\nclass TranslocoService {\n    loader;\n    parser;\n    missingHandler;\n    interceptor;\n    fallbackStrategy;\n    langChanges$;\n    translations = new Map();\n    cache = new Map();\n    firstFallbackLang;\n    defaultLang = '';\n    availableLangs = [];\n    isResolvedMissingOnce = false;\n    lang;\n    failedLangs = new Set();\n    events = new Subject();\n    events$ = this.events.asObservable();\n    config;\n    constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {\n        this.loader = loader;\n        this.parser = parser;\n        this.missingHandler = missingHandler;\n        this.interceptor = interceptor;\n        this.fallbackStrategy = fallbackStrategy;\n        if (!this.loader) {\n            this.loader = new DefaultLoader(this.translations);\n        }\n        service = this;\n        this.config = JSON.parse(JSON.stringify(userConfig));\n        this.setAvailableLangs(this.config.availableLangs || []);\n        this.setFallbackLangForMissingTranslation(this.config);\n        this.setDefaultLang(this.config.defaultLang);\n        this.lang = new BehaviorSubject(this.getDefaultLang());\n        // Don't use distinctUntilChanged as we need the ability to update\n        // the value when using setTranslation or setTranslationKeys\n        this.langChanges$ = this.lang.asObservable();\n        /**\n         * When we have a failure, we want to define the next language that succeeded as the active\n         */\n        this.events$.pipe(takeUntilDestroyed()).subscribe((e) => {\n            if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n                this.setActiveLang(e.payload.langName);\n            }\n        });\n    }\n    getDefaultLang() {\n        return this.defaultLang;\n    }\n    setDefaultLang(lang) {\n        this.defaultLang = lang;\n    }\n    getActiveLang() {\n        return this.lang.getValue();\n    }\n    setActiveLang(lang) {\n        this.parser.onLangChanged?.(lang);\n        this.lang.next(lang);\n        this.events.next({\n            type: 'langChanged',\n            payload: getEventPayload(lang),\n        });\n        return this;\n    }\n    setAvailableLangs(langs) {\n        this.availableLangs = langs;\n    }\n    /**\n     * Gets the available languages.\n     *\n     * @returns\n     * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n     * depending on how the available languages are set in your module.\n     */\n    getAvailableLangs() {\n        return this.availableLangs;\n    }\n    load(path, options = {}) {\n        const cached = this.cache.get(path);\n        if (cached) {\n            return cached;\n        }\n        let loadTranslation;\n        const isScope = this._isLangScoped(path);\n        let scope;\n        if (isScope) {\n            scope = getScopeFromLang(path);\n        }\n        const loadersOptions = {\n            path,\n            mainLoader: this.loader,\n            inlineLoader: options.inlineLoader,\n            data: isScope ? { scope: scope } : undefined,\n        };\n        if (this.useFallbackTranslation(path)) {\n            // if the path is scope the fallback should be `scope/fallbackLang`;\n            const fallback = isScope\n                ? `${scope}/${this.firstFallbackLang}`\n                : this.firstFallbackLang;\n            const loaders = getFallbacksLoaders({\n                ...loadersOptions,\n                fallbackPath: fallback,\n            });\n            loadTranslation = forkJoin(loaders);\n        }\n        else {\n            const loader = resolveLoader(loadersOptions);\n            loadTranslation = from(loader);\n        }\n        const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap((translation) => {\n            if (Array.isArray(translation)) {\n                translation.forEach((t) => {\n                    this.handleSuccess(t.lang, t.translation);\n                    // Save the fallback in cache so we'll not create a redundant request\n                    if (t.lang !== path) {\n                        this.cache.set(t.lang, of({}));\n                    }\n                });\n                return;\n            }\n            this.handleSuccess(path, translation);\n        }), catchError((error) => {\n            if (!this.config.prodMode) {\n                console.error(`Error while trying to load \"${path}\"`, error);\n            }\n            return this.handleFailure(path, options);\n        }), shareReplay(1));\n        this.cache.set(path, load$);\n        return load$;\n    }\n    /**\n     * Gets the instant translated value of a key\n     *\n     * @example\n     *\n     * translate<string>('hello')\n     * translate('hello', { value: 'value' })\n     * translate<string[]>(['hello', 'key'])\n     * translate('hello', { }, 'en')\n     * translate('scope.someKey', { }, 'en')\n     */\n    translate(key, params = {}, lang = this.getActiveLang()) {\n        if (!key)\n            return key;\n        const { scope, resolveLang } = this.resolveLangAndScope(lang);\n        if (Array.isArray(key)) {\n            return key.map((k) => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));\n        }\n        key = scope ? `${scope}.${key}` : key;\n        const translation = this.getTranslation(resolveLang);\n        const value = translation[key];\n        if (!value) {\n            return this._handleMissingKey(key, value, params);\n        }\n        return this.parser.transpile({\n            value,\n            params,\n            translation,\n            key,\n        });\n    }\n    /**\n     * Gets the translated value of a key as observable\n     *\n     * @example\n     *\n     * selectTranslate<string>('hello').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n     *\n     */\n    selectTranslate(key, params, lang, _isObject = false) {\n        let inlineLoader;\n        const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject\n            ? this.translateObject(key, params, lang)\n            : this.translate(key, params, lang)));\n        if (isNil(lang)) {\n            return this.langChanges$.pipe(switchMap((lang) => load(lang)));\n        }\n        lang = Array.isArray(lang) ? lang[0] : lang;\n        if (isScopeObject(lang)) {\n            // it's a scope object.\n            const providerScope = lang;\n            lang = providerScope.scope;\n            inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n        }\n        lang = lang;\n        if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n            return load(lang);\n        }\n        // it's a scope\n        const scope = lang;\n        return this.langChanges$.pipe(switchMap((lang) => load(`${scope}/${lang}`, { inlineLoader })));\n    }\n    /**\n     * Whether the scope with lang\n     *\n     * @example\n     *\n     * todos/en => true\n     * todos => false\n     */\n    isScopeWithLang(lang) {\n        return this.isLang(getLangFromScope(lang));\n    }\n    translateObject(key, params = {}, lang = this.getActiveLang()) {\n        if (isString(key) || Array.isArray(key)) {\n            const { resolveLang, scope } = this.resolveLangAndScope(lang);\n            if (Array.isArray(key)) {\n                return key.map((k) => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));\n            }\n            const translation = this.getTranslation(resolveLang);\n            key = scope ? `${scope}.${key}` : key;\n            const value = unflatten(this.getObjectByKey(translation, key));\n            /* If an empty object was returned we want to try and translate the key as a string and not an object */\n            return isEmpty(value)\n                ? this.translate(key, params, lang)\n                : this.parser.transpile({ value, params: params, translation, key });\n        }\n        const translations = [];\n        for (const [_key, _params] of this.getEntries(key)) {\n            translations.push(this.translateObject(_key, _params, lang));\n        }\n        return translations;\n    }\n    selectTranslateObject(key, params, lang) {\n        if (isString(key) || Array.isArray(key)) {\n            return this.selectTranslate(key, params, lang, true);\n        }\n        const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n        /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n         * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n        return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map((value) => {\n            const translations = [value];\n            for (const [_key, _params] of rest) {\n                translations.push(this.translateObject(_key, _params, lang));\n            }\n            return translations;\n        }));\n    }\n    getTranslation(langOrScope) {\n        if (langOrScope) {\n            if (this.isLang(langOrScope)) {\n                return this.translations.get(langOrScope) || {};\n            }\n            else {\n                // This is a scope, build the scope value from the translation object\n                const { scope, resolveLang } = this.resolveLangAndScope(langOrScope);\n                const translation = this.translations.get(resolveLang) || {};\n                return this.getObjectByKey(translation, scope);\n            }\n        }\n        return this.translations;\n    }\n    /**\n     * Gets an object of translations for a given language\n     *\n     * @example\n     *\n     * selectTranslation().subscribe() - will return the current lang translation\n     * selectTranslation('es').subscribe()\n     * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n     * selectTranslation('admin-page/es').subscribe()\n     */\n    selectTranslation(lang) {\n        let language$ = this.langChanges$;\n        if (lang) {\n            const scopeLangSpecified = getLangFromScope(lang) !== lang;\n            if (this.isLang(lang) || scopeLangSpecified) {\n                language$ = of(lang);\n            }\n            else {\n                language$ = this.langChanges$.pipe(map((currentLang) => `${lang}/${currentLang}`));\n            }\n        }\n        return language$.pipe(switchMap((language) => this.load(language).pipe(map(() => this.getTranslation(language)))));\n    }\n    /**\n     * Sets or merge a given translation object to current lang\n     *\n     * @example\n     *\n     * setTranslation({ ... })\n     * setTranslation({ ... }, 'en')\n     * setTranslation({ ... }, 'es', { merge: false } )\n     * setTranslation({ ... }, 'todos/en', { merge: false } )\n     */\n    setTranslation(translation, lang = this.getActiveLang(), options = {}) {\n        const defaults = { merge: true, emitChange: true };\n        const mergedOptions = { ...defaults, ...options };\n        const scope = getScopeFromLang(lang);\n        /**\n         * If this isn't a scope we use the whole translation as is\n         * otherwise we need to flat the scope and use it\n         */\n        let flattenScopeOrTranslation = translation;\n        // Merged the scoped language into the active language\n        if (scope) {\n            const key = this.getMappedScope(scope);\n            flattenScopeOrTranslation = flatten({ [key]: translation });\n        }\n        const currentLang = scope ? getLangFromScope(lang) : lang;\n        const mergedTranslation = {\n            ...(mergedOptions.merge && this.getTranslation(currentLang)),\n            ...flattenScopeOrTranslation,\n        };\n        const flattenTranslation = this.config.flatten.aot\n            ? mergedTranslation\n            : flatten(mergedTranslation);\n        const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);\n        this.translations.set(currentLang, withHook);\n        mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n    }\n    /**\n     * Sets translation key with given value\n     *\n     * @example\n     *\n     * setTranslationKey('key', 'value')\n     * setTranslationKey('key.nested', 'value')\n     * setTranslationKey('key.nested', 'value', 'en')\n     * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n     */\n    setTranslationKey(key, value, options = {}) {\n        const lang = options.lang || this.getActiveLang();\n        const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n        const newValue = {\n            [key]: withHook,\n        };\n        this.setTranslation(newValue, lang, { ...options, merge: true });\n    }\n    /**\n     * Sets the fallback lang for the currently active language\n     * @param fallbackLang\n     */\n    setFallbackLangForMissingTranslation({ fallbackLang, }) {\n        const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n        if (fallbackLang && this.useFallbackTranslation(lang)) {\n            this.firstFallbackLang = lang;\n        }\n    }\n    /**\n     * @internal\n     */\n    _handleMissingKey(key, value, params) {\n        if (this.config.missingHandler.allowEmpty && value === '') {\n            return '';\n        }\n        if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n            // We need to set it to true to prevent a loop\n            this.isResolvedMissingOnce = true;\n            const fallbackValue = this.translate(key, params, this.firstFallbackLang);\n            this.isResolvedMissingOnce = false;\n            return fallbackValue;\n        }\n        return this.missingHandler.handle(key, this.getMissingHandlerData(), params);\n    }\n    /**\n     * @internal\n     */\n    _isLangScoped(lang) {\n        return this.getAvailableLangsIds().indexOf(lang) === -1;\n    }\n    /**\n     * Checks if a given string is one of the specified available languages.\n     * @returns\n     * True if the given string is an available language.\n     * False if the given string is not an available language.\n     */\n    isLang(lang) {\n        return this.getAvailableLangsIds().indexOf(lang) !== -1;\n    }\n    /**\n     * @internal\n     *\n     * We always want to make sure the global lang is loaded\n     * before loading the scope since you can access both via the pipe/directive.\n     */\n    _loadDependencies(path, inlineLoader) {\n        const mainLang = getLangFromScope(path);\n        if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n            return combineLatest([\n                this.load(mainLang),\n                this.load(path, { inlineLoader }),\n            ]);\n        }\n        return this.load(path, { inlineLoader });\n    }\n    /**\n     * @internal\n     */\n    _completeScopeWithLang(langOrScope) {\n        if (this._isLangScoped(langOrScope) &&\n            !this.isLang(getLangFromScope(langOrScope))) {\n            return `${langOrScope}/${this.getActiveLang()}`;\n        }\n        return langOrScope;\n    }\n    /**\n     * @internal\n     */\n    _setScopeAlias(scope, alias) {\n        if (!this.config.scopeMapping) {\n            this.config.scopeMapping = {};\n        }\n        this.config.scopeMapping[scope] = alias;\n    }\n    ngOnDestroy() {\n        // Caretaker note: since this is the root provider, it'll be destroyed when the `NgModuleRef.destroy()` is run.\n        // Cached values capture `this`, thus leading to a circular reference and preventing the `TranslocoService` from\n        // being GC'd. This would lead to a memory leak when server-side rendering is used since the service is created\n        // and destroyed per each HTTP request, but any service is not getting GC'd.\n        this.cache.clear();\n    }\n    isLoadedTranslation(lang) {\n        return size(this.getTranslation(lang));\n    }\n    getAvailableLangsIds() {\n        const first = this.getAvailableLangs()[0];\n        if (isString(first)) {\n            return this.getAvailableLangs();\n        }\n        return this.getAvailableLangs().map((l) => l.id);\n    }\n    getMissingHandlerData() {\n        return {\n            ...this.config,\n            activeLang: this.getActiveLang(),\n            availableLangs: this.availableLangs,\n            defaultLang: this.defaultLang,\n        };\n    }\n    /**\n     * Use a fallback translation set for missing keys of the primary language\n     * This is unrelated to the fallback language (which changes the active language)\n     */\n    useFallbackTranslation(lang) {\n        return (this.config.missingHandler.useFallbackTranslation &&\n            lang !== this.firstFallbackLang);\n    }\n    handleSuccess(lang, translation) {\n        this.setTranslation(translation, lang, { emitChange: false });\n        this.events.next({\n            wasFailure: !!this.failedLangs.size,\n            type: 'translationLoadSuccess',\n            payload: getEventPayload(lang),\n        });\n        this.failedLangs.forEach((l) => this.cache.delete(l));\n        this.failedLangs.clear();\n    }\n    handleFailure(lang, loadOptions) {\n        // When starting to load a first choice language, initialize\n        // the failed counter and resolve the fallback langs.\n        if (isNil(loadOptions.failedCounter)) {\n            loadOptions.failedCounter = 0;\n            if (!loadOptions.fallbackLangs) {\n                loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n            }\n        }\n        const splitted = lang.split('/');\n        const fallbacks = loadOptions.fallbackLangs;\n        const nextLang = fallbacks[loadOptions.failedCounter];\n        this.failedLangs.add(lang);\n        // This handles the case where a loaded fallback language is requested again\n        if (this.cache.has(nextLang)) {\n            this.handleSuccess(nextLang, this.getTranslation(nextLang));\n            return EMPTY;\n        }\n        const isFallbackLang = nextLang === splitted[splitted.length - 1];\n        if (!nextLang || isFallbackLang) {\n            let msg = `Unable to load translation and all the fallback languages`;\n            if (splitted.length > 1) {\n                msg += `, did you misspelled the scope name?`;\n            }\n            throw new Error(msg);\n        }\n        let resolveLang = nextLang;\n        // if it's scoped lang\n        if (splitted.length > 1) {\n            // We need to resolve it to:\n            // todos/langNotExists => todos/nextLang\n            splitted[splitted.length - 1] = nextLang;\n            resolveLang = splitted.join('/');\n        }\n        loadOptions.failedCounter++;\n        this.events.next({\n            type: 'translationLoadFailure',\n            payload: getEventPayload(lang),\n        });\n        return this.load(resolveLang, loadOptions);\n    }\n    getMappedScope(scope) {\n        const { scopeMapping = {} } = this.config;\n        return scopeMapping[scope] || toCamelCase(scope);\n    }\n    /**\n     * If lang is scope we need to check the following cases:\n     * todos/es => in this case we should take `es` as lang\n     * todos => in this case we should set the active lang as lang\n     */\n    resolveLangAndScope(lang) {\n        let resolveLang = lang;\n        let scope;\n        if (this._isLangScoped(lang)) {\n            // en for example\n            const langFromScope = getLangFromScope(lang);\n            // en is lang\n            const hasLang = this.isLang(langFromScope);\n            // take en\n            resolveLang = hasLang ? langFromScope : this.getActiveLang();\n            // find the scope\n            scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n        }\n        return { scope, resolveLang };\n    }\n    getObjectByKey(translation, key) {\n        const result = {};\n        const prefix = `${key}.`;\n        for (const currentKey in translation) {\n            if (currentKey.startsWith(prefix)) {\n                result[currentKey.replace(prefix, '')] = translation[currentKey];\n            }\n        }\n        return result;\n    }\n    getEntries(key) {\n        return key instanceof Map ? key.entries() : Object.entries(key);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoService, deps: [{ token: TRANSLOCO_LOADER, optional: true }, { token: TRANSLOCO_TRANSPILER }, { token: TRANSLOCO_MISSING_HANDLER }, { token: TRANSLOCO_INTERCEPTOR }, { token: TRANSLOCO_CONFIG }, { token: TRANSLOCO_FALLBACK_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoService, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_LOADER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_TRANSPILER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_MISSING_HANDLER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_INTERCEPTOR]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_CONFIG]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_FALLBACK_STRATEGY]\n                }] }] });\n\nclass TranslocoLoaderComponent {\n    html;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoLoaderComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.9\", type: TranslocoLoaderComponent, isStandalone: true, selector: \"ng-component\", inputs: { html: \"html\" }, ngImport: i0, template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoLoaderComponent, decorators: [{\n            type: Component,\n            args: [{\n                    template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n                    standalone: true,\n                }]\n        }], propDecorators: { html: [{\n                type: Input\n            }] } });\n\nclass TemplateHandler {\n    view;\n    vcr;\n    constructor(view, vcr) {\n        this.view = view;\n        this.vcr = vcr;\n    }\n    attachView() {\n        if (this.view instanceof TemplateRef) {\n            this.vcr.createEmbeddedView(this.view);\n        }\n        else if (isString(this.view)) {\n            const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n            componentRef.instance.html = this.view;\n            componentRef.hostView.detectChanges();\n        }\n        else {\n            this.vcr.createComponent(this.view);\n        }\n    }\n    detachView() {\n        this.vcr.clear();\n    }\n}\n\nconst TRANSLOCO_LANG = new InjectionToken('TRANSLOCO_LANG');\n\nconst TRANSLOCO_LOADING_TEMPLATE = new InjectionToken('TRANSLOCO_LOADING_TEMPLATE');\n\nconst TRANSLOCO_SCOPE = new InjectionToken('TRANSLOCO_SCOPE');\n\nclass LangResolver {\n    initialized = false;\n    // inline => provider => active\n    resolve({ inline, provider, active }) {\n        let lang = active;\n        /**\n         * When the user changes the lang we need to update\n         * the view. Otherwise, the lang will remain the inline/provided lang\n         */\n        if (this.initialized) {\n            lang = active;\n            return lang;\n        }\n        if (provider) {\n            const [, extracted] = getPipeValue(provider, 'static');\n            lang = extracted;\n        }\n        if (inline) {\n            const [, extracted] = getPipeValue(inline, 'static');\n            lang = extracted;\n        }\n        this.initialized = true;\n        return lang;\n    }\n    /**\n     *\n     * Resolve the lang\n     *\n     * @example\n     *\n     * resolveLangBasedOnScope('todos/en') => en\n     * resolveLangBasedOnScope('en') => en\n     *\n     */\n    resolveLangBasedOnScope(lang) {\n        const scope = getScopeFromLang(lang);\n        return scope ? getLangFromScope(lang) : lang;\n    }\n    /**\n     *\n     * Resolve the lang path for loading\n     *\n     * @example\n     *\n     * resolveLangPath('todos', 'en') => todos/en\n     * resolveLangPath('en') => en\n     *\n     */\n    resolveLangPath(lang, scope) {\n        return scope ? `${scope}/${lang}` : lang;\n    }\n}\n\nclass ScopeResolver {\n    service;\n    constructor(service) {\n        this.service = service;\n    }\n    // inline => provider\n    resolve(params) {\n        const { inline, provider } = params;\n        if (inline) {\n            return inline;\n        }\n        if (provider) {\n            if (isScopeObject(provider)) {\n                const { scope, alias = toCamelCase(scope) } = provider;\n                this.service._setScopeAlias(scope, alias);\n                return scope;\n            }\n            return provider;\n        }\n        return undefined;\n    }\n}\n\nclass TranslocoDirective {\n    destroyRef = inject(DestroyRef);\n    service = inject(TranslocoService);\n    tpl = inject(TemplateRef, {\n        optional: true,\n    });\n    providerLang = inject(TRANSLOCO_LANG, { optional: true });\n    providerScope = inject(TRANSLOCO_SCOPE, { optional: true });\n    providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n        optional: true,\n    });\n    cdr = inject(ChangeDetectorRef);\n    host = inject(ElementRef);\n    vcr = inject(ViewContainerRef);\n    renderer = inject(Renderer2);\n    view;\n    memo = new Map();\n    key;\n    params = {};\n    inlineScope;\n    /** @deprecated use prefix instead, will be removed in Transloco v8 */\n    inlineRead;\n    prefix;\n    inlineLang;\n    inlineTpl;\n    currentLang;\n    loaderTplHandler;\n    // Whether we already rendered the view once\n    initialized = false;\n    path;\n    langResolver = new LangResolver();\n    scopeResolver = new ScopeResolver(this.service);\n    strategy = this.tpl === null ? 'attribute' : 'structural';\n    static ngTemplateContextGuard(dir, ctx) {\n        return true;\n    }\n    ngOnInit() {\n        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);\n        this.service.langChanges$\n            .pipe(switchMap((activeLang) => {\n            const lang = this.langResolver.resolve({\n                inline: this.inlineLang,\n                provider: this.providerLang,\n                active: activeLang,\n            });\n            return Array.isArray(this.providerScope)\n                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))\n                : this.resolveScope(lang, this.providerScope);\n        }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef))\n            .subscribe(() => {\n            this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);\n            this.strategy === 'attribute'\n                ? this.attributeStrategy()\n                : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);\n            this.cdr.markForCheck();\n            this.initialized = true;\n        });\n        if (!this.initialized) {\n            const loadingContent = this.resolveLoadingContent();\n            if (loadingContent) {\n                this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n                this.loaderTplHandler.attachView();\n            }\n        }\n    }\n    ngOnChanges(changes) {\n        // We need to support dynamic keys/params, so if this is not the first change CD cycle\n        // we need to run the function again in order to update the value\n        if (this.strategy === 'attribute') {\n            const notInit = Object.keys(changes).some((v) => !changes[v].firstChange);\n            notInit && this.attributeStrategy();\n        }\n    }\n    attributeStrategy() {\n        this.detachLoader();\n        this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));\n    }\n    structuralStrategy(lang, prefix) {\n        this.memo.clear();\n        const translateFn = this.getTranslateFn(lang, prefix);\n        if (this.view) {\n            // when the lang changes we need to change the reference so Angular will update the view\n            this.view.context['$implicit'] = translateFn;\n            this.view.context['currentLang'] = this.currentLang;\n        }\n        else {\n            this.detachLoader();\n            this.view = this.vcr.createEmbeddedView(this.tpl, {\n                $implicit: translateFn,\n                currentLang: this.currentLang,\n            });\n        }\n    }\n    getTranslateFn(lang, prefix) {\n        return (key, params) => {\n            const withPrefix = prefix ? `${prefix}.${key}` : key;\n            const memoKey = params\n                ? `${withPrefix}${JSON.stringify(params)}`\n                : withPrefix;\n            if (!this.memo.has(memoKey)) {\n                this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));\n            }\n            return this.memo.get(memoKey);\n        };\n    }\n    resolveLoadingContent() {\n        return this.inlineTpl || this.providedLoadingTpl;\n    }\n    ngOnDestroy() {\n        this.memo.clear();\n    }\n    detachLoader() {\n        this.loaderTplHandler?.detachView();\n    }\n    resolveScope(lang, providerScope) {\n        const resolvedScope = this.scopeResolver.resolve({\n            inline: this.inlineScope,\n            provider: providerScope,\n        });\n        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n        return this.service._loadDependencies(this.path, inlineLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.9\", type: TranslocoDirective, isStandalone: true, selector: \"[transloco]\", inputs: { key: [\"transloco\", \"key\"], params: [\"translocoParams\", \"params\"], inlineScope: [\"translocoScope\", \"inlineScope\"], inlineRead: [\"translocoRead\", \"inlineRead\"], prefix: [\"translocoPrefix\", \"prefix\"], inlineLang: [\"translocoLang\", \"inlineLang\"], inlineTpl: [\"translocoLoadingTpl\", \"inlineTpl\"] }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[transloco]',\n                    standalone: true,\n                }]\n        }], propDecorators: { key: [{\n                type: Input,\n                args: ['transloco']\n            }], params: [{\n                type: Input,\n                args: ['translocoParams']\n            }], inlineScope: [{\n                type: Input,\n                args: ['translocoScope']\n            }], inlineRead: [{\n                type: Input,\n                args: ['translocoRead']\n            }], prefix: [{\n                type: Input,\n                args: ['translocoPrefix']\n            }], inlineLang: [{\n                type: Input,\n                args: ['translocoLang']\n            }], inlineTpl: [{\n                type: Input,\n                args: ['translocoLoadingTpl']\n            }] } });\n\nclass TranslocoPipe {\n    service;\n    providerScope;\n    providerLang;\n    cdr;\n    subscription = null;\n    lastValue = '';\n    lastKey;\n    path;\n    langResolver = new LangResolver();\n    scopeResolver;\n    constructor(service, providerScope, providerLang, cdr) {\n        this.service = service;\n        this.providerScope = providerScope;\n        this.providerLang = providerLang;\n        this.cdr = cdr;\n        this.scopeResolver = new ScopeResolver(this.service);\n    }\n    // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311\n    // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488\n    transform(key, params, inlineLang) {\n        if (!key) {\n            return key;\n        }\n        const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n        if (keyName === this.lastKey) {\n            return this.lastValue;\n        }\n        this.lastKey = keyName;\n        this.subscription?.unsubscribe();\n        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);\n        this.subscription = this.service.langChanges$\n            .pipe(switchMap((activeLang) => {\n            const lang = this.langResolver.resolve({\n                inline: inlineLang,\n                provider: this.providerLang,\n                active: activeLang,\n            });\n            return Array.isArray(this.providerScope)\n                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))\n                : this.resolveScope(lang, this.providerScope);\n        }), listenOrNotOperator(listenToLangChange))\n            .subscribe(() => this.updateValue(key, params));\n        return this.lastValue;\n    }\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n        // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n        // callback within its `destination` property, preventing classes from being GC'd.\n        this.subscription = null;\n    }\n    updateValue(key, params) {\n        const lang = this.langResolver.resolveLangBasedOnScope(this.path);\n        this.lastValue = this.service.translate(key, params, lang);\n        this.cdr.markForCheck();\n    }\n    resolveScope(lang, providerScope) {\n        const resolvedScope = this.scopeResolver.resolve({\n            inline: undefined,\n            provider: providerScope,\n        });\n        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n        return this.service._loadDependencies(this.path, inlineLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoPipe, deps: [{ token: TranslocoService }, { token: TRANSLOCO_SCOPE, optional: true }, { token: TRANSLOCO_LANG, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });\n    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoPipe, isStandalone: true, name: \"transloco\", pure: false });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'transloco',\n                    pure: false,\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: TranslocoService }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_SCOPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_LANG]\n                }] }, { type: i0.ChangeDetectorRef }] });\n\nconst decl = [TranslocoDirective, TranslocoPipe];\nclass TranslocoModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoModule, imports: [TranslocoDirective, TranslocoPipe], exports: [TranslocoDirective, TranslocoPipe] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: decl,\n                    exports: decl,\n                }]\n        }] });\n\nfunction provideTransloco(options) {\n    const providers = [\n        provideTranslocoTranspiler(DefaultTranspiler),\n        provideTranslocoMissingHandler(DefaultMissingHandler),\n        provideTranslocoInterceptor(DefaultInterceptor),\n        provideTranslocoFallbackStrategy(DefaultFallbackStrategy),\n    ];\n    if (options.config) {\n        providers.push(provideTranslocoConfig(options.config));\n    }\n    if (options.loader) {\n        providers.push(provideTranslocoLoader(options.loader));\n    }\n    return providers;\n}\nfunction provideTranslocoConfig(config) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_CONFIG,\n            useValue: translocoConfig(config),\n        },\n    ]);\n}\nfunction provideTranslocoLoader(loader) {\n    return makeEnvironmentProviders([\n        { provide: TRANSLOCO_LOADER, useClass: loader },\n    ]);\n}\nfunction provideTranslocoScope(...scopes) {\n    return scopes.map((scope) => ({\n        provide: TRANSLOCO_SCOPE,\n        useValue: scope,\n        multi: true,\n    }));\n}\nfunction provideTranslocoLoadingTpl(content) {\n    return {\n        provide: TRANSLOCO_LOADING_TEMPLATE,\n        useValue: content,\n    };\n}\nfunction provideTranslocoTranspiler(transpiler) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_TRANSPILER,\n            useClass: transpiler,\n            deps: [TRANSLOCO_CONFIG],\n        },\n    ]);\n}\nfunction provideTranslocoFallbackStrategy(strategy) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_FALLBACK_STRATEGY,\n            useClass: strategy,\n            deps: [TRANSLOCO_CONFIG],\n        },\n    ]);\n}\nfunction provideTranslocoMissingHandler(handler) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_MISSING_HANDLER,\n            useClass: handler,\n        },\n    ]);\n}\nfunction provideTranslocoInterceptor(interceptor) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_INTERCEPTOR,\n            useClass: interceptor,\n        },\n    ]);\n}\nfunction provideTranslocoLang(lang) {\n    return {\n        provide: TRANSLOCO_LANG,\n        useValue: lang,\n    };\n}\n\nconst TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');\nclass TestingLoader {\n    langs;\n    constructor(langs) {\n        this.langs = langs;\n    }\n    getTranslation(lang) {\n        return of(this.langs[lang]);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TestingLoader, deps: [{ token: TRANSLOCO_TEST_LANGS }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TestingLoader });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TestingLoader, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_TEST_LANGS]\n                }] }] });\nfunction initTranslocoService(service, langs = {}, options) {\n    const preloadAllLangs = () => options.preloadLangs\n        ? Promise.all(Object.keys(langs).map((lang) => service.load(lang).toPromise()))\n        : Promise.resolve();\n    return preloadAllLangs;\n}\nclass TranslocoTestingModule {\n    static forRoot(options) {\n        return {\n            ngModule: TranslocoTestingModule,\n            providers: [\n                provideTransloco({\n                    loader: TestingLoader,\n                    config: {\n                        prodMode: true,\n                        missingHandler: { logMissingKey: false },\n                        ...options.translocoConfig,\n                    },\n                }),\n                {\n                    provide: TRANSLOCO_TEST_LANGS,\n                    useValue: options.langs,\n                },\n                {\n                    provide: TRANSLOCO_TEST_OPTIONS,\n                    useValue: options,\n                },\n                {\n                    provide: APP_INITIALIZER,\n                    useFactory: initTranslocoService,\n                    deps: [\n                        TranslocoService,\n                        TRANSLOCO_TEST_LANGS,\n                        TRANSLOCO_TEST_OPTIONS,\n                    ],\n                    multi: true,\n                },\n            ],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoTestingModule, exports: [TranslocoModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoTestingModule, imports: [TranslocoModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.9\", ngImport: i0, type: TranslocoTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [TranslocoModule],\n                }]\n        }] });\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nfunction getBrowserLang() {\n    let browserLang = getBrowserCultureLang();\n    if (!browserLang || !isBrowser()) {\n        return undefined;\n    }\n    if (browserLang.indexOf('-') !== -1) {\n        browserLang = browserLang.split('-')[0];\n    }\n    if (browserLang.indexOf('_') !== -1) {\n        browserLang = browserLang.split('_')[0];\n    }\n    return browserLang;\n}\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nfunction getBrowserCultureLang() {\n    if (!isBrowser()) {\n        return '';\n    }\n    const navigator = window.navigator;\n    return navigator.languages?.[0] ?? navigator.language;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultFallbackStrategy, DefaultInterceptor, DefaultMissingHandler, DefaultTranspiler, FunctionalTranspiler, TRANSLOCO_CONFIG, TRANSLOCO_FALLBACK_STRATEGY, TRANSLOCO_INTERCEPTOR, TRANSLOCO_LANG, TRANSLOCO_LOADER, TRANSLOCO_LOADING_TEMPLATE, TRANSLOCO_MISSING_HANDLER, TRANSLOCO_SCOPE, TRANSLOCO_TRANSPILER, TestingLoader, TranslocoDirective, TranslocoModule, TranslocoPipe, TranslocoService, TranslocoTestingModule, coerceArray, defaultConfig, flatten, getBrowserCultureLang, getBrowserLang, getFunctionArgs, getLangFromScope, getPipeValue, getScopeFromLang, getValue, hasInlineLoader, isBrowser, isDefined, isEmpty, isFunction, isNil, isNumber, isObject, isScopeObject, isString, provideTransloco, provideTranslocoConfig, provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoLang, provideTranslocoLoader, provideTranslocoLoadingTpl, provideTranslocoMissingHandler, provideTranslocoScope, provideTranslocoTranspiler, setValue, size, toCamelCase, toNumber, translate, translateObject, translocoConfig, unflatten };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,eAAe,QAAQ,eAAe;AAC3Q,SAASC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,KAAK,QAAQ,MAAM;AACpJ,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,SAAS,IAAIC,WAAW,EAAEC,OAAO,IAAIC,SAAS,QAAQ,MAAM;AAErE,MAAMC,aAAa,CAAC;EAChBC,YAAY;EACZC,WAAWA,CAACD,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOvB,EAAE,CAAC,IAAI,CAACoB,YAAY,CAACI,GAAG,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD;AACJ;AACA,MAAME,gBAAgB,GAAG,IAAI5C,cAAc,CAAC,kBAAkB,CAAC;AAE/D,SAAS6C,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAI,CAACD,GAAG,EAAE;IACN,OAAOA,GAAG;EACd;EACA;EACA,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,GAAG,EAAEC,IAAI,CAAC,EAAE;IACjD,OAAOD,GAAG,CAACC,IAAI,CAAC;EACpB;EACA,OAAOA,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,EAAET,GAAG,CAAC;AACxD;AACA,SAASU,QAAQA,CAACV,GAAG,EAAEW,IAAI,EAAEC,GAAG,EAAE;EAC9BZ,GAAG,GAAG;IAAE,GAAGA;EAAI,CAAC;EAChB,MAAMM,KAAK,GAAGK,IAAI,CAACL,KAAK,CAAC,GAAG,CAAC;EAC7B,MAAMO,SAAS,GAAGP,KAAK,CAACQ,MAAM,GAAG,CAAC;EAClCR,KAAK,CAACC,MAAM,CAAC,CAACQ,GAAG,EAAEC,IAAI,EAAEC,KAAK,KAAK;IAC/B,IAAIA,KAAK,KAAKJ,SAAS,EAAE;MACrBE,GAAG,CAACC,IAAI,CAAC,GAAGJ,GAAG;IACnB,CAAC,MACI;MACDG,GAAG,CAACC,IAAI,CAAC,GAAGE,KAAK,CAACC,OAAO,CAACJ,GAAG,CAACC,IAAI,CAAC,CAAC,GAC9BD,GAAG,CAACC,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,GACjB;QAAE,GAAGL,GAAG,CAACC,IAAI;MAAE,CAAC;IAC1B;IACA,OAAOD,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAC3B,CAAC,EAAEhB,GAAG,CAAC;EACP,OAAOA,GAAG;AACd;AACA,SAASqB,IAAIA,CAACC,UAAU,EAAE;EACtB,IAAI,CAACA,UAAU,EAAE;IACb,OAAO,CAAC;EACZ;EACA,IAAIJ,KAAK,CAACC,OAAO,CAACG,UAAU,CAAC,EAAE;IAC3B,OAAOA,UAAU,CAACR,MAAM;EAC5B;EACA,IAAIS,QAAQ,CAACD,UAAU,CAAC,EAAE;IACtB,OAAOpB,MAAM,CAACsB,IAAI,CAACF,UAAU,CAAC,CAACR,MAAM;EACzC;EACA,OAAOQ,UAAU,GAAGA,UAAU,CAACR,MAAM,GAAG,CAAC;AAC7C;AACA,SAASW,OAAOA,CAACH,UAAU,EAAE;EACzB,OAAOD,IAAI,CAACC,UAAU,CAAC,KAAK,CAAC;AACjC;AACA,SAASI,UAAUA,CAACd,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAG,KAAK,UAAU;AACpC;AACA,SAASe,QAAQA,CAACf,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,SAASgB,QAAQA,CAAChB,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,SAASW,QAAQA,CAACM,IAAI,EAAE;EACpB,OAAO,CAAC,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACX,KAAK,CAACC,OAAO,CAACU,IAAI,CAAC;AACrE;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAOb,KAAK,CAACC,OAAO,CAACY,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CACLC,OAAO,CAAC,qBAAqB,EAAE,CAACC,IAAI,EAAElB,KAAK,KAAKA,KAAK,IAAI,CAAC,GAAGkB,IAAI,CAACC,WAAW,CAAC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC,CACrGH,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AACnC;AACA,SAASI,SAASA,CAAA,EAAG;EACjB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACxC;AACA,SAASC,KAAKA,CAACT,KAAK,EAAE;EAClB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKU,SAAS;AAChD;AACA,SAASC,SAASA,CAACX,KAAK,EAAE;EACtB,OAAOS,KAAK,CAACT,KAAK,CAAC,KAAK,KAAK;AACjC;AACA,SAASY,QAAQA,CAACZ,KAAK,EAAE;EACrB,IAAIH,QAAQ,CAACG,KAAK,CAAC,EACf,OAAOA,KAAK;EAChB,IAAIJ,QAAQ,CAACI,KAAK,CAAC,IAAI,CAACa,KAAK,CAACC,MAAM,CAACd,KAAK,CAAC,GAAGe,UAAU,CAACf,KAAK,CAAC,CAAC,EAAE;IAC9D,OAAOc,MAAM,CAACd,KAAK,CAAC;EACxB;EACA,OAAO,IAAI;AACf;AACA,SAASgB,aAAaA,CAAClB,IAAI,EAAE;EACzB,OAAOA,IAAI,IAAI,OAAOA,IAAI,CAACmB,KAAK,KAAK,QAAQ;AACjD;AACA,SAASC,eAAeA,CAACpB,IAAI,EAAE;EAC3B,OAAOA,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACqB,MAAM,CAAC;AACxC;AACA,SAAS9D,SAASA,CAACY,GAAG,EAAE;EACpB,OAAOX,WAAW,CAACW,GAAG,CAAC;AAC3B;AACA,SAASV,OAAOA,CAACU,GAAG,EAAE;EAClB,OAAOT,SAAS,CAACS,GAAG,EAAE;IAAEmD,IAAI,EAAE;EAAK,CAAC,CAAC;AACzC;AAEA,MAAMC,gBAAgB,GAAG,IAAIlG,cAAc,CAAC,kBAAkB,EAAE;EAC5DmG,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAMC;AACnB,CAAC,CAAC;AACF,MAAMA,aAAa,GAAG;EAClBC,WAAW,EAAE,IAAI;EACjBC,oBAAoB,EAAE,KAAK;EAC3BC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,EAAE;EAChBC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE;IACZC,aAAa,EAAE,IAAI;IACnBC,sBAAsB,EAAE,KAAK;IAC7BC,UAAU,EAAE;EAChB,CAAC;EACD3E,OAAO,EAAE;IACL4E,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI;AAC9B,CAAC;AACD,SAASC,eAAeA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAClC,OAAO;IACH,GAAGd,aAAa;IAChB,GAAGc,MAAM;IACTP,cAAc,EAAE;MACZ,GAAGP,aAAa,CAACO,cAAc;MAC/B,GAAGO,MAAM,CAACP;IACd,CAAC;IACDxE,OAAO,EAAE;MACL,GAAGiE,aAAa,CAACjE,OAAO;MACxB,GAAG+E,MAAM,CAAC/E;IACd;EACJ,CAAC;AACL;AAEA,MAAMgF,oBAAoB,GAAG,IAAIpH,cAAc,CAAC,sBAAsB,CAAC;AACvE,MAAMqH,iBAAiB,CAAC;EACpBF,MAAM,GAAGlH,MAAM,CAACiG,gBAAgB,EAAE;IAAEoB,QAAQ,EAAE;EAAK,CAAC,CAAC,IAAIjB,aAAa;EACtE,IAAIkB,oBAAoBA,CAAA,EAAG;IACvB,OAAOC,cAAc,CAAC,IAAI,CAACL,MAAM,CAAC;EACtC;EACAM,SAASA,CAAC;IAAE5C,KAAK;IAAE6C,MAAM,GAAG,CAAC,CAAC;IAAEC,WAAW;IAAEC;EAAI,CAAC,EAAE;IAChD,IAAInD,QAAQ,CAACI,KAAK,CAAC,EAAE;MACjB,IAAIgD,UAAU;MACd,IAAIC,WAAW,GAAGjD,KAAK;MACvB,OAAO,CAACgD,UAAU,GAAG,IAAI,CAACN,oBAAoB,CAACQ,IAAI,CAACD,WAAW,CAAC,MAAM,IAAI,EAAE;QACxE,MAAM,CAACE,KAAK,EAAEC,UAAU,CAAC,GAAGJ,UAAU;QACtCC,WAAW,GAAGA,WAAW,CAAC9C,OAAO,CAACgD,KAAK,EAAE,MAAM;UAC3C,MAAMA,KAAK,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC;UAC/B,MAAMC,KAAK,GAAGtF,QAAQ,CAAC6E,MAAM,EAAEM,KAAK,CAAC;UACrC,IAAIxC,SAAS,CAAC2C,KAAK,CAAC,EAAE;YAClB,OAAOA,KAAK;UAChB;UACA,OAAO3C,SAAS,CAACmC,WAAW,CAACK,KAAK,CAAC,CAAC,GAC9B,IAAI,CAACP,SAAS,CAAC;YACbC,MAAM;YACNC,WAAW;YACXC,GAAG;YACH/C,KAAK,EAAE8C,WAAW,CAACK,KAAK;UAC5B,CAAC,CAAC,GACA,EAAE;QACZ,CAAC,CAAC;MACN;MACA,OAAOF,WAAW;IACtB,CAAC,MACI,IAAIJ,MAAM,EAAE;MACb,IAAIrD,QAAQ,CAACQ,KAAK,CAAC,EAAE;QACjBA,KAAK,GAAG,IAAI,CAACuD,YAAY,CAAC;UACtBvD,KAAK,EAAEA,KAAK;UACZ6C,MAAM;UACNC,WAAW;UACXC;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAI5D,KAAK,CAACC,OAAO,CAACY,KAAK,CAAC,EAAE;QAC3BA,KAAK,GAAG,IAAI,CAACwD,WAAW,CAAC;UAAExD,KAAK;UAAE6C,MAAM;UAAEC,WAAW;UAAEC;QAAI,CAAC,CAAC;MACjE;IACJ;IACA,OAAO/C,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuD,YAAYA,CAAC;IAAEvD,KAAK;IAAE6C,MAAM,GAAG,CAAC,CAAC;IAAEC,WAAW;IAAEC;EAAK,CAAC,EAAE;IACpD,IAAIU,MAAM,GAAGzD,KAAK;IAClB7B,MAAM,CAACsB,IAAI,CAACoD,MAAM,CAAC,CAACa,OAAO,CAAEjF,CAAC,IAAK;MAC/B;MACA,MAAMkF,UAAU,GAAG,IAAI,CAACf,SAAS,CAAC;QAC9B;QACA5C,KAAK,EAAEhC,QAAQ,CAACyF,MAAM,EAAEhF,CAAC,CAAC;QAC1B;QACAoE,MAAM,EAAE7E,QAAQ,CAAC6E,MAAM,EAAEpE,CAAC,CAAC;QAC3BqE,WAAW;QACXC;MACJ,CAAC,CAAC;MACF;MACAU,MAAM,GAAG9E,QAAQ,CAAC8E,MAAM,EAAEhF,CAAC,EAAEkF,UAAU,CAAC;IAC5C,CAAC,CAAC;IACF,OAAOF,MAAM;EACjB;EACAD,WAAWA,CAAC;IAAExD,KAAK;IAAE,GAAG4D;EAAK,CAAC,EAAE;IAC5B,OAAO5D,KAAK,CAACvD,GAAG,CAAEoH,CAAC,IAAK,IAAI,CAACjB,SAAS,CAAC;MACnC5C,KAAK,EAAE6D,CAAC;MACR,GAAGD;IACP,CAAC,CAAC,CAAC;EACP;EACA,OAAOE,IAAI,YAAAC,0BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFxB,iBAAiB;EAAA;EACpH,OAAOyB,KAAK,kBAD6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EACY3B,iBAAiB;IAAAjB,OAAA,EAAjBiB,iBAAiB,CAAAsB;EAAA;AAC5H;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAH6FlJ,EAAE,CAAAmJ,iBAAA,CAGJ7B,iBAAiB,EAAc,CAAC;IAC/G8B,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;AACV,SAASsH,cAAcA,CAACL,MAAM,EAAE;EAC5B,MAAM,CAACiC,KAAK,EAAEC,GAAG,CAAC,GAAGlC,MAAM,CAACF,aAAa;EACzC,OAAO,IAAIqC,MAAM,CAAE,GAAEF,KAAM,MAAKA,KAAM,GAAEC,GAAI,OAAMA,GAAI,EAAC,EAAE,GAAG,CAAC;AACjE;AACA,SAASE,eAAeA,CAACC,UAAU,EAAE;EACjC,MAAMC,QAAQ,GAAGD,UAAU,GAAGA,UAAU,CAACpG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EACxD,MAAMsG,IAAI,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAAC7F,MAAM,EAAE+F,CAAC,EAAE,EAAE;IACtC,IAAI9E,KAAK,GAAG4E,QAAQ,CAACE,CAAC,CAAC,CAACzB,IAAI,CAAC,CAAC;IAC9B,OAAOrD,KAAK,CAACA,KAAK,CAACjB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;MACrC+F,CAAC,EAAE;MACH9E,KAAK,GAAGA,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGyE,QAAQ,CAACE,CAAC,CAAC;IAClD;IACAD,IAAI,CAACE,IAAI,CAAC/E,KAAK,CAAC;EACpB;EACA,OAAO6E,IAAI;AACf;AACA,MAAMG,oBAAoB,SAASxC,iBAAiB,CAAC;EACjDyC,QAAQ,GAAG7J,MAAM,CAACE,QAAQ,CAAC;EAC3BsH,SAASA,CAAC;IAAE5C,KAAK;IAAE,GAAG4D;EAAK,CAAC,EAAE;IAC1B,IAAID,UAAU,GAAG3D,KAAK;IACtB,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;MACjB2D,UAAU,GAAG3D,KAAK,CAACG,OAAO,CAAC,6BAA6B,EAAE,CAACgD,KAAK,EAAE+B,YAAY,EAAEL,IAAI,KAAK;QACrF,IAAI;UACA,MAAMM,IAAI,GAAG,IAAI,CAACF,QAAQ,CAACnH,GAAG,CAACoH,YAAY,CAAC;UAC5C,OAAOC,IAAI,CAACvC,SAAS,CAAC,GAAG8B,eAAe,CAACG,IAAI,CAAC,CAAC;QACnD,CAAC,CACD,OAAOO,CAAC,EAAE;UACN,IAAIC,OAAO,GAAI,0BAAyBrF,KAAM;AAClE,wHAAwHkF,YAAa,cAAa;UAC9H,IAAIE,CAAC,CAACC,OAAO,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YACzCD,OAAO,GAAI,sBAAqBH,YAAa,2DAA0D;UAC3G;UACA,MAAM,IAAIK,KAAK,CAACF,OAAO,CAAC;QAC5B;MACJ,CAAC,CAAC;IACN;IACA,OAAO,KAAK,CAACzC,SAAS,CAAC;MAAE5C,KAAK,EAAE2D,UAAU;MAAE,GAAGC;IAAK,CAAC,CAAC;EAC1D;EACA,OAAOE,IAAI;IAAA,IAAA0B,iCAAA;IAAA,gBAAAC,6BAAAzB,CAAA;MAAA,QAAAwB,iCAAA,KAAAA,iCAAA,GA7C8EtK,EAAE,CAAAwK,qBAAA,CA6CQV,oBAAoB,IAAAhB,CAAA,IAApBgB,oBAAoB;IAAA;EAAA;EACvH,OAAOf,KAAK,kBA9C6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EA8CYa,oBAAoB;IAAAzD,OAAA,EAApByD,oBAAoB,CAAAlB;EAAA;AAC/H;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAhD6FlJ,EAAE,CAAAmJ,iBAAA,CAgDJW,oBAAoB,EAAc,CAAC;IAClHV,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;AAEV,MAAMsK,yBAAyB,GAAG,IAAIxK,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAMyK,qBAAqB,CAAC;EACxBC,MAAMA,CAAC9C,GAAG,EAAET,MAAM,EAAE;IAChB,IAAIA,MAAM,CAACP,cAAc,CAACC,aAAa,IAAI,CAACM,MAAM,CAACX,QAAQ,EAAE;MACzD,MAAMmE,GAAG,GAAI,4BAA2B/C,GAAI,GAAE;MAC9CgD,OAAO,CAACC,IAAI,CAAE,MAAKF,GAAI,EAAC,EAAE,6BAA6B,CAAC;IAC5D;IACA,OAAO/C,GAAG;EACd;EACA,OAAOe,IAAI,YAAAmC,8BAAAjC,CAAA;IAAA,YAAAA,CAAA,IAAwF4B,qBAAqB;EAAA;EACxH,OAAO3B,KAAK,kBA9D6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EA8DYyB,qBAAqB;IAAArE,OAAA,EAArBqE,qBAAqB,CAAA9B;EAAA;AAChI;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAhE6FlJ,EAAE,CAAAmJ,iBAAA,CAgEJuB,qBAAqB,EAAc,CAAC;IACnHtB,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;AAEV,MAAM6K,qBAAqB,GAAG,IAAI/K,cAAc,CAAC,uBAAuB,CAAC;AACzE,MAAMgL,kBAAkB,CAAC;EACrBC,kBAAkBA,CAACtD,WAAW,EAAE;IAC5B,OAAOA,WAAW;EACtB;EACAuD,qBAAqBA,CAACC,CAAC,EAAEtG,KAAK,EAAE;IAC5B,OAAOA,KAAK;EAChB;EACA,OAAO8D,IAAI,YAAAyC,2BAAAvC,CAAA;IAAA,YAAAA,CAAA,IAAwFmC,kBAAkB;EAAA;EACrH,OAAOlC,KAAK,kBA7E6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EA6EYgC,kBAAkB;IAAA5E,OAAA,EAAlB4E,kBAAkB,CAAArC;EAAA;AAC7H;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA/E6FlJ,EAAE,CAAAmJ,iBAAA,CA+EJ8B,kBAAkB,EAAc,CAAC;IAChH7B,IAAI,EAAEjJ;EACV,CAAC,CAAC;AAAA;AAEV,MAAMmL,2BAA2B,GAAG,IAAIrL,cAAc,CAAC,6BAA6B,CAAC;AACrF,MAAMsL,uBAAuB,CAAC;EAC1BC,UAAU;EACV/I,WAAWA,CAAC+I,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,YAAYA,CAAA,EAAG;IACX,MAAM9E,YAAY,GAAG,IAAI,CAAC6E,UAAU,CAAC7E,YAAY;IACjD,IAAI,CAACA,YAAY,EAAE;MACf,MAAM,IAAI0D,KAAK,CAAC,sFAAsF,CAAC;IAC3G;IACA,OAAOpG,KAAK,CAACC,OAAO,CAACyC,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;EACtE;EACA,OAAOiC,IAAI,YAAA8C,gCAAA5C,CAAA;IAAA,YAAAA,CAAA,IAAwFyC,uBAAuB,EAhGjCvL,EAAE,CAAA2L,QAAA,CAgGiDxF,gBAAgB;EAAA;EAC5J,OAAO4C,KAAK,kBAjG6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EAiGYsC,uBAAuB;IAAAlF,OAAA,EAAvBkF,uBAAuB,CAAA3C;EAAA;AAClI;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAnG6FlJ,EAAE,CAAAmJ,iBAAA,CAmGJoC,uBAAuB,EAAc,CAAC;IACrHnC,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAC/CxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACxD,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0F,gBAAgBA,CAAClJ,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,MAAMU,KAAK,GAAGV,IAAI,CAACU,KAAK,CAAC,GAAG,CAAC;EAC7BA,KAAK,CAACyI,GAAG,CAAC,CAAC;EACX,OAAOzI,KAAK,CAAC0I,IAAI,CAAC,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACrJ,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,OAAOA,IAAI,CAACU,KAAK,CAAC,GAAG,CAAC,CAACyI,GAAG,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACjH,GAAG,EAAEF,KAAK,EAAEoH,IAAI,GAAG,GAAG,EAAE;EAC1C,IAAIxH,QAAQ,CAACM,GAAG,CAAC,EAAE;IACf,MAAM0E,QAAQ,GAAG1E,GAAG,CAAC3B,KAAK,CAAC6I,IAAI,CAAC;IAChC,MAAMC,QAAQ,GAAGzC,QAAQ,CAACoC,GAAG,CAAC,CAAC;IAC/B,OAAOK,QAAQ,KAAKrH,KAAK,GAAG,CAAC,IAAI,EAAE4E,QAAQ,CAAC0C,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAED,QAAQ,CAAC;EAC/E;EACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACtB;AACA,SAASE,yBAAyBA,CAACC,OAAO,EAAE3J,IAAI,EAAE;EAC9C,MAAM,CAAC4J,SAAS,CAAC,GAAGN,YAAY,CAACtJ,IAAI,EAAE,QAAQ,CAAC;EAChD,IAAI,CAAC4J,SAAS,EAAE;IACZ;IACA,OAAO,CAAC,CAACD,OAAO,CAAClF,MAAM,CAACZ,oBAAoB;EAChD;EACA;EACA,OAAO,KAAK;AAChB;AACA,SAASgG,mBAAmBA,CAACC,kBAAkB,EAAE;EAC7C,OAAOA,kBAAkB,GAAIC,MAAM,IAAKA,MAAM,GAAGrL,IAAI,CAAC,CAAC,CAAC;AAC5D;AACA,SAASsL,YAAYA,CAACC,YAAY,EAAE7G,KAAK,EAAE;EACvC,OAAO9C,MAAM,CAACsB,IAAI,CAACqI,YAAY,CAAC,CAACtJ,MAAM,CAAC,CAACQ,GAAG,EAAEnB,IAAI,KAAK;IACnDmB,GAAG,CAAE,GAAEiC,KAAM,IAAGpD,IAAK,EAAC,CAAC,GAAGiK,YAAY,CAACjK,IAAI,CAAC;IAC5C,OAAOmB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,SAAS+I,mBAAmBA,CAACC,aAAa,EAAE/G,KAAK,EAAE;EAC/C,OAAOC,eAAe,CAAC8G,aAAa,CAAC,GAC/BH,YAAY,CAACG,aAAa,CAAC7G,MAAM,EAAEF,KAAK,CAAC,GACzCP,SAAS;AACnB;AACA,SAASuH,eAAeA,CAACpK,IAAI,EAAE;EAC3B,OAAO;IACHoD,KAAK,EAAE8F,gBAAgB,CAAClJ,IAAI,CAAC,IAAI,IAAI;IACrCqK,QAAQ,EAAEhB,gBAAgB,CAACrJ,IAAI;EACnC,CAAC;AACL;AAEA,SAASsK,aAAaA,CAACC,OAAO,EAAE;EAC5B,MAAM;IAAElK,IAAI;IAAE4J,YAAY;IAAEO,UAAU;IAAEC;EAAK,CAAC,GAAGF,OAAO;EACxD,IAAIN,YAAY,EAAE;IACd,MAAMS,UAAU,GAAGT,YAAY,CAAC5J,IAAI,CAAC;IACrC,IAAIyB,UAAU,CAAC4I,UAAU,CAAC,KAAK,KAAK,EAAE;MAClC,MAAO,iEAAgErK,IAAK,EAAC;IACjF;IACA,OAAO4J,YAAY,CAAC5J,IAAI,CAAC,CAAC,CAAC,CAACsK,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,GAAGD,GAAG,CAAC;EAC9E;EACA,OAAOJ,UAAU,CAACzK,cAAc,CAACM,IAAI,EAAEoK,IAAI,CAAC;AAChD;AAEA,SAASK,mBAAmBA,CAAC;EAAEN,UAAU;EAAEnK,IAAI;EAAEoK,IAAI;EAAEM,YAAY;EAAEd;AAAc,CAAC,EAAE;EAClF,MAAMe,KAAK,GAAGD,YAAY,GAAG,CAAC1K,IAAI,EAAE0K,YAAY,CAAC,GAAG,CAAC1K,IAAI,CAAC;EAC1D,OAAO2K,KAAK,CAACpM,GAAG,CAAEyB,IAAI,IAAK;IACvB,MAAMiD,MAAM,GAAGgH,aAAa,CAAC;MAAEjK,IAAI;MAAEmK,UAAU;MAAEP,YAAY;MAAEQ;IAAK,CAAC,CAAC;IACtE,OAAO9L,IAAI,CAAC2E,MAAM,CAAC,CAAC2H,IAAI,CAACrM,GAAG,CAAEqG,WAAW,KAAM;MAC3CA,WAAW;MACXjF,IAAI,EAAEK;IACV,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC;AACN;AAEA,IAAIsJ,OAAO;AACX,SAASuB,SAASA,CAAChG,GAAG,EAAEF,MAAM,GAAG,CAAC,CAAC,EAAEhF,IAAI,EAAE;EACvC,OAAO2J,OAAO,CAACuB,SAAS,CAAChG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC;AAC/C;AACA,SAASmL,eAAeA,CAACjG,GAAG,EAAEF,MAAM,GAAG,CAAC,CAAC,EAAEhF,IAAI,EAAE;EAC7C,OAAO2J,OAAO,CAACwB,eAAe,CAACjG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC;AACrD;AACA,MAAMoL,gBAAgB,CAAC;EACnB9H,MAAM;EACN+H,MAAM;EACNnH,cAAc;EACdoH,WAAW;EACXC,gBAAgB;EAChBC,YAAY;EACZ3L,YAAY,GAAG,IAAI4L,GAAG,CAAC,CAAC;EACxBC,KAAK,GAAG,IAAID,GAAG,CAAC,CAAC;EACjBE,iBAAiB;EACjB/H,WAAW,GAAG,EAAE;EAChBK,cAAc,GAAG,EAAE;EACnB2H,qBAAqB,GAAG,KAAK;EAC7B5L,IAAI;EACJ6L,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvBC,MAAM,GAAG,IAAIlN,OAAO,CAAC,CAAC;EACtBmN,OAAO,GAAG,IAAI,CAACD,MAAM,CAACE,YAAY,CAAC,CAAC;EACpCxH,MAAM;EACN3E,WAAWA,CAACwD,MAAM,EAAE+H,MAAM,EAAEnH,cAAc,EAAEoH,WAAW,EAAEzC,UAAU,EAAE0C,gBAAgB,EAAE;IACnF,IAAI,CAACjI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+H,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACnH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACoH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC,IAAI,CAACjI,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAI1D,aAAa,CAAC,IAAI,CAACC,YAAY,CAAC;IACtD;IACA8J,OAAO,GAAG,IAAI;IACd,IAAI,CAAClF,MAAM,GAAGyH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvD,UAAU,CAAC,CAAC;IACpD,IAAI,CAACwD,iBAAiB,CAAC,IAAI,CAAC5H,MAAM,CAACR,cAAc,IAAI,EAAE,CAAC;IACxD,IAAI,CAACqI,oCAAoC,CAAC,IAAI,CAAC7H,MAAM,CAAC;IACtD,IAAI,CAAC8H,cAAc,CAAC,IAAI,CAAC9H,MAAM,CAACb,WAAW,CAAC;IAC5C,IAAI,CAAC5D,IAAI,GAAG,IAAIlB,eAAe,CAAC,IAAI,CAAC0N,cAAc,CAAC,CAAC,CAAC;IACtD;IACA;IACA,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACxL,IAAI,CAACiM,YAAY,CAAC,CAAC;IAC5C;AACR;AACA;IACQ,IAAI,CAACD,OAAO,CAACf,IAAI,CAAC1L,kBAAkB,CAAC,CAAC,CAAC,CAACkN,SAAS,CAAElF,CAAC,IAAK;MACrD,IAAIA,CAAC,CAACd,IAAI,KAAK,wBAAwB,IAAIc,CAAC,CAACmF,UAAU,EAAE;QACrD,IAAI,CAACC,aAAa,CAACpF,CAAC,CAACqF,OAAO,CAACvC,QAAQ,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAmC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC5I,WAAW;EAC3B;EACA2I,cAAcA,CAACvM,IAAI,EAAE;IACjB,IAAI,CAAC4D,WAAW,GAAG5D,IAAI;EAC3B;EACA6M,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7M,IAAI,CAACG,QAAQ,CAAC,CAAC;EAC/B;EACAwM,aAAaA,CAAC3M,IAAI,EAAE;IAChB,IAAI,CAACqL,MAAM,CAACyB,aAAa,GAAG9M,IAAI,CAAC;IACjC,IAAI,CAACA,IAAI,CAAC+M,IAAI,CAAC/M,IAAI,CAAC;IACpB,IAAI,CAAC+L,MAAM,CAACgB,IAAI,CAAC;MACbtG,IAAI,EAAE,aAAa;MACnBmG,OAAO,EAAExC,eAAe,CAACpK,IAAI;IACjC,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAqM,iBAAiBA,CAACW,KAAK,EAAE;IACrB,IAAI,CAAC/I,cAAc,GAAG+I,KAAK;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChJ,cAAc;EAC9B;EACAiJ,IAAIA,CAAC7M,IAAI,EAAEkK,OAAO,GAAG,CAAC,CAAC,EAAE;IACrB,MAAM4C,MAAM,GAAG,IAAI,CAACzB,KAAK,CAACzL,GAAG,CAACI,IAAI,CAAC;IACnC,IAAI8M,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,IAAIC,eAAe;IACnB,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACjN,IAAI,CAAC;IACxC,IAAI+C,KAAK;IACT,IAAIiK,OAAO,EAAE;MACTjK,KAAK,GAAG8F,gBAAgB,CAAC7I,IAAI,CAAC;IAClC;IACA,MAAMkN,cAAc,GAAG;MACnBlN,IAAI;MACJmK,UAAU,EAAE,IAAI,CAAClH,MAAM;MACvB2G,YAAY,EAAEM,OAAO,CAACN,YAAY;MAClCQ,IAAI,EAAE4C,OAAO,GAAG;QAAEjK,KAAK,EAAEA;MAAM,CAAC,GAAGP;IACvC,CAAC;IACD,IAAI,IAAI,CAACuB,sBAAsB,CAAC/D,IAAI,CAAC,EAAE;MACnC;MACA,MAAMmN,QAAQ,GAAGH,OAAO,GACjB,GAAEjK,KAAM,IAAG,IAAI,CAACuI,iBAAkB,EAAC,GACpC,IAAI,CAACA,iBAAiB;MAC5B,MAAM8B,OAAO,GAAG3C,mBAAmB,CAAC;QAChC,GAAGyC,cAAc;QACjBxC,YAAY,EAAEyC;MAClB,CAAC,CAAC;MACFJ,eAAe,GAAGrO,QAAQ,CAAC0O,OAAO,CAAC;IACvC,CAAC,MACI;MACD,MAAMnK,MAAM,GAAGgH,aAAa,CAACiD,cAAc,CAAC;MAC5CH,eAAe,GAAGzO,IAAI,CAAC2E,MAAM,CAAC;IAClC;IACA,MAAMoK,KAAK,GAAGN,eAAe,CAACnC,IAAI,CAACjM,KAAK,CAAC,IAAI,CAACyF,MAAM,CAACV,aAAa,CAAC,EAAE9E,GAAG,CAAEgG,WAAW,IAAK;MACtF,IAAI3D,KAAK,CAACC,OAAO,CAAC0D,WAAW,CAAC,EAAE;QAC5BA,WAAW,CAACY,OAAO,CAAEM,CAAC,IAAK;UACvB,IAAI,CAACwH,aAAa,CAACxH,CAAC,CAACnG,IAAI,EAAEmG,CAAC,CAAClB,WAAW,CAAC;UACzC;UACA,IAAIkB,CAAC,CAACnG,IAAI,KAAKK,IAAI,EAAE;YACjB,IAAI,CAACqL,KAAK,CAACkC,GAAG,CAACzH,CAAC,CAACnG,IAAI,EAAEvB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACJ,CAAC,CAAC;QACF;MACJ;MACA,IAAI,CAACkP,aAAa,CAACtN,IAAI,EAAE4E,WAAW,CAAC;IACzC,CAAC,CAAC,EAAE/F,UAAU,CAAE2O,KAAK,IAAK;MACtB,IAAI,CAAC,IAAI,CAACpJ,MAAM,CAACX,QAAQ,EAAE;QACvBoE,OAAO,CAAC2F,KAAK,CAAE,+BAA8BxN,IAAK,GAAE,EAAEwN,KAAK,CAAC;MAChE;MACA,OAAO,IAAI,CAACC,aAAa,CAACzN,IAAI,EAAEkK,OAAO,CAAC;IAC5C,CAAC,CAAC,EAAEpL,WAAW,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,CAACuM,KAAK,CAACkC,GAAG,CAACvN,IAAI,EAAEqN,KAAK,CAAC;IAC3B,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxC,SAASA,CAAChG,GAAG,EAAEF,MAAM,GAAG,CAAC,CAAC,EAAEhF,IAAI,GAAG,IAAI,CAAC6M,aAAa,CAAC,CAAC,EAAE;IACrD,IAAI,CAAC3H,GAAG,EACJ,OAAOA,GAAG;IACd,MAAM;MAAE9B,KAAK;MAAE2K;IAAY,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAAChO,IAAI,CAAC;IAC7D,IAAIsB,KAAK,CAACC,OAAO,CAAC2D,GAAG,CAAC,EAAE;MACpB,OAAOA,GAAG,CAACtG,GAAG,CAAEqP,CAAC,IAAK,IAAI,CAAC/C,SAAS,CAAC9H,KAAK,GAAI,GAAEA,KAAM,IAAG6K,CAAE,EAAC,GAAGA,CAAC,EAAEjJ,MAAM,EAAE+I,WAAW,CAAC,CAAC;IAC3F;IACA7I,GAAG,GAAG9B,KAAK,GAAI,GAAEA,KAAM,IAAG8B,GAAI,EAAC,GAAGA,GAAG;IACrC,MAAMD,WAAW,GAAG,IAAI,CAAClF,cAAc,CAACgO,WAAW,CAAC;IACpD,MAAM5L,KAAK,GAAG8C,WAAW,CAACC,GAAG,CAAC;IAC9B,IAAI,CAAC/C,KAAK,EAAE;MACR,OAAO,IAAI,CAAC+L,iBAAiB,CAAChJ,GAAG,EAAE/C,KAAK,EAAE6C,MAAM,CAAC;IACrD;IACA,OAAO,IAAI,CAACqG,MAAM,CAACtG,SAAS,CAAC;MACzB5C,KAAK;MACL6C,MAAM;MACNC,WAAW;MACXC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiJ,eAAeA,CAACjJ,GAAG,EAAEF,MAAM,EAAEhF,IAAI,EAAEoO,SAAS,GAAG,KAAK,EAAE;IAClD,IAAInE,YAAY;IAChB,MAAMiD,IAAI,GAAGA,CAAClN,IAAI,EAAEuK,OAAO,KAAK,IAAI,CAAC2C,IAAI,CAAClN,IAAI,EAAEuK,OAAO,CAAC,CAACU,IAAI,CAACrM,GAAG,CAAC,MAAMwP,SAAS,GAC3E,IAAI,CAACjD,eAAe,CAACjG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC,GACvC,IAAI,CAACkL,SAAS,CAAChG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC,CAAC,CAAC;IACzC,IAAI4C,KAAK,CAAC5C,IAAI,CAAC,EAAE;MACb,OAAO,IAAI,CAACwL,YAAY,CAACP,IAAI,CAAC7L,SAAS,CAAEY,IAAI,IAAKkN,IAAI,CAAClN,IAAI,CAAC,CAAC,CAAC;IAClE;IACAA,IAAI,GAAGsB,KAAK,CAACC,OAAO,CAACvB,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;IAC3C,IAAImD,aAAa,CAACnD,IAAI,CAAC,EAAE;MACrB;MACA,MAAMmK,aAAa,GAAGnK,IAAI;MAC1BA,IAAI,GAAGmK,aAAa,CAAC/G,KAAK;MAC1B6G,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEA,aAAa,CAAC/G,KAAK,CAAC;IAC1E;IACApD,IAAI,GAAGA,IAAI;IACX,IAAI,IAAI,CAACqO,MAAM,CAACrO,IAAI,CAAC,IAAI,IAAI,CAACsO,eAAe,CAACtO,IAAI,CAAC,EAAE;MACjD,OAAOkN,IAAI,CAAClN,IAAI,CAAC;IACrB;IACA;IACA,MAAMoD,KAAK,GAAGpD,IAAI;IAClB,OAAO,IAAI,CAACwL,YAAY,CAACP,IAAI,CAAC7L,SAAS,CAAEY,IAAI,IAAKkN,IAAI,CAAE,GAAE9J,KAAM,IAAGpD,IAAK,EAAC,EAAE;MAAEiK;IAAa,CAAC,CAAC,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqE,eAAeA,CAACtO,IAAI,EAAE;IAClB,OAAO,IAAI,CAACqO,MAAM,CAAChF,gBAAgB,CAACrJ,IAAI,CAAC,CAAC;EAC9C;EACAmL,eAAeA,CAACjG,GAAG,EAAEF,MAAM,GAAG,CAAC,CAAC,EAAEhF,IAAI,GAAG,IAAI,CAAC6M,aAAa,CAAC,CAAC,EAAE;IAC3D,IAAI9K,QAAQ,CAACmD,GAAG,CAAC,IAAI5D,KAAK,CAACC,OAAO,CAAC2D,GAAG,CAAC,EAAE;MACrC,MAAM;QAAE6I,WAAW;QAAE3K;MAAM,CAAC,GAAG,IAAI,CAAC4K,mBAAmB,CAAChO,IAAI,CAAC;MAC7D,IAAIsB,KAAK,CAACC,OAAO,CAAC2D,GAAG,CAAC,EAAE;QACpB,OAAOA,GAAG,CAACtG,GAAG,CAAEqP,CAAC,IAAK,IAAI,CAAC9C,eAAe,CAAC/H,KAAK,GAAI,GAAEA,KAAM,IAAG6K,CAAE,EAAC,GAAGA,CAAC,EAAEjJ,MAAM,EAAE+I,WAAW,CAAC,CAAC;MACjG;MACA,MAAM9I,WAAW,GAAG,IAAI,CAAClF,cAAc,CAACgO,WAAW,CAAC;MACpD7I,GAAG,GAAG9B,KAAK,GAAI,GAAEA,KAAM,IAAG8B,GAAI,EAAC,GAAGA,GAAG;MACrC,MAAM/C,KAAK,GAAG3C,SAAS,CAAC,IAAI,CAAC+O,cAAc,CAACtJ,WAAW,EAAEC,GAAG,CAAC,CAAC;MAC9D;MACA,OAAOrD,OAAO,CAACM,KAAK,CAAC,GACf,IAAI,CAAC+I,SAAS,CAAChG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC,GACjC,IAAI,CAACqL,MAAM,CAACtG,SAAS,CAAC;QAAE5C,KAAK;QAAE6C,MAAM,EAAEA,MAAM;QAAEC,WAAW;QAAEC;MAAI,CAAC,CAAC;IAC5E;IACA,MAAMrF,YAAY,GAAG,EAAE;IACvB,KAAK,MAAM,CAAC2O,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACC,UAAU,CAACxJ,GAAG,CAAC,EAAE;MAChDrF,YAAY,CAACqH,IAAI,CAAC,IAAI,CAACiE,eAAe,CAACqD,IAAI,EAAEC,OAAO,EAAEzO,IAAI,CAAC,CAAC;IAChE;IACA,OAAOH,YAAY;EACvB;EACA8O,qBAAqBA,CAACzJ,GAAG,EAAEF,MAAM,EAAEhF,IAAI,EAAE;IACrC,IAAI+B,QAAQ,CAACmD,GAAG,CAAC,IAAI5D,KAAK,CAACC,OAAO,CAAC2D,GAAG,CAAC,EAAE;MACrC,OAAO,IAAI,CAACiJ,eAAe,CAACjJ,GAAG,EAAEF,MAAM,EAAEhF,IAAI,EAAE,IAAI,CAAC;IACxD;IACA,MAAM,CAAC,CAAC4O,QAAQ,EAAEC,WAAW,CAAC,EAAE,GAAG9I,IAAI,CAAC,GAAG,IAAI,CAAC2I,UAAU,CAACxJ,GAAG,CAAC;IAC/D;AACR;IACQ,OAAO,IAAI,CAACyJ,qBAAqB,CAACC,QAAQ,EAAEC,WAAW,EAAE7O,IAAI,CAAC,CAACiL,IAAI,CAACrM,GAAG,CAAEuD,KAAK,IAAK;MAC/E,MAAMtC,YAAY,GAAG,CAACsC,KAAK,CAAC;MAC5B,KAAK,MAAM,CAACqM,IAAI,EAAEC,OAAO,CAAC,IAAI1I,IAAI,EAAE;QAChClG,YAAY,CAACqH,IAAI,CAAC,IAAI,CAACiE,eAAe,CAACqD,IAAI,EAAEC,OAAO,EAAEzO,IAAI,CAAC,CAAC;MAChE;MACA,OAAOH,YAAY;IACvB,CAAC,CAAC,CAAC;EACP;EACAE,cAAcA,CAAC+O,WAAW,EAAE;IACxB,IAAIA,WAAW,EAAE;MACb,IAAI,IAAI,CAACT,MAAM,CAACS,WAAW,CAAC,EAAE;QAC1B,OAAO,IAAI,CAACjP,YAAY,CAACI,GAAG,CAAC6O,WAAW,CAAC,IAAI,CAAC,CAAC;MACnD,CAAC,MACI;QACD;QACA,MAAM;UAAE1L,KAAK;UAAE2K;QAAY,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACc,WAAW,CAAC;QACpE,MAAM7J,WAAW,GAAG,IAAI,CAACpF,YAAY,CAACI,GAAG,CAAC8N,WAAW,CAAC,IAAI,CAAC,CAAC;QAC5D,OAAO,IAAI,CAACQ,cAAc,CAACtJ,WAAW,EAAE7B,KAAK,CAAC;MAClD;IACJ;IACA,OAAO,IAAI,CAACvD,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkP,iBAAiBA,CAAC/O,IAAI,EAAE;IACpB,IAAIgP,SAAS,GAAG,IAAI,CAACxD,YAAY;IACjC,IAAIxL,IAAI,EAAE;MACN,MAAMiP,kBAAkB,GAAG5F,gBAAgB,CAACrJ,IAAI,CAAC,KAAKA,IAAI;MAC1D,IAAI,IAAI,CAACqO,MAAM,CAACrO,IAAI,CAAC,IAAIiP,kBAAkB,EAAE;QACzCD,SAAS,GAAGvQ,EAAE,CAACuB,IAAI,CAAC;MACxB,CAAC,MACI;QACDgP,SAAS,GAAG,IAAI,CAACxD,YAAY,CAACP,IAAI,CAACrM,GAAG,CAAEsQ,WAAW,IAAM,GAAElP,IAAK,IAAGkP,WAAY,EAAC,CAAC,CAAC;MACtF;IACJ;IACA,OAAOF,SAAS,CAAC/D,IAAI,CAAC7L,SAAS,CAAE+P,QAAQ,IAAK,IAAI,CAACjC,IAAI,CAACiC,QAAQ,CAAC,CAAClE,IAAI,CAACrM,GAAG,CAAC,MAAM,IAAI,CAACmB,cAAc,CAACoP,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACtH;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAACnK,WAAW,EAAEjF,IAAI,GAAG,IAAI,CAAC6M,aAAa,CAAC,CAAC,EAAEtC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnE,MAAM8E,QAAQ,GAAG;MAAEC,KAAK,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC;IAClD,MAAMC,aAAa,GAAG;MAAE,GAAGH,QAAQ;MAAE,GAAG9E;IAAQ,CAAC;IACjD,MAAMnH,KAAK,GAAG8F,gBAAgB,CAAClJ,IAAI,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAIyP,yBAAyB,GAAGxK,WAAW;IAC3C;IACA,IAAI7B,KAAK,EAAE;MACP,MAAM8B,GAAG,GAAG,IAAI,CAACwK,cAAc,CAACtM,KAAK,CAAC;MACtCqM,yBAAyB,GAAG/P,OAAO,CAAC;QAAE,CAACwF,GAAG,GAAGD;MAAY,CAAC,CAAC;IAC/D;IACA,MAAMiK,WAAW,GAAG9L,KAAK,GAAGiG,gBAAgB,CAACrJ,IAAI,CAAC,GAAGA,IAAI;IACzD,MAAM2P,iBAAiB,GAAG;MACtB,IAAIH,aAAa,CAACF,KAAK,IAAI,IAAI,CAACvP,cAAc,CAACmP,WAAW,CAAC,CAAC;MAC5D,GAAGO;IACP,CAAC;IACD,MAAMG,kBAAkB,GAAG,IAAI,CAACnL,MAAM,CAAC/E,OAAO,CAAC4E,GAAG,GAC5CqL,iBAAiB,GACjBjQ,OAAO,CAACiQ,iBAAiB,CAAC;IAChC,MAAME,QAAQ,GAAG,IAAI,CAACvE,WAAW,CAAC/C,kBAAkB,CAACqH,kBAAkB,EAAEV,WAAW,CAAC;IACrF,IAAI,CAACrP,YAAY,CAAC+N,GAAG,CAACsB,WAAW,EAAEW,QAAQ,CAAC;IAC5CL,aAAa,CAACD,UAAU,IAAI,IAAI,CAAC5C,aAAa,CAAC,IAAI,CAACE,aAAa,CAAC,CAAC,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiD,iBAAiBA,CAAC5K,GAAG,EAAE/C,KAAK,EAAEoI,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMvK,IAAI,GAAGuK,OAAO,CAACvK,IAAI,IAAI,IAAI,CAAC6M,aAAa,CAAC,CAAC;IACjD,MAAMgD,QAAQ,GAAG,IAAI,CAACvE,WAAW,CAAC9C,qBAAqB,CAACtD,GAAG,EAAE/C,KAAK,EAAEnC,IAAI,CAAC;IACzE,MAAM+P,QAAQ,GAAG;MACb,CAAC7K,GAAG,GAAG2K;IACX,CAAC;IACD,IAAI,CAACT,cAAc,CAACW,QAAQ,EAAE/P,IAAI,EAAE;MAAE,GAAGuK,OAAO;MAAE+E,KAAK,EAAE;IAAK,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;EACIhD,oCAAoCA,CAAC;IAAEtI;EAAc,CAAC,EAAE;IACpD,MAAMhE,IAAI,GAAGsB,KAAK,CAACC,OAAO,CAACyC,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;IACzE,IAAIA,YAAY,IAAI,IAAI,CAACI,sBAAsB,CAACpE,IAAI,CAAC,EAAE;MACnD,IAAI,CAAC2L,iBAAiB,GAAG3L,IAAI;IACjC;EACJ;EACA;AACJ;AACA;EACIkO,iBAAiBA,CAAChJ,GAAG,EAAE/C,KAAK,EAAE6C,MAAM,EAAE;IAClC,IAAI,IAAI,CAACP,MAAM,CAACP,cAAc,CAACG,UAAU,IAAIlC,KAAK,KAAK,EAAE,EAAE;MACvD,OAAO,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAACyJ,qBAAqB,IAAI,IAAI,CAACxH,sBAAsB,CAAC,CAAC,EAAE;MAC9D;MACA,IAAI,CAACwH,qBAAqB,GAAG,IAAI;MACjC,MAAMoE,aAAa,GAAG,IAAI,CAAC9E,SAAS,CAAChG,GAAG,EAAEF,MAAM,EAAE,IAAI,CAAC2G,iBAAiB,CAAC;MACzE,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,OAAOoE,aAAa;IACxB;IACA,OAAO,IAAI,CAAC9L,cAAc,CAAC8D,MAAM,CAAC9C,GAAG,EAAE,IAAI,CAAC+K,qBAAqB,CAAC,CAAC,EAAEjL,MAAM,CAAC;EAChF;EACA;AACJ;AACA;EACIsI,aAAaA,CAACtN,IAAI,EAAE;IAChB,OAAO,IAAI,CAACkQ,oBAAoB,CAAC,CAAC,CAACC,OAAO,CAACnQ,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqO,MAAMA,CAACrO,IAAI,EAAE;IACT,OAAO,IAAI,CAACkQ,oBAAoB,CAAC,CAAC,CAACC,OAAO,CAACnQ,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoQ,iBAAiBA,CAAC/P,IAAI,EAAE4J,YAAY,EAAE;IAClC,MAAMoG,QAAQ,GAAGhH,gBAAgB,CAAChJ,IAAI,CAAC;IACvC,IAAI,IAAI,CAACiN,aAAa,CAACjN,IAAI,CAAC,IAAI,CAAC,IAAI,CAACiQ,mBAAmB,CAACD,QAAQ,CAAC,EAAE;MACjE,OAAOhR,aAAa,CAAC,CACjB,IAAI,CAAC6N,IAAI,CAACmD,QAAQ,CAAC,EACnB,IAAI,CAACnD,IAAI,CAAC7M,IAAI,EAAE;QAAE4J;MAAa,CAAC,CAAC,CACpC,CAAC;IACN;IACA,OAAO,IAAI,CAACiD,IAAI,CAAC7M,IAAI,EAAE;MAAE4J;IAAa,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;EACIsG,sBAAsBA,CAACzB,WAAW,EAAE;IAChC,IAAI,IAAI,CAACxB,aAAa,CAACwB,WAAW,CAAC,IAC/B,CAAC,IAAI,CAACT,MAAM,CAAChF,gBAAgB,CAACyF,WAAW,CAAC,CAAC,EAAE;MAC7C,OAAQ,GAAEA,WAAY,IAAG,IAAI,CAACjC,aAAa,CAAC,CAAE,EAAC;IACnD;IACA,OAAOiC,WAAW;EACtB;EACA;AACJ;AACA;EACI0B,cAAcA,CAACpN,KAAK,EAAEqN,KAAK,EAAE;IACzB,IAAI,CAAC,IAAI,CAAChM,MAAM,CAACiM,YAAY,EAAE;MAC3B,IAAI,CAACjM,MAAM,CAACiM,YAAY,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACjM,MAAM,CAACiM,YAAY,CAACtN,KAAK,CAAC,GAAGqN,KAAK;EAC3C;EACAE,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA;IACA,IAAI,CAACjF,KAAK,CAACkF,KAAK,CAAC,CAAC;EACtB;EACAN,mBAAmBA,CAACtQ,IAAI,EAAE;IACtB,OAAOyB,IAAI,CAAC,IAAI,CAAC1B,cAAc,CAACC,IAAI,CAAC,CAAC;EAC1C;EACAkQ,oBAAoBA,CAAA,EAAG;IACnB,MAAMW,KAAK,GAAG,IAAI,CAAC5D,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,IAAIlL,QAAQ,CAAC8O,KAAK,CAAC,EAAE;MACjB,OAAO,IAAI,CAAC5D,iBAAiB,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAACrO,GAAG,CAAEkS,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC;EACpD;EACAd,qBAAqBA,CAAA,EAAG;IACpB,OAAO;MACH,GAAG,IAAI,CAACxL,MAAM;MACduM,UAAU,EAAE,IAAI,CAACnE,aAAa,CAAC,CAAC;MAChC5I,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCL,WAAW,EAAE,IAAI,CAACA;IACtB,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIQ,sBAAsBA,CAACpE,IAAI,EAAE;IACzB,OAAQ,IAAI,CAACyE,MAAM,CAACP,cAAc,CAACE,sBAAsB,IACrDpE,IAAI,KAAK,IAAI,CAAC2L,iBAAiB;EACvC;EACAgC,aAAaA,CAAC3N,IAAI,EAAEiF,WAAW,EAAE;IAC7B,IAAI,CAACmK,cAAc,CAACnK,WAAW,EAAEjF,IAAI,EAAE;MAAEuP,UAAU,EAAE;IAAM,CAAC,CAAC;IAC7D,IAAI,CAACxD,MAAM,CAACgB,IAAI,CAAC;MACbL,UAAU,EAAE,CAAC,CAAC,IAAI,CAACb,WAAW,CAACpK,IAAI;MACnCgF,IAAI,EAAE,wBAAwB;MAC9BmG,OAAO,EAAExC,eAAe,CAACpK,IAAI;IACjC,CAAC,CAAC;IACF,IAAI,CAAC6L,WAAW,CAAChG,OAAO,CAAEiL,CAAC,IAAK,IAAI,CAACpF,KAAK,CAACuF,MAAM,CAACH,CAAC,CAAC,CAAC;IACrD,IAAI,CAACjF,WAAW,CAAC+E,KAAK,CAAC,CAAC;EAC5B;EACA9C,aAAaA,CAAC9N,IAAI,EAAEkR,WAAW,EAAE;IAC7B;IACA;IACA,IAAItO,KAAK,CAACsO,WAAW,CAACC,aAAa,CAAC,EAAE;MAClCD,WAAW,CAACC,aAAa,GAAG,CAAC;MAC7B,IAAI,CAACD,WAAW,CAACE,aAAa,EAAE;QAC5BF,WAAW,CAACE,aAAa,GAAG,IAAI,CAAC7F,gBAAgB,CAACzC,YAAY,CAAC9I,IAAI,CAAC;MACxE;IACJ;IACA,MAAM+G,QAAQ,GAAG/G,IAAI,CAACU,KAAK,CAAC,GAAG,CAAC;IAChC,MAAM2Q,SAAS,GAAGH,WAAW,CAACE,aAAa;IAC3C,MAAME,QAAQ,GAAGD,SAAS,CAACH,WAAW,CAACC,aAAa,CAAC;IACrD,IAAI,CAACtF,WAAW,CAAC0F,GAAG,CAACvR,IAAI,CAAC;IAC1B;IACA,IAAI,IAAI,CAAC0L,KAAK,CAAC8F,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC1B,IAAI,CAAC3D,aAAa,CAAC2D,QAAQ,EAAE,IAAI,CAACvR,cAAc,CAACuR,QAAQ,CAAC,CAAC;MAC3D,OAAOhS,KAAK;IAChB;IACA,MAAMmS,cAAc,GAAGH,QAAQ,KAAKvK,QAAQ,CAACA,QAAQ,CAAC7F,MAAM,GAAG,CAAC,CAAC;IACjE,IAAI,CAACoQ,QAAQ,IAAIG,cAAc,EAAE;MAC7B,IAAIxJ,GAAG,GAAI,2DAA0D;MACrE,IAAIlB,QAAQ,CAAC7F,MAAM,GAAG,CAAC,EAAE;QACrB+G,GAAG,IAAK,sCAAqC;MACjD;MACA,MAAM,IAAIP,KAAK,CAACO,GAAG,CAAC;IACxB;IACA,IAAI8F,WAAW,GAAGuD,QAAQ;IAC1B;IACA,IAAIvK,QAAQ,CAAC7F,MAAM,GAAG,CAAC,EAAE;MACrB;MACA;MACA6F,QAAQ,CAACA,QAAQ,CAAC7F,MAAM,GAAG,CAAC,CAAC,GAAGoQ,QAAQ;MACxCvD,WAAW,GAAGhH,QAAQ,CAACqC,IAAI,CAAC,GAAG,CAAC;IACpC;IACA8H,WAAW,CAACC,aAAa,EAAE;IAC3B,IAAI,CAACpF,MAAM,CAACgB,IAAI,CAAC;MACbtG,IAAI,EAAE,wBAAwB;MAC9BmG,OAAO,EAAExC,eAAe,CAACpK,IAAI;IACjC,CAAC,CAAC;IACF,OAAO,IAAI,CAACkN,IAAI,CAACa,WAAW,EAAEmD,WAAW,CAAC;EAC9C;EACAxB,cAAcA,CAACtM,KAAK,EAAE;IAClB,MAAM;MAAEsN,YAAY,GAAG,CAAC;IAAE,CAAC,GAAG,IAAI,CAACjM,MAAM;IACzC,OAAOiM,YAAY,CAACtN,KAAK,CAAC,IAAIhB,WAAW,CAACgB,KAAK,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACI4K,mBAAmBA,CAAChO,IAAI,EAAE;IACtB,IAAI+N,WAAW,GAAG/N,IAAI;IACtB,IAAIoD,KAAK;IACT,IAAI,IAAI,CAACkK,aAAa,CAACtN,IAAI,CAAC,EAAE;MAC1B;MACA,MAAM0R,aAAa,GAAGrI,gBAAgB,CAACrJ,IAAI,CAAC;MAC5C;MACA,MAAM2R,OAAO,GAAG,IAAI,CAACtD,MAAM,CAACqD,aAAa,CAAC;MAC1C;MACA3D,WAAW,GAAG4D,OAAO,GAAGD,aAAa,GAAG,IAAI,CAAC7E,aAAa,CAAC,CAAC;MAC5D;MACAzJ,KAAK,GAAG,IAAI,CAACsM,cAAc,CAACiC,OAAO,GAAGzI,gBAAgB,CAAClJ,IAAI,CAAC,GAAGA,IAAI,CAAC;IACxE;IACA,OAAO;MAAEoD,KAAK;MAAE2K;IAAY,CAAC;EACjC;EACAQ,cAAcA,CAACtJ,WAAW,EAAEC,GAAG,EAAE;IAC7B,MAAMU,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMgM,MAAM,GAAI,GAAE1M,GAAI,GAAE;IACxB,KAAK,MAAM2M,UAAU,IAAI5M,WAAW,EAAE;MAClC,IAAI4M,UAAU,CAACC,UAAU,CAACF,MAAM,CAAC,EAAE;QAC/BhM,MAAM,CAACiM,UAAU,CAACvP,OAAO,CAACsP,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG3M,WAAW,CAAC4M,UAAU,CAAC;MACpE;IACJ;IACA,OAAOjM,MAAM;EACjB;EACA8I,UAAUA,CAACxJ,GAAG,EAAE;IACZ,OAAOA,GAAG,YAAYuG,GAAG,GAAGvG,GAAG,CAAC6M,OAAO,CAAC,CAAC,GAAGzR,MAAM,CAACyR,OAAO,CAAC7M,GAAG,CAAC;EACnE;EACA,OAAOe,IAAI,YAAA+L,yBAAA7L,CAAA;IAAA,YAAAA,CAAA,IAAwFiF,gBAAgB,EA/tB1B/N,EAAE,CAAA2L,QAAA,CA+tB0C9I,gBAAgB,MA/tB5D7C,EAAE,CAAA2L,QAAA,CA+tBuFtE,oBAAoB,GA/tB7GrH,EAAE,CAAA2L,QAAA,CA+tBwHlB,yBAAyB,GA/tBnJzK,EAAE,CAAA2L,QAAA,CA+tB8JX,qBAAqB,GA/tBrLhL,EAAE,CAAA2L,QAAA,CA+tBgMxF,gBAAgB,GA/tBlNnG,EAAE,CAAA2L,QAAA,CA+tB6NL,2BAA2B;EAAA;EACnV,OAAOvC,KAAK,kBAhuB6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EAguBY8E,gBAAgB;IAAA1H,OAAA,EAAhB0H,gBAAgB,CAAAnF,IAAA;IAAAxC,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAA8C,SAAA,oBAAAA,SAAA,KAluB6FlJ,EAAE,CAAAmJ,iBAAA,CAkuBJ4E,gBAAgB,EAAc,CAAC;IAC9G3E,IAAI,EAAEjJ,UAAU;IAChBwJ,IAAI,EAAE,CAAC;MAAEvD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEgD,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAC/CxC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAAC9G,gBAAgB;IAC3B,CAAC;EAAE,CAAC,EAAE;IAAEuG,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACtC,oBAAoB;IAC/B,CAAC;EAAE,CAAC,EAAE;IAAE+B,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACc,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAErB,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACqB,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE5B,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACxD,gBAAgB;IAC3B,CAAC;EAAE,CAAC,EAAE;IAAEiD,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAAC2B,2BAA2B;IACtC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMsJ,wBAAwB,CAAC;EAC3BC,IAAI;EACJ,OAAOjM,IAAI,YAAAkM,iCAAAhM,CAAA;IAAA,YAAAA,CAAA,IAAwF8L,wBAAwB;EAAA;EAC3H,OAAOG,IAAI,kBA9vB8E/U,EAAE,CAAAgV,iBAAA;IAAA5L,IAAA,EA8vBJwL,wBAAwB;IAAAK,SAAA;IAAAC,MAAA;MAAAL,IAAA;IAAA;IAAAM,UAAA;IAAAC,QAAA,GA9vBtBpV,EAAE,CAAAqV,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3V,EAAE,CAAA6V,SAAA,YA+vB5B,CAAC;MAAA;MAAA,IAAAF,EAAA;QA/vByB3V,EAAE,CAAA8V,UAAA,cAAAF,GAAA,CAAAf,IAAA,EAAF7U,EAAE,CAAA+V,cA+vBnC,CAAC;MAAA;IAAA;IAAAC,aAAA;EAAA;AAE7D;AACA;EAAA,QAAA9M,SAAA,oBAAAA,SAAA,KAlwB6FlJ,EAAE,CAAAmJ,iBAAA,CAkwBJyL,wBAAwB,EAAc,CAAC;IACtHxL,IAAI,EAAE7I,SAAS;IACfoJ,IAAI,EAAE,CAAC;MACC8L,QAAQ,EAAG;AAC/B;AACA,GAAG;MACiBN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEN,IAAI,EAAE,CAAC;MACrBzL,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyV,eAAe,CAAC;EAClBC,IAAI;EACJC,GAAG;EACH1T,WAAWA,CAACyT,IAAI,EAAEC,GAAG,EAAE;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACF,IAAI,YAAYzV,WAAW,EAAE;MAClC,IAAI,CAAC0V,GAAG,CAACE,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAC;IAC1C,CAAC,MACI,IAAIxR,QAAQ,CAAC,IAAI,CAACwR,IAAI,CAAC,EAAE;MAC1B,MAAMI,YAAY,GAAG,IAAI,CAACH,GAAG,CAACI,eAAe,CAAC3B,wBAAwB,CAAC;MACvE0B,YAAY,CAACE,QAAQ,CAAC3B,IAAI,GAAG,IAAI,CAACqB,IAAI;MACtCI,YAAY,CAACG,QAAQ,CAACC,aAAa,CAAC,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACP,GAAG,CAACI,eAAe,CAAC,IAAI,CAACL,IAAI,CAAC;IACvC;EACJ;EACAS,UAAUA,CAAA,EAAG;IACT,IAAI,CAACR,GAAG,CAAC5C,KAAK,CAAC,CAAC;EACpB;AACJ;AAEA,MAAMqD,cAAc,GAAG,IAAI3W,cAAc,CAAC,gBAAgB,CAAC;AAE3D,MAAM4W,0BAA0B,GAAG,IAAI5W,cAAc,CAAC,4BAA4B,CAAC;AAEnF,MAAM6W,eAAe,GAAG,IAAI7W,cAAc,CAAC,iBAAiB,CAAC;AAE7D,MAAM8W,YAAY,CAAC;EACfC,WAAW,GAAG,KAAK;EACnB;EACAC,OAAOA,CAAC;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAO,CAAC,EAAE;IAClC,IAAIzU,IAAI,GAAGyU,MAAM;IACjB;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACJ,WAAW,EAAE;MAClBrU,IAAI,GAAGyU,MAAM;MACb,OAAOzU,IAAI;IACf;IACA,IAAIwU,QAAQ,EAAE;MACV,MAAM,GAAGE,SAAS,CAAC,GAAGpL,YAAY,CAACkL,QAAQ,EAAE,QAAQ,CAAC;MACtDxU,IAAI,GAAG0U,SAAS;IACpB;IACA,IAAIH,MAAM,EAAE;MACR,MAAM,GAAGG,SAAS,CAAC,GAAGpL,YAAY,CAACiL,MAAM,EAAE,QAAQ,CAAC;MACpDvU,IAAI,GAAG0U,SAAS;IACpB;IACA,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,OAAOrU,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2U,uBAAuBA,CAAC3U,IAAI,EAAE;IAC1B,MAAMoD,KAAK,GAAG8F,gBAAgB,CAAClJ,IAAI,CAAC;IACpC,OAAOoD,KAAK,GAAGiG,gBAAgB,CAACrJ,IAAI,CAAC,GAAGA,IAAI;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4U,eAAeA,CAAC5U,IAAI,EAAEoD,KAAK,EAAE;IACzB,OAAOA,KAAK,GAAI,GAAEA,KAAM,IAAGpD,IAAK,EAAC,GAAGA,IAAI;EAC5C;AACJ;AAEA,MAAM6U,aAAa,CAAC;EAChBlL,OAAO;EACP7J,WAAWA,CAAC6J,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;EACA2K,OAAOA,CAACtP,MAAM,EAAE;IACZ,MAAM;MAAEuP,MAAM;MAAEC;IAAS,CAAC,GAAGxP,MAAM;IACnC,IAAIuP,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,IAAIC,QAAQ,EAAE;MACV,IAAIrR,aAAa,CAACqR,QAAQ,CAAC,EAAE;QACzB,MAAM;UAAEpR,KAAK;UAAEqN,KAAK,GAAGrO,WAAW,CAACgB,KAAK;QAAE,CAAC,GAAGoR,QAAQ;QACtD,IAAI,CAAC7K,OAAO,CAAC6G,cAAc,CAACpN,KAAK,EAAEqN,KAAK,CAAC;QACzC,OAAOrN,KAAK;MAChB;MACA,OAAOoR,QAAQ;IACnB;IACA,OAAO3R,SAAS;EACpB;AACJ;AAEA,MAAMiS,kBAAkB,CAAC;EACrBC,UAAU,GAAGxX,MAAM,CAACQ,UAAU,CAAC;EAC/B4L,OAAO,GAAGpM,MAAM,CAAC6N,gBAAgB,CAAC;EAClC4J,GAAG,GAAGzX,MAAM,CAACO,WAAW,EAAE;IACtB8G,QAAQ,EAAE;EACd,CAAC,CAAC;EACFqQ,YAAY,GAAG1X,MAAM,CAAC0W,cAAc,EAAE;IAAErP,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzDuF,aAAa,GAAG5M,MAAM,CAAC4W,eAAe,EAAE;IAAEvP,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC3DsQ,kBAAkB,GAAG3X,MAAM,CAAC2W,0BAA0B,EAAE;IACpDtP,QAAQ,EAAE;EACd,CAAC,CAAC;EACFuQ,GAAG,GAAG5X,MAAM,CAACS,iBAAiB,CAAC;EAC/BoX,IAAI,GAAG7X,MAAM,CAACU,UAAU,CAAC;EACzBuV,GAAG,GAAGjW,MAAM,CAACW,gBAAgB,CAAC;EAC9BmX,QAAQ,GAAG9X,MAAM,CAACY,SAAS,CAAC;EAC5BoV,IAAI;EACJ+B,IAAI,GAAG,IAAI7J,GAAG,CAAC,CAAC;EAChBvG,GAAG;EACHF,MAAM,GAAG,CAAC,CAAC;EACXuQ,WAAW;EACX;EACAC,UAAU;EACV5D,MAAM;EACN6D,UAAU;EACVC,SAAS;EACTxG,WAAW;EACXyG,gBAAgB;EAChB;EACAtB,WAAW,GAAG,KAAK;EACnBhU,IAAI;EACJuV,YAAY,GAAG,IAAIxB,YAAY,CAAC,CAAC;EACjCyB,aAAa,GAAG,IAAIhB,aAAa,CAAC,IAAI,CAAClL,OAAO,CAAC;EAC/CmM,QAAQ,GAAG,IAAI,CAACd,GAAG,KAAK,IAAI,GAAG,WAAW,GAAG,YAAY;EACzD,OAAOe,sBAAsBA,CAACC,GAAG,EAAE/C,GAAG,EAAE;IACpC,OAAO,IAAI;EACf;EACAgD,QAAQA,CAAA,EAAG;IACP,MAAMnM,kBAAkB,GAAGJ,yBAAyB,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACsL,YAAY,IAAI,IAAI,CAACQ,UAAU,CAAC;IACxG,IAAI,CAAC9L,OAAO,CAAC6B,YAAY,CACpBP,IAAI,CAAC7L,SAAS,CAAE4R,UAAU,IAAK;MAChC,MAAMhR,IAAI,GAAG,IAAI,CAAC4V,YAAY,CAACtB,OAAO,CAAC;QACnCC,MAAM,EAAE,IAAI,CAACkB,UAAU;QACvBjB,QAAQ,EAAE,IAAI,CAACS,YAAY;QAC3BR,MAAM,EAAEzD;MACZ,CAAC,CAAC;MACF,OAAO1P,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC4I,aAAa,CAAC,GAClCpL,QAAQ,CAAC,IAAI,CAACoL,aAAa,CAACvL,GAAG,CAAEuL,aAAa,IAAK,IAAI,CAAC+L,YAAY,CAAClW,IAAI,EAAEmK,aAAa,CAAC,CAAC,CAAC,GAC3F,IAAI,CAAC+L,YAAY,CAAClW,IAAI,EAAE,IAAI,CAACmK,aAAa,CAAC;IACrD,CAAC,CAAC,EAAEN,mBAAmB,CAACC,kBAAkB,CAAC,EAAEvK,kBAAkB,CAAC,IAAI,CAACwV,UAAU,CAAC,CAAC,CAC5EtI,SAAS,CAAC,MAAM;MACjB,IAAI,CAACyC,WAAW,GAAG,IAAI,CAAC0G,YAAY,CAACjB,uBAAuB,CAAC,IAAI,CAACtU,IAAI,CAAC;MACvE,IAAI,CAACyV,QAAQ,KAAK,WAAW,GACvB,IAAI,CAACK,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAClH,WAAW,EAAE,IAAI,CAAC0C,MAAM,IAAI,IAAI,CAAC4D,UAAU,CAAC;MAC/E,IAAI,CAACL,GAAG,CAACkB,YAAY,CAAC,CAAC;MACvB,IAAI,CAAChC,WAAW,GAAG,IAAI;IAC3B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACA,WAAW,EAAE;MACnB,MAAMiC,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACnD,IAAID,cAAc,EAAE;QAChB,IAAI,CAACX,gBAAgB,GAAG,IAAIrC,eAAe,CAACgD,cAAc,EAAE,IAAI,CAAC9C,GAAG,CAAC;QACrE,IAAI,CAACmC,gBAAgB,CAAClC,UAAU,CAAC,CAAC;MACtC;IACJ;EACJ;EACA+C,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,IAAI,CAACX,QAAQ,KAAK,WAAW,EAAE;MAC/B,MAAMY,OAAO,GAAGpW,MAAM,CAACsB,IAAI,CAAC6U,OAAO,CAAC,CAACE,IAAI,CAAE3Q,CAAC,IAAK,CAACyQ,OAAO,CAACzQ,CAAC,CAAC,CAAC4Q,WAAW,CAAC;MACzEF,OAAO,IAAI,IAAI,CAACP,iBAAiB,CAAC,CAAC;IACvC;EACJ;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACU,YAAY,CAAC,CAAC;IACnB,IAAI,CAACxB,QAAQ,CAACyB,WAAW,CAAC,IAAI,CAAC1B,IAAI,CAAC2B,aAAa,EAAE,WAAW,EAAE,IAAI,CAACpN,OAAO,CAACuB,SAAS,CAAC,IAAI,CAAChG,GAAG,EAAE,IAAI,CAACF,MAAM,EAAE,IAAI,CAACkK,WAAW,CAAC,CAAC;EACpI;EACAkH,kBAAkBA,CAACpW,IAAI,EAAE4R,MAAM,EAAE;IAC7B,IAAI,CAAC0D,IAAI,CAAC1E,KAAK,CAAC,CAAC;IACjB,MAAMoG,WAAW,GAAG,IAAI,CAACC,cAAc,CAACjX,IAAI,EAAE4R,MAAM,CAAC;IACrD,IAAI,IAAI,CAAC2B,IAAI,EAAE;MACX;MACA,IAAI,CAACA,IAAI,CAAC2D,OAAO,CAAC,WAAW,CAAC,GAAGF,WAAW;MAC5C,IAAI,CAACzD,IAAI,CAAC2D,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAChI,WAAW;IACvD,CAAC,MACI;MACD,IAAI,CAAC2H,YAAY,CAAC,CAAC;MACnB,IAAI,CAACtD,IAAI,GAAG,IAAI,CAACC,GAAG,CAACE,kBAAkB,CAAC,IAAI,CAACsB,GAAG,EAAE;QAC9CmC,SAAS,EAAEH,WAAW;QACtB9H,WAAW,EAAE,IAAI,CAACA;MACtB,CAAC,CAAC;IACN;EACJ;EACA+H,cAAcA,CAACjX,IAAI,EAAE4R,MAAM,EAAE;IACzB,OAAO,CAAC1M,GAAG,EAAEF,MAAM,KAAK;MACpB,MAAMoS,UAAU,GAAGxF,MAAM,GAAI,GAAEA,MAAO,IAAG1M,GAAI,EAAC,GAAGA,GAAG;MACpD,MAAMmS,OAAO,GAAGrS,MAAM,GACf,GAAEoS,UAAW,GAAElL,IAAI,CAACE,SAAS,CAACpH,MAAM,CAAE,EAAC,GACxCoS,UAAU;MAChB,IAAI,CAAC,IAAI,CAAC9B,IAAI,CAAC9D,GAAG,CAAC6F,OAAO,CAAC,EAAE;QACzB,IAAI,CAAC/B,IAAI,CAAC1H,GAAG,CAACyJ,OAAO,EAAE,IAAI,CAAC1N,OAAO,CAACuB,SAAS,CAACkM,UAAU,EAAEpS,MAAM,EAAEhF,IAAI,CAAC,CAAC;MAC5E;MACA,OAAO,IAAI,CAACsV,IAAI,CAACrV,GAAG,CAACoX,OAAO,CAAC;IACjC,CAAC;EACL;EACAd,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACb,SAAS,IAAI,IAAI,CAACR,kBAAkB;EACpD;EACAvE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2E,IAAI,CAAC1E,KAAK,CAAC,CAAC;EACrB;EACAiG,YAAYA,CAAA,EAAG;IACX,IAAI,CAAClB,gBAAgB,EAAE3B,UAAU,CAAC,CAAC;EACvC;EACAkC,YAAYA,CAAClW,IAAI,EAAEmK,aAAa,EAAE;IAC9B,MAAMmN,aAAa,GAAG,IAAI,CAACzB,aAAa,CAACvB,OAAO,CAAC;MAC7CC,MAAM,EAAE,IAAI,CAACgB,WAAW;MACxBf,QAAQ,EAAErK;IACd,CAAC,CAAC;IACF,IAAI,CAAC9J,IAAI,GAAG,IAAI,CAACuV,YAAY,CAAChB,eAAe,CAAC5U,IAAI,EAAEsX,aAAa,CAAC;IAClE,MAAMrN,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEmN,aAAa,CAAC;IACtE,OAAO,IAAI,CAAC3N,OAAO,CAACyG,iBAAiB,CAAC,IAAI,CAAC/P,IAAI,EAAE4J,YAAY,CAAC;EAClE;EACA,OAAOhE,IAAI,YAAAsR,2BAAApR,CAAA;IAAA,YAAAA,CAAA,IAAwF2O,kBAAkB;EAAA;EACrH,OAAO0C,IAAI,kBAr/B8Ena,EAAE,CAAAoa,iBAAA;IAAAhR,IAAA,EAq/BJqO,kBAAkB;IAAAxC,SAAA;IAAAC,MAAA;MAAArN,GAAA;MAAAF,MAAA;MAAAuQ,WAAA;MAAAC,UAAA;MAAA5D,MAAA;MAAA6D,UAAA;MAAAC,SAAA;IAAA;IAAAlD,UAAA;IAAAC,QAAA,GAr/BhBpV,EAAE,CAAAqa,oBAAA;EAAA;AAs/B/F;AACA;EAAA,QAAAnR,SAAA,oBAAAA,SAAA,KAv/B6FlJ,EAAE,CAAAmJ,iBAAA,CAu/BJsO,kBAAkB,EAAc,CAAC;IAChHrO,IAAI,EAAErI,SAAS;IACf4I,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE,aAAa;MACvBnF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtN,GAAG,EAAE,CAAC;MACpBuB,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEhC,MAAM,EAAE,CAAC;MACTyB,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEuO,WAAW,EAAE,CAAC;MACd9O,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEwO,UAAU,EAAE,CAAC;MACb/O,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE4K,MAAM,EAAE,CAAC;MACTnL,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEyO,UAAU,EAAE,CAAC;MACbhP,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE0O,SAAS,EAAE,CAAC;MACZjP,IAAI,EAAE5I,KAAK;MACXmJ,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4Q,aAAa,CAAC;EAChBjO,OAAO;EACPQ,aAAa;EACb8K,YAAY;EACZE,GAAG;EACH0C,YAAY,GAAG,IAAI;EACnBC,SAAS,GAAG,EAAE;EACdC,OAAO;EACP1X,IAAI;EACJuV,YAAY,GAAG,IAAIxB,YAAY,CAAC,CAAC;EACjCyB,aAAa;EACb/V,WAAWA,CAAC6J,OAAO,EAAEQ,aAAa,EAAE8K,YAAY,EAAEE,GAAG,EAAE;IACnD,IAAI,CAACxL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC8K,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACU,aAAa,GAAG,IAAIhB,aAAa,CAAC,IAAI,CAAClL,OAAO,CAAC;EACxD;EACA;EACA;EACAqO,SAASA,CAAC9S,GAAG,EAAEF,MAAM,EAAEyQ,UAAU,EAAE;IAC/B,IAAI,CAACvQ,GAAG,EAAE;MACN,OAAOA,GAAG;IACd;IACA,MAAM+S,OAAO,GAAGjT,MAAM,GAAI,GAAEE,GAAI,GAAEgH,IAAI,CAACE,SAAS,CAACpH,MAAM,CAAE,EAAC,GAAGE,GAAG;IAChE,IAAI+S,OAAO,KAAK,IAAI,CAACF,OAAO,EAAE;MAC1B,OAAO,IAAI,CAACD,SAAS;IACzB;IACA,IAAI,CAACC,OAAO,GAAGE,OAAO;IACtB,IAAI,CAACJ,YAAY,EAAEK,WAAW,CAAC,CAAC;IAChC,MAAMpO,kBAAkB,GAAGJ,yBAAyB,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACsL,YAAY,IAAIQ,UAAU,CAAC;IACnG,IAAI,CAACoC,YAAY,GAAG,IAAI,CAAClO,OAAO,CAAC6B,YAAY,CACxCP,IAAI,CAAC7L,SAAS,CAAE4R,UAAU,IAAK;MAChC,MAAMhR,IAAI,GAAG,IAAI,CAAC4V,YAAY,CAACtB,OAAO,CAAC;QACnCC,MAAM,EAAEkB,UAAU;QAClBjB,QAAQ,EAAE,IAAI,CAACS,YAAY;QAC3BR,MAAM,EAAEzD;MACZ,CAAC,CAAC;MACF,OAAO1P,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC4I,aAAa,CAAC,GAClCpL,QAAQ,CAAC,IAAI,CAACoL,aAAa,CAACvL,GAAG,CAAEuL,aAAa,IAAK,IAAI,CAAC+L,YAAY,CAAClW,IAAI,EAAEmK,aAAa,CAAC,CAAC,CAAC,GAC3F,IAAI,CAAC+L,YAAY,CAAClW,IAAI,EAAE,IAAI,CAACmK,aAAa,CAAC;IACrD,CAAC,CAAC,EAAEN,mBAAmB,CAACC,kBAAkB,CAAC,CAAC,CACvC2C,SAAS,CAAC,MAAM,IAAI,CAAC0L,WAAW,CAACjT,GAAG,EAAEF,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC8S,SAAS;EACzB;EACAnH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkH,YAAY,EAAEK,WAAW,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAACL,YAAY,GAAG,IAAI;EAC5B;EACAM,WAAWA,CAACjT,GAAG,EAAEF,MAAM,EAAE;IACrB,MAAMhF,IAAI,GAAG,IAAI,CAAC4V,YAAY,CAACjB,uBAAuB,CAAC,IAAI,CAACtU,IAAI,CAAC;IACjE,IAAI,CAACyX,SAAS,GAAG,IAAI,CAACnO,OAAO,CAACuB,SAAS,CAAChG,GAAG,EAAEF,MAAM,EAAEhF,IAAI,CAAC;IAC1D,IAAI,CAACmV,GAAG,CAACkB,YAAY,CAAC,CAAC;EAC3B;EACAH,YAAYA,CAAClW,IAAI,EAAEmK,aAAa,EAAE;IAC9B,MAAMmN,aAAa,GAAG,IAAI,CAACzB,aAAa,CAACvB,OAAO,CAAC;MAC7CC,MAAM,EAAE1R,SAAS;MACjB2R,QAAQ,EAAErK;IACd,CAAC,CAAC;IACF,IAAI,CAAC9J,IAAI,GAAG,IAAI,CAACuV,YAAY,CAAChB,eAAe,CAAC5U,IAAI,EAAEsX,aAAa,CAAC;IAClE,MAAMrN,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEmN,aAAa,CAAC;IACtE,OAAO,IAAI,CAAC3N,OAAO,CAACyG,iBAAiB,CAAC,IAAI,CAAC/P,IAAI,EAAE4J,YAAY,CAAC;EAClE;EACA,OAAOhE,IAAI,YAAAmS,sBAAAjS,CAAA;IAAA,YAAAA,CAAA,IAAwFyR,aAAa,EArlCvBva,EAAE,CAAAgb,iBAAA,CAqlCuCjN,gBAAgB,OArlCzD/N,EAAE,CAAAgb,iBAAA,CAqlCoElE,eAAe,OArlCrF9W,EAAE,CAAAgb,iBAAA,CAqlCgHpE,cAAc,OArlChI5W,EAAE,CAAAgb,iBAAA,CAqlC2Jhb,EAAE,CAACW,iBAAiB;EAAA;EAC1Q,OAAOsa,KAAK,kBAtlC6Ejb,EAAE,CAAAkb,YAAA;IAAAC,IAAA;IAAA/R,IAAA,EAslCMmR,aAAa;IAAAa,IAAA;IAAAjG,UAAA;EAAA;AAClH;AACA;EAAA,QAAAjM,SAAA,oBAAAA,SAAA,KAxlC6FlJ,EAAE,CAAAmJ,iBAAA,CAwlCJoR,aAAa,EAAc,CAAC;IAC3GnR,IAAI,EAAEpI,IAAI;IACV2I,IAAI,EAAE,CAAC;MACCwR,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,KAAK;MACXjG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/L,IAAI,EAAE2E;EAAiB,CAAC,EAAE;IAAE3E,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAC3ExC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACmN,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE1N,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAE9I;IACV,CAAC,EAAE;MACC8I,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACiN,cAAc;IACzB,CAAC;EAAE,CAAC,EAAE;IAAExN,IAAI,EAAEpJ,EAAE,CAACW;EAAkB,CAAC,CAAC;AAAA;AAErD,MAAM0a,IAAI,GAAG,CAAC5D,kBAAkB,EAAE8C,aAAa,CAAC;AAChD,MAAMe,eAAe,CAAC;EAClB,OAAO1S,IAAI,YAAA2S,wBAAAzS,CAAA;IAAA,YAAAA,CAAA,IAAwFwS,eAAe;EAAA;EAClH,OAAOE,IAAI,kBA9mC8Exb,EAAE,CAAAyb,gBAAA;IAAArS,IAAA,EA8mCSkS;EAAe;EACnH,OAAOI,IAAI,kBA/mC8E1b,EAAE,CAAA2b,gBAAA;AAgnC/F;AACA;EAAA,QAAAzS,SAAA,oBAAAA,SAAA,KAjnC6FlJ,EAAE,CAAAmJ,iBAAA,CAinCJmS,eAAe,EAAc,CAAC;IAC7GlS,IAAI,EAAEnI,QAAQ;IACd0I,IAAI,EAAE,CAAC;MACCiS,OAAO,EAAEP,IAAI;MACbQ,OAAO,EAAER;IACb,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASS,gBAAgBA,CAAC5O,OAAO,EAAE;EAC/B,MAAM6O,SAAS,GAAG,CACdC,0BAA0B,CAAC1U,iBAAiB,CAAC,EAC7C2U,8BAA8B,CAACvR,qBAAqB,CAAC,EACrDwR,2BAA2B,CAACjR,kBAAkB,CAAC,EAC/CkR,gCAAgC,CAAC5Q,uBAAuB,CAAC,CAC5D;EACD,IAAI2B,OAAO,CAAC9F,MAAM,EAAE;IAChB2U,SAAS,CAAClS,IAAI,CAACuS,sBAAsB,CAAClP,OAAO,CAAC9F,MAAM,CAAC,CAAC;EAC1D;EACA,IAAI8F,OAAO,CAACjH,MAAM,EAAE;IAChB8V,SAAS,CAAClS,IAAI,CAACwS,sBAAsB,CAACnP,OAAO,CAACjH,MAAM,CAAC,CAAC;EAC1D;EACA,OAAO8V,SAAS;AACpB;AACA,SAASK,sBAAsBA,CAAChV,MAAM,EAAE;EACpC,OAAOlG,wBAAwB,CAAC,CAC5B;IACIob,OAAO,EAAEnW,gBAAgB;IACzBoW,QAAQ,EAAEpV,eAAe,CAACC,MAAM;EACpC,CAAC,CACJ,CAAC;AACN;AACA,SAASiV,sBAAsBA,CAACpW,MAAM,EAAE;EACpC,OAAO/E,wBAAwB,CAAC,CAC5B;IAAEob,OAAO,EAAEzZ,gBAAgB;IAAE2Z,QAAQ,EAAEvW;EAAO,CAAC,CAClD,CAAC;AACN;AACA,SAASwW,qBAAqBA,CAAC,GAAGC,MAAM,EAAE;EACtC,OAAOA,MAAM,CAACnb,GAAG,CAAEwE,KAAK,KAAM;IAC1BuW,OAAO,EAAExF,eAAe;IACxByF,QAAQ,EAAExW,KAAK;IACf4W,KAAK,EAAE;EACX,CAAC,CAAC,CAAC;AACP;AACA,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAO;IACHP,OAAO,EAAEzF,0BAA0B;IACnC0F,QAAQ,EAAEM;EACd,CAAC;AACL;AACA,SAASb,0BAA0BA,CAACc,UAAU,EAAE;EAC5C,OAAO5b,wBAAwB,CAAC,CAC5B;IACIob,OAAO,EAAEjV,oBAAoB;IAC7BmV,QAAQ,EAAEM,UAAU;IACpBC,IAAI,EAAE,CAAC5W,gBAAgB;EAC3B,CAAC,CACJ,CAAC;AACN;AACA,SAASgW,gCAAgCA,CAAC1D,QAAQ,EAAE;EAChD,OAAOvX,wBAAwB,CAAC,CAC5B;IACIob,OAAO,EAAEhR,2BAA2B;IACpCkR,QAAQ,EAAE/D,QAAQ;IAClBsE,IAAI,EAAE,CAAC5W,gBAAgB;EAC3B,CAAC,CACJ,CAAC;AACN;AACA,SAAS8V,8BAA8BA,CAACe,OAAO,EAAE;EAC7C,OAAO9b,wBAAwB,CAAC,CAC5B;IACIob,OAAO,EAAE7R,yBAAyB;IAClC+R,QAAQ,EAAEQ;EACd,CAAC,CACJ,CAAC;AACN;AACA,SAASd,2BAA2BA,CAACjO,WAAW,EAAE;EAC9C,OAAO/M,wBAAwB,CAAC,CAC5B;IACIob,OAAO,EAAEtR,qBAAqB;IAC9BwR,QAAQ,EAAEvO;EACd,CAAC,CACJ,CAAC;AACN;AACA,SAASgP,oBAAoBA,CAACta,IAAI,EAAE;EAChC,OAAO;IACH2Z,OAAO,EAAE1F,cAAc;IACvB2F,QAAQ,EAAE5Z;EACd,CAAC;AACL;AAEA,MAAMua,oBAAoB,GAAG,IAAIjd,cAAc,CAAC,oDAAoD,CAAC;AACrG,MAAMkd,sBAAsB,GAAG,IAAIld,cAAc,CAAC,0CAA0C,CAAC;AAC7F,MAAMmd,aAAa,CAAC;EAChBzN,KAAK;EACLlN,WAAWA,CAACkN,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAjN,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOvB,EAAE,CAAC,IAAI,CAACuO,KAAK,CAAChN,IAAI,CAAC,CAAC;EAC/B;EACA,OAAOiG,IAAI,YAAAyU,sBAAAvU,CAAA;IAAA,YAAAA,CAAA,IAAwFsU,aAAa,EArtCvBpd,EAAE,CAAA2L,QAAA,CAqtCuCuR,oBAAoB;EAAA;EACtJ,OAAOnU,KAAK,kBAttC6E/I,EAAE,CAAAgJ,kBAAA;IAAAC,KAAA,EAstCYmU,aAAa;IAAA/W,OAAA,EAAb+W,aAAa,CAAAxU;EAAA;AACxH;AACA;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAxtC6FlJ,EAAE,CAAAmJ,iBAAA,CAwtCJiU,aAAa,EAAc,CAAC;IAC3GhU,IAAI,EAAEjJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEiJ,IAAI,EAAE5D,SAAS;IAAEoG,UAAU,EAAE,CAAC;MAC/CxC,IAAI,EAAE/I,MAAM;MACZsJ,IAAI,EAAE,CAACuT,oBAAoB;IAC/B,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAASI,oBAAoBA,CAAChR,OAAO,EAAEqD,KAAK,GAAG,CAAC,CAAC,EAAEzC,OAAO,EAAE;EACxD,MAAMqQ,eAAe,GAAGA,CAAA,KAAMrQ,OAAO,CAACsQ,YAAY,GAC5CC,OAAO,CAACC,GAAG,CAACza,MAAM,CAACsB,IAAI,CAACoL,KAAK,CAAC,CAACpO,GAAG,CAAEoB,IAAI,IAAK2J,OAAO,CAACuD,IAAI,CAAClN,IAAI,CAAC,CAACgb,SAAS,CAAC,CAAC,CAAC,CAAC,GAC7EF,OAAO,CAACxG,OAAO,CAAC,CAAC;EACvB,OAAOsG,eAAe;AAC1B;AACA,MAAMK,sBAAsB,CAAC;EACzB,OAAOC,OAAOA,CAAC3Q,OAAO,EAAE;IACpB,OAAO;MACH4Q,QAAQ,EAAEF,sBAAsB;MAChC7B,SAAS,EAAE,CACPD,gBAAgB,CAAC;QACb7V,MAAM,EAAEmX,aAAa;QACrBhW,MAAM,EAAE;UACJX,QAAQ,EAAE,IAAI;UACdI,cAAc,EAAE;YAAEC,aAAa,EAAE;UAAM,CAAC;UACxC,GAAGoG,OAAO,CAAC/F;QACf;MACJ,CAAC,CAAC,EACF;QACImV,OAAO,EAAEY,oBAAoB;QAC7BX,QAAQ,EAAErP,OAAO,CAACyC;MACtB,CAAC,EACD;QACI2M,OAAO,EAAEa,sBAAsB;QAC/BZ,QAAQ,EAAErP;MACd,CAAC,EACD;QACIoP,OAAO,EAAEnb,eAAe;QACxB4c,UAAU,EAAET,oBAAoB;QAChCP,IAAI,EAAE,CACFhP,gBAAgB,EAChBmP,oBAAoB,EACpBC,sBAAsB,CACzB;QACDR,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACL;EACA,OAAO/T,IAAI,YAAAoV,+BAAAlV,CAAA;IAAA,YAAAA,CAAA,IAAwF8U,sBAAsB;EAAA;EACzH,OAAOpC,IAAI,kBAvwC8Exb,EAAE,CAAAyb,gBAAA;IAAArS,IAAA,EAuwCSwU;EAAsB;EAC1H,OAAOlC,IAAI,kBAxwC8E1b,EAAE,CAAA2b,gBAAA;IAAAC,OAAA,GAwwC2CN,eAAe;EAAA;AACzJ;AACA;EAAA,QAAApS,SAAA,oBAAAA,SAAA,KA1wC6FlJ,EAAE,CAAAmJ,iBAAA,CA0wCJyU,sBAAsB,EAAc,CAAC;IACpHxU,IAAI,EAAEnI,QAAQ;IACd0I,IAAI,EAAE,CAAC;MACCkS,OAAO,EAAE,CAACP,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,SAAS2C,cAAcA,CAAA,EAAG;EACtB,IAAIC,WAAW,GAAGC,qBAAqB,CAAC,CAAC;EACzC,IAAI,CAACD,WAAW,IAAI,CAAC7Y,SAAS,CAAC,CAAC,EAAE;IAC9B,OAAOG,SAAS;EACpB;EACA,IAAI0Y,WAAW,CAACpL,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACjCoL,WAAW,GAAGA,WAAW,CAAC7a,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,IAAI6a,WAAW,CAACpL,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACjCoL,WAAW,GAAGA,WAAW,CAAC7a,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAO6a,WAAW;AACtB;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC7B,IAAI,CAAC9Y,SAAS,CAAC,CAAC,EAAE;IACd,OAAO,EAAE;EACb;EACA,MAAM+Y,SAAS,GAAG9Y,MAAM,CAAC8Y,SAAS;EAClC,OAAOA,SAAS,CAACC,SAAS,GAAG,CAAC,CAAC,IAAID,SAAS,CAACtM,QAAQ;AACzD;;AAEA;AACA;AACA;;AAEA,SAASvG,uBAAuB,EAAEN,kBAAkB,EAAEP,qBAAqB,EAAEpD,iBAAiB,EAAEwC,oBAAoB,EAAE3D,gBAAgB,EAAEmF,2BAA2B,EAAEN,qBAAqB,EAAE4L,cAAc,EAAE/T,gBAAgB,EAAEgU,0BAA0B,EAAEpM,yBAAyB,EAAEqM,eAAe,EAAEzP,oBAAoB,EAAE+V,aAAa,EAAE3F,kBAAkB,EAAE6D,eAAe,EAAEf,aAAa,EAAExM,gBAAgB,EAAE6P,sBAAsB,EAAE/Y,WAAW,EAAEyB,aAAa,EAAEjE,OAAO,EAAE8b,qBAAqB,EAAEF,cAAc,EAAEzU,eAAe,EAAEwC,gBAAgB,EAAEC,YAAY,EAAEJ,gBAAgB,EAAE/I,QAAQ,EAAEkD,eAAe,EAAEX,SAAS,EAAEI,SAAS,EAAEjB,OAAO,EAAEC,UAAU,EAAEc,KAAK,EAAEZ,QAAQ,EAAEL,QAAQ,EAAEwB,aAAa,EAAEpB,QAAQ,EAAEoX,gBAAgB,EAAEM,sBAAsB,EAAED,gCAAgC,EAAED,2BAA2B,EAAEe,oBAAoB,EAAEZ,sBAAsB,EAAEO,0BAA0B,EAAEX,8BAA8B,EAAEQ,qBAAqB,EAAET,0BAA0B,EAAEvY,QAAQ,EAAEW,IAAI,EAAEW,WAAW,EAAEW,QAAQ,EAAEmI,SAAS,EAAEC,eAAe,EAAE3G,eAAe,EAAEhF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}