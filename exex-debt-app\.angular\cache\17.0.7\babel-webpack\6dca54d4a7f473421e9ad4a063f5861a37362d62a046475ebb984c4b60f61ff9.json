{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nlet ExexTableComponent = class ExexTableComponent {\n  constructor(windowSizeService) {\n    this.windowSizeService = windowSizeService;\n    this.editEvent = new EventEmitter();\n    this.deleteEvent = new EventEmitter();\n    this.selectedEvent = new EventEmitter();\n    this.heightTableScroll = 0;\n  }\n  ngOnInit() {\n    this.windowSizeService.heightTableSubject.subscribe(height => {\n      this.heightTableScroll = height;\n    });\n  }\n  onSelectionChange(rows) {\n    this.selectedEvent.emit(rows);\n  }\n  editRow(row) {\n    this.editEvent.emit(row);\n  }\n  deleteRow(row) {\n    this.deleteEvent.emit(row);\n  }\n};\n__decorate([Input()], ExexTableComponent.prototype, \"propExexTable\", void 0);\n__decorate([Output()], ExexTableComponent.prototype, \"editEvent\", void 0);\n__decorate([Output()], ExexTableComponent.prototype, \"deleteEvent\", void 0);\n__decorate([Output()], ExexTableComponent.prototype, \"selectedEvent\", void 0);\nExexTableComponent = __decorate([Component({\n  selector: 'exex-table',\n  template: `<p-table\n        [rows]=\"propExexTable.rows || 10\"\n        [columns]=\"propExexTable.columns\"\n        [value]=\"propExexTable.value\"\n        [paginator]=\"true\"\n        [scrollable]=\"true\"\n        [resizableColumns]=\"true\"\n        [scrollHeight]=\"heightTableScroll + 'px'\"\n        [(selection)]=\"selectedList\"\n        (selectionChange)=\"onSelectionChange($event)\"\n        [tableStyle]=\"{ 'min-width': '75rem' }\"\n        dataKey=\"id\"\n        columnResizeMode=\"expand\"\n        styleClass=\"p-datatable-striped p-datatable-gridlines\">\n        <ng-template pTemplate=\"header\" let-columns>\n            <tr>\n                <th style=\"width: 52px\" *ngIf=\"!!!propExexTable.isHideChecked\">\n                    <p-tableHeaderCheckbox />\n                </th>\n                <ng-container *ngFor=\"let col of columns; trackBy: col\">\n                    <th [pSortableColumn]=\"col.field\" [style.min-width]=\"col.width\" pResizableColumn>\n                        {{ col.title }} <p-sortIcon [field]=\"col.field\" />\n                    </th>\n                </ng-container>\n                <th style=\"width: 121px\" *ngIf=\"!!!propExexTable.isHideEdit\"></th>\n            </tr>\n        </ng-template>\n        <ng-template pTemplate=\"body\" let-row let-columns=\"columns\" let-index=\"rowIndex\">\n            <tr>\n                <td *ngIf=\"!!!propExexTable.isHideChecked\">\n                    <p-tableCheckbox [value]=\"row\" />\n                </td>\n                <ng-container *ngFor=\"let col of columns; trackBy: col\">\n                    <td [ngClass]=\"row[col.field] | textAlign\">\n                        {{ row[col.field] | money }}\n                    </td>\n                </ng-container>\n                <td class=\"custom-group-button-edit\" *ngIf=\"!!!propExexTable.isHideEdit\">\n                    <p-button\n                        icon=\"pi pi-pencil\"\n                        class=\"mr-2\"\n                        severity=\"success\"\n                        size=\"small\"\n                        [raised]=\"true\"\n                        (onClick)=\"editRow(row)\" />\n                    <p-button\n                        icon=\"pi pi-trash\"\n                        severity=\"danger\"\n                        size=\"small\"\n                        [raised]=\"true\"\n                        (onClick)=\"deleteRow(row)\" />\n                </td>\n            </tr>\n        </ng-template>\n    </p-table> `\n})], ExexTableComponent);\nexport { ExexTableComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "ExexTableComponent", "constructor", "windowSizeService", "editEvent", "deleteEvent", "selectedEvent", "heightTableScroll", "ngOnInit", "heightTableSubject", "subscribe", "height", "onSelectionChange", "rows", "emit", "editRow", "row", "deleteRow", "__decorate", "selector", "template"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\common\\exex-table\\exex-table.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';\r\nimport { WindowSizeService } from 'src/app/core/service/window-size.service';\r\nimport { IPropExexTable } from './exex-table.model';\r\n\r\n@Component({\r\n    selector: 'exex-table',\r\n    template: `<p-table\r\n        [rows]=\"propExexTable.rows || 10\"\r\n        [columns]=\"propExexTable.columns\"\r\n        [value]=\"propExexTable.value\"\r\n        [paginator]=\"true\"\r\n        [scrollable]=\"true\"\r\n        [resizableColumns]=\"true\"\r\n        [scrollHeight]=\"heightTableScroll + 'px'\"\r\n        [(selection)]=\"selectedList\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n        [tableStyle]=\"{ 'min-width': '75rem' }\"\r\n        dataKey=\"id\"\r\n        columnResizeMode=\"expand\"\r\n        styleClass=\"p-datatable-striped p-datatable-gridlines\">\r\n        <ng-template pTemplate=\"header\" let-columns>\r\n            <tr>\r\n                <th style=\"width: 52px\" *ngIf=\"!!!propExexTable.isHideChecked\">\r\n                    <p-tableHeaderCheckbox />\r\n                </th>\r\n                <ng-container *ngFor=\"let col of columns; trackBy: col\">\r\n                    <th [pSortableColumn]=\"col.field\" [style.min-width]=\"col.width\" pResizableColumn>\r\n                        {{ col.title }} <p-sortIcon [field]=\"col.field\" />\r\n                    </th>\r\n                </ng-container>\r\n                <th style=\"width: 121px\" *ngIf=\"!!!propExexTable.isHideEdit\"></th>\r\n            </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"body\" let-row let-columns=\"columns\" let-index=\"rowIndex\">\r\n            <tr>\r\n                <td *ngIf=\"!!!propExexTable.isHideChecked\">\r\n                    <p-tableCheckbox [value]=\"row\" />\r\n                </td>\r\n                <ng-container *ngFor=\"let col of columns; trackBy: col\">\r\n                    <td [ngClass]=\"row[col.field] | textAlign\">\r\n                        {{ row[col.field] | money }}\r\n                    </td>\r\n                </ng-container>\r\n                <td class=\"custom-group-button-edit\" *ngIf=\"!!!propExexTable.isHideEdit\">\r\n                    <p-button\r\n                        icon=\"pi pi-pencil\"\r\n                        class=\"mr-2\"\r\n                        severity=\"success\"\r\n                        size=\"small\"\r\n                        [raised]=\"true\"\r\n                        (onClick)=\"editRow(row)\" />\r\n                    <p-button\r\n                        icon=\"pi pi-trash\"\r\n                        severity=\"danger\"\r\n                        size=\"small\"\r\n                        [raised]=\"true\"\r\n                        (onClick)=\"deleteRow(row)\" />\r\n                </td>\r\n            </tr>\r\n        </ng-template>\r\n    </p-table> `,\r\n})\r\nexport class ExexTableComponent {\r\n    @Input() propExexTable!: IPropExexTable;\r\n    @Output() editEvent = new EventEmitter<any>();\r\n    @Output() deleteEvent = new EventEmitter<any>();\r\n    @Output() selectedEvent = new EventEmitter<any>();\r\n\r\n    selectedList!: any[] | null;\r\n    heightTableScroll: number = 0;\r\n\r\n    constructor(private windowSizeService: WindowSizeService) {}\r\n\r\n    ngOnInit() {\r\n        this.windowSizeService.heightTableSubject.subscribe((height) => {\r\n            this.heightTableScroll = height;\r\n        });\r\n    }\r\n\r\n    onSelectionChange(rows: any) {\r\n        this.selectedEvent.emit(rows);\r\n    }\r\n\r\n    editRow(row) {\r\n        this.editEvent.emit(row);\r\n    }\r\n\r\n    deleteRow(row) {\r\n        this.deleteEvent.emit(row);\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAuB,eAAe;AA8D9E,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAS3BC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAP3B,KAAAC,SAAS,GAAG,IAAIN,YAAY,EAAO;IACnC,KAAAO,WAAW,GAAG,IAAIP,YAAY,EAAO;IACrC,KAAAQ,aAAa,GAAG,IAAIR,YAAY,EAAO;IAGjD,KAAAS,iBAAiB,GAAW,CAAC;EAE8B;EAE3DC,QAAQA,CAAA;IACJ,IAAI,CAACL,iBAAiB,CAACM,kBAAkB,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC3D,IAAI,CAACJ,iBAAiB,GAAGI,MAAM;IACnC,CAAC,CAAC;EACN;EAEAC,iBAAiBA,CAACC,IAAS;IACvB,IAAI,CAACP,aAAa,CAACQ,IAAI,CAACD,IAAI,CAAC;EACjC;EAEAE,OAAOA,CAACC,GAAG;IACP,IAAI,CAACZ,SAAS,CAACU,IAAI,CAACE,GAAG,CAAC;EAC5B;EAEAC,SAASA,CAACD,GAAG;IACT,IAAI,CAACX,WAAW,CAACS,IAAI,CAACE,GAAG,CAAC;EAC9B;CACH;AA3BYE,UAAA,EAARnB,KAAK,EAAE,C,wDAAgC;AAC9BmB,UAAA,EAATlB,MAAM,EAAE,C,oDAAqC;AACpCkB,UAAA,EAATlB,MAAM,EAAE,C,sDAAuC;AACtCkB,UAAA,EAATlB,MAAM,EAAE,C,wDAAyC;AAJzCC,kBAAkB,GAAAiB,UAAA,EA1D9BrB,SAAS,CAAC;EACPsB,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAuDb,CAAC,C,EACWnB,kBAAkB,CA4B9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}