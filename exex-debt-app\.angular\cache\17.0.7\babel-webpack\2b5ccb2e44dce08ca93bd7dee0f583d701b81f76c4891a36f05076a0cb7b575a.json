{"ast": null, "code": "import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, Directive, Inject, Input, HostListener, NgModule } from '@angular/core';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nclass Tooltip {\n  platformId;\n  el;\n  zone;\n  config;\n  renderer;\n  viewContainer;\n  /**\n   * Position of the tooltip.\n   * @group Props\n   */\n  tooltipPosition;\n  /**\n   * Event to show the tooltip.\n   * @group Props\n   */\n  tooltipEvent = 'hover';\n  /**\n   *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Type of CSS position.\n   * @group Props\n   */\n  positionStyle;\n  /**\n   * Style class of the tooltip.\n   * @group Props\n   */\n  tooltipStyleClass;\n  /**\n   * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n   * @group Props\n   */\n  tooltipZIndex;\n  /**\n   * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n   * @group Props\n   */\n  escape = true;\n  /**\n   * Delay to show the tooltip in milliseconds.\n   * @group Props\n   */\n  showDelay;\n  /**\n   * Delay to hide the tooltip in milliseconds.\n   * @group Props\n   */\n  hideDelay;\n  /**\n   * Time to wait in milliseconds to hide the tooltip even it is active.\n   * @group Props\n   */\n  life;\n  /**\n   * Specifies the additional vertical offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionTop;\n  /**\n   * Specifies the additional horizontal offset of the tooltip from its default position.\n   * @group Props\n   */\n  positionLeft;\n  /**\n   * Whether to hide tooltip when hovering over tooltip content.\n   * @group Props\n   */\n  autoHide = true;\n  /**\n   * Automatically adjusts the element position when there is not enough space on the selected position.\n   * @group Props\n   */\n  fitContent = true;\n  /**\n   * Whether to hide tooltip on escape key press.\n   * @group Props\n   */\n  hideOnEscape = true;\n  /**\n   * Content of the tooltip.\n   * @group Props\n   */\n  content;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @defaultValue false\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n    this.deactivate();\n  }\n  /**\n   * Specifies the tooltip configuration options for the component.\n   * @group Props\n   */\n  tooltipOptions;\n  _tooltipOptions = {\n    tooltipLabel: null,\n    tooltipPosition: 'right',\n    tooltipEvent: 'hover',\n    appendTo: 'body',\n    positionStyle: null,\n    tooltipStyleClass: null,\n    tooltipZIndex: 'auto',\n    escape: true,\n    disabled: null,\n    showDelay: null,\n    hideDelay: null,\n    positionTop: null,\n    positionLeft: null,\n    life: null,\n    autoHide: true,\n    hideOnEscape: true,\n    id: UniqueComponentId() + '_tooltip'\n  };\n  _disabled;\n  container;\n  styleClass;\n  tooltipText;\n  showTimeout;\n  hideTimeout;\n  active;\n  mouseEnterListener;\n  mouseLeaveListener;\n  containerMouseleaveListener;\n  clickListener;\n  focusListener;\n  blurListener;\n  scrollHandler;\n  resizeListener;\n  constructor(platformId, el, zone, config, renderer, viewContainer) {\n    this.platformId = platformId;\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n    this.renderer = renderer;\n    this.viewContainer = viewContainer;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        if (this.getOption('tooltipEvent') === 'hover') {\n          this.mouseEnterListener = this.onMouseEnter.bind(this);\n          this.mouseLeaveListener = this.onMouseLeave.bind(this);\n          this.clickListener = this.onInputClick.bind(this);\n          this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n          this.el.nativeElement.addEventListener('click', this.clickListener);\n          this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n        } else if (this.getOption('tooltipEvent') === 'focus') {\n          this.focusListener = this.onFocus.bind(this);\n          this.blurListener = this.onBlur.bind(this);\n          let target = this.getTarget(this.el.nativeElement);\n          target.addEventListener('focus', this.focusListener);\n          target.addEventListener('blur', this.blurListener);\n        }\n      });\n    }\n  }\n  ngOnChanges(simpleChange) {\n    if (simpleChange.tooltipPosition) {\n      this.setOption({\n        tooltipPosition: simpleChange.tooltipPosition.currentValue\n      });\n    }\n    if (simpleChange.tooltipEvent) {\n      this.setOption({\n        tooltipEvent: simpleChange.tooltipEvent.currentValue\n      });\n    }\n    if (simpleChange.appendTo) {\n      this.setOption({\n        appendTo: simpleChange.appendTo.currentValue\n      });\n    }\n    if (simpleChange.positionStyle) {\n      this.setOption({\n        positionStyle: simpleChange.positionStyle.currentValue\n      });\n    }\n    if (simpleChange.tooltipStyleClass) {\n      this.setOption({\n        tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n      });\n    }\n    if (simpleChange.tooltipZIndex) {\n      this.setOption({\n        tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n      });\n    }\n    if (simpleChange.escape) {\n      this.setOption({\n        escape: simpleChange.escape.currentValue\n      });\n    }\n    if (simpleChange.showDelay) {\n      this.setOption({\n        showDelay: simpleChange.showDelay.currentValue\n      });\n    }\n    if (simpleChange.hideDelay) {\n      this.setOption({\n        hideDelay: simpleChange.hideDelay.currentValue\n      });\n    }\n    if (simpleChange.life) {\n      this.setOption({\n        life: simpleChange.life.currentValue\n      });\n    }\n    if (simpleChange.positionTop) {\n      this.setOption({\n        positionTop: simpleChange.positionTop.currentValue\n      });\n    }\n    if (simpleChange.positionLeft) {\n      this.setOption({\n        positionLeft: simpleChange.positionLeft.currentValue\n      });\n    }\n    if (simpleChange.disabled) {\n      this.setOption({\n        disabled: simpleChange.disabled.currentValue\n      });\n    }\n    if (simpleChange.content) {\n      this.setOption({\n        tooltipLabel: simpleChange.content.currentValue\n      });\n      if (this.active) {\n        if (simpleChange.content.currentValue) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n    if (simpleChange.autoHide) {\n      this.setOption({\n        autoHide: simpleChange.autoHide.currentValue\n      });\n    }\n    if (simpleChange.id) {\n      this.setOption({\n        id: simpleChange.id.currentValue\n      });\n    }\n    if (simpleChange.tooltipOptions) {\n      this._tooltipOptions = {\n        ...this._tooltipOptions,\n        ...simpleChange.tooltipOptions.currentValue\n      };\n      this.deactivate();\n      if (this.active) {\n        if (this.getOption('tooltipLabel')) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n  }\n  isAutoHide() {\n    return this.getOption('autoHide');\n  }\n  onMouseEnter(e) {\n    if (!this.container && !this.showTimeout) {\n      this.activate();\n    }\n  }\n  onMouseLeave(e) {\n    if (!this.isAutoHide()) {\n      const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n      !valid && this.deactivate();\n    } else {\n      this.deactivate();\n    }\n  }\n  onFocus(e) {\n    this.activate();\n  }\n  onBlur(e) {\n    this.deactivate();\n  }\n  onInputClick(e) {\n    this.deactivate();\n  }\n  onPressEscape() {\n    if (this.hideOnEscape) {\n      this.deactivate();\n    }\n  }\n  activate() {\n    this.active = true;\n    this.clearHideTimeout();\n    if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n      this.show();\n    }, this.getOption('showDelay'));else this.show();\n    if (this.getOption('life')) {\n      let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, duration);\n    }\n  }\n  deactivate() {\n    this.active = false;\n    this.clearShowTimeout();\n    if (this.getOption('hideDelay')) {\n      this.clearHideTimeout(); //life timeout\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, this.getOption('hideDelay'));\n    } else {\n      this.hide();\n    }\n  }\n  create() {\n    if (this.container) {\n      this.clearHideTimeout();\n      this.remove();\n    }\n    this.container = document.createElement('div');\n    this.container.setAttribute('id', this.getOption('id'));\n    this.container.setAttribute('role', 'tooltip');\n    let tooltipArrow = document.createElement('div');\n    tooltipArrow.className = 'p-tooltip-arrow';\n    this.container.appendChild(tooltipArrow);\n    this.tooltipText = document.createElement('div');\n    this.tooltipText.className = 'p-tooltip-text';\n    this.updateText();\n    if (this.getOption('positionStyle')) {\n      this.container.style.position = this.getOption('positionStyle');\n    }\n    this.container.appendChild(this.tooltipText);\n    if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n    this.container.style.display = 'inline-block';\n    if (this.fitContent) {\n      this.container.style.width = 'fit-content';\n    }\n    if (this.isAutoHide()) {\n      this.container.style.pointerEvents = 'none';\n    } else {\n      this.container.style.pointerEvents = 'unset';\n      this.bindContainerMouseleaveListener();\n    }\n  }\n  bindContainerMouseleaveListener() {\n    if (!this.containerMouseleaveListener) {\n      const targetEl = this.container ?? this.container.nativeElement;\n      this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', e => {\n        this.deactivate();\n      });\n    }\n  }\n  unbindContainerMouseleaveListener() {\n    if (this.containerMouseleaveListener) {\n      this.bindContainerMouseleaveListener();\n      this.containerMouseleaveListener = null;\n    }\n  }\n  show() {\n    if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n      return;\n    }\n    this.create();\n    this.align();\n    DomHandler.fadeIn(this.container, 250);\n    if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n  hide() {\n    if (this.getOption('tooltipZIndex') === 'auto') {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n  }\n  updateText() {\n    const content = this.getOption('tooltipLabel');\n    if (content instanceof TemplateRef) {\n      const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n      embeddedViewRef.detectChanges();\n      embeddedViewRef.rootNodes.forEach(node => this.tooltipText.appendChild(node));\n    } else if (this.getOption('escape')) {\n      this.tooltipText.innerHTML = '';\n      this.tooltipText.appendChild(document.createTextNode(content));\n    } else {\n      this.tooltipText.innerHTML = content;\n    }\n  }\n  align() {\n    let position = this.getOption('tooltipPosition');\n    switch (position) {\n      case 'top':\n        this.alignTop();\n        if (this.isOutOfBounds()) {\n          this.alignBottom();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'bottom':\n        this.alignBottom();\n        if (this.isOutOfBounds()) {\n          this.alignTop();\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n        break;\n      case 'left':\n        this.alignLeft();\n        if (this.isOutOfBounds()) {\n          this.alignRight();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n      case 'right':\n        this.alignRight();\n        if (this.isOutOfBounds()) {\n          this.alignLeft();\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n        break;\n    }\n  }\n  getHostOffset() {\n    if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n      let offset = this.el.nativeElement.getBoundingClientRect();\n      let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n      let targetTop = offset.top + DomHandler.getWindowScrollTop();\n      return {\n        left: targetLeft,\n        top: targetTop\n      };\n    } else {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  alignRight() {\n    this.preAlign('right');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignLeft() {\n    this.preAlign('left');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignTop() {\n    this.preAlign('top');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  alignBottom() {\n    this.preAlign('bottom');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n  setOption(option) {\n    this._tooltipOptions = {\n      ...this._tooltipOptions,\n      ...option\n    };\n  }\n  getOption(option) {\n    return this._tooltipOptions[option];\n  }\n  getTarget(el) {\n    return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n  }\n  preAlign(position) {\n    this.container.style.left = -999 + 'px';\n    this.container.style.top = -999 + 'px';\n    let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n    this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n  }\n  isOutOfBounds() {\n    let offset = this.container.getBoundingClientRect();\n    let targetTop = offset.top;\n    let targetLeft = offset.left;\n    let width = DomHandler.getOuterWidth(this.container);\n    let height = DomHandler.getOuterHeight(this.container);\n    let viewport = DomHandler.getViewport();\n    return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n  }\n  onWindowResize(e) {\n    this.hide();\n  }\n  bindDocumentResizeListener() {\n    this.zone.runOutsideAngular(() => {\n      this.resizeListener = this.onWindowResize.bind(this);\n      window.addEventListener('resize', this.resizeListener);\n    });\n  }\n  unbindDocumentResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n        if (this.container) {\n          this.hide();\n        }\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  unbindEvents() {\n    if (this.getOption('tooltipEvent') === 'hover') {\n      this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n      this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n      this.el.nativeElement.removeEventListener('click', this.clickListener);\n    } else if (this.getOption('tooltipEvent') === 'focus') {\n      let target = this.getTarget(this.el.nativeElement);\n      target.removeEventListener('focus', this.focusListener);\n      target.removeEventListener('blur', this.blurListener);\n    }\n    this.unbindDocumentResizeListener();\n  }\n  remove() {\n    if (this.container && this.container.parentElement) {\n      if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n    }\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.unbindContainerMouseleaveListener();\n    this.clearTimeouts();\n    this.container = null;\n    this.scrollHandler = null;\n  }\n  clearShowTimeout() {\n    if (this.showTimeout) {\n      clearTimeout(this.showTimeout);\n      this.showTimeout = null;\n    }\n  }\n  clearHideTimeout() {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n  }\n  clearTimeouts() {\n    this.clearShowTimeout();\n    this.clearHideTimeout();\n  }\n  ngOnDestroy() {\n    this.unbindEvents();\n    if (this.container) {\n      ZIndexUtils.clear(this.container);\n    }\n    this.remove();\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n  }\n  static ɵfac = function Tooltip_Factory(t) {\n    return new (t || Tooltip)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Tooltip,\n    selectors: [[\"\", \"pTooltip\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    hostBindings: function Tooltip_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.escape\", function Tooltip_keydown_escape_HostBindingHandler($event) {\n          return ctx.onPressEscape($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    inputs: {\n      tooltipPosition: \"tooltipPosition\",\n      tooltipEvent: \"tooltipEvent\",\n      appendTo: \"appendTo\",\n      positionStyle: \"positionStyle\",\n      tooltipStyleClass: \"tooltipStyleClass\",\n      tooltipZIndex: \"tooltipZIndex\",\n      escape: \"escape\",\n      showDelay: \"showDelay\",\n      hideDelay: \"hideDelay\",\n      life: \"life\",\n      positionTop: \"positionTop\",\n      positionLeft: \"positionLeft\",\n      autoHide: \"autoHide\",\n      fitContent: \"fitContent\",\n      hideOnEscape: \"hideOnEscape\",\n      content: [\"pTooltip\", \"content\"],\n      disabled: [\"tooltipDisabled\", \"disabled\"],\n      tooltipOptions: \"tooltipOptions\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[pTooltip]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.PrimeNGConfig\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipEvent: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    positionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    tooltipZIndex: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }],\n    showDelay: [{\n      type: Input\n    }],\n    hideDelay: [{\n      type: Input\n    }],\n    life: [{\n      type: Input\n    }],\n    positionTop: [{\n      type: Input\n    }],\n    positionLeft: [{\n      type: Input\n    }],\n    autoHide: [{\n      type: Input\n    }],\n    fitContent: [{\n      type: Input\n    }],\n    hideOnEscape: [{\n      type: Input\n    }],\n    content: [{\n      type: Input,\n      args: ['pTooltip']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['tooltipDisabled']\n    }],\n    tooltipOptions: [{\n      type: Input\n    }],\n    onPressEscape: [{\n      type: HostListener,\n      args: ['document:keydown.escape', ['$event']]\n    }]\n  });\n})();\nclass TooltipModule {\n  static ɵfac = function TooltipModule_Factory(t) {\n    return new (t || TooltipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TooltipModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tooltip],\n      declarations: [Tooltip]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };", "map": {"version": 3, "names": ["isPlatformBrowser", "CommonModule", "i0", "TemplateRef", "PLATFORM_ID", "Directive", "Inject", "Input", "HostListener", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "UniqueComponentId", "ZIndexUtils", "i1", "<PERSON><PERSON><PERSON>", "platformId", "el", "zone", "config", "renderer", "viewContainer", "tooltipPosition", "tooltipEvent", "appendTo", "positionStyle", "tooltipStyleClass", "tooltipZIndex", "escape", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "life", "positionTop", "positionLeft", "autoHide", "<PERSON><PERSON><PERSON><PERSON>", "hideOnEscape", "content", "disabled", "_disabled", "val", "deactivate", "tooltipOptions", "_tooltipOptions", "tooltipLabel", "id", "container", "styleClass", "tooltipText", "showTimeout", "hideTimeout", "active", "mouseEnterListener", "mouseLeaveListener", "containerMouseleaveListener", "clickListener", "focusListener", "blurListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "constructor", "ngAfterViewInit", "runOutsideAngular", "getOption", "onMouseEnter", "bind", "onMouseLeave", "onInputClick", "nativeElement", "addEventListener", "onFocus", "onBlur", "target", "get<PERSON><PERSON><PERSON>", "ngOnChanges", "simpleChange", "setOption", "currentValue", "offsetParent", "updateText", "align", "show", "hide", "isAutoHide", "e", "activate", "valid", "hasClass", "relatedTarget", "onPressEscape", "clearHideTimeout", "setTimeout", "duration", "clearShowTimeout", "create", "remove", "document", "createElement", "setAttribute", "tooltipArrow", "className", "append<PERSON><PERSON><PERSON>", "style", "position", "body", "display", "width", "pointerEvents", "bindContainerMouseleaveListener", "targetEl", "listen", "unbindContainerMouseleaveListener", "fadeIn", "set", "zIndex", "tooltip", "bindDocumentResizeListener", "bindScrollListener", "clear", "embeddedViewRef", "createEmbeddedView", "detectChanges", "rootNodes", "for<PERSON>ach", "node", "innerHTML", "createTextNode", "alignTop", "isOutOfBounds", "alignBottom", "alignRight", "alignLeft", "getHostOffset", "offset", "getBoundingClientRect", "targetLeft", "left", "getWindowScrollLeft", "targetTop", "top", "getWindowScrollTop", "preAlign", "hostOffset", "getOuterWidth", "getOuterHeight", "option", "findSingle", "defaultClassName", "height", "viewport", "getViewport", "onWindowResize", "window", "unbindDocumentResizeListener", "removeEventListener", "unbindScrollListener", "unbindEvents", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeouts", "clearTimeout", "ngOnDestroy", "destroy", "ɵfac", "Tooltip_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "PrimeNGConfig", "Renderer2", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostBindings", "Tooltip_HostBindings", "rf", "ctx", "ɵɵlistener", "Tooltip_keydown_escape_HostBindingHandler", "$event", "ɵɵresolveDocument", "inputs", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "undefined", "decorators", "TooltipModule", "TooltipModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/primeng/fesm2022/primeng-tooltip.mjs"], "sourcesContent": ["import { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { TemplateRef, PLATFORM_ID, Directive, Inject, Input, HostListener, NgModule } from '@angular/core';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\nclass Tooltip {\n    platformId;\n    el;\n    zone;\n    config;\n    renderer;\n    viewContainer;\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    tooltipPosition;\n    /**\n     * Event to show the tooltip.\n     * @group Props\n     */\n    tooltipEvent = 'hover';\n    /**\n     *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    positionStyle;\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    tooltipStyleClass;\n    /**\n     * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n     * @group Props\n     */\n    tooltipZIndex;\n    /**\n     * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n     * @group Props\n     */\n    escape = true;\n    /**\n     * Delay to show the tooltip in milliseconds.\n     * @group Props\n     */\n    showDelay;\n    /**\n     * Delay to hide the tooltip in milliseconds.\n     * @group Props\n     */\n    hideDelay;\n    /**\n     * Time to wait in milliseconds to hide the tooltip even it is active.\n     * @group Props\n     */\n    life;\n    /**\n     * Specifies the additional vertical offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionTop;\n    /**\n     * Specifies the additional horizontal offset of the tooltip from its default position.\n     * @group Props\n     */\n    positionLeft;\n    /**\n     * Whether to hide tooltip when hovering over tooltip content.\n     * @group Props\n     */\n    autoHide = true;\n    /**\n     * Automatically adjusts the element position when there is not enough space on the selected position.\n     * @group Props\n     */\n    fitContent = true;\n    /**\n     * Whether to hide tooltip on escape key press.\n     * @group Props\n     */\n    hideOnEscape = true;\n    /**\n     * Content of the tooltip.\n     * @group Props\n     */\n    content;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @defaultValue false\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n        this.deactivate();\n    }\n    /**\n     * Specifies the tooltip configuration options for the component.\n     * @group Props\n     */\n    tooltipOptions;\n    _tooltipOptions = {\n        tooltipLabel: null,\n        tooltipPosition: 'right',\n        tooltipEvent: 'hover',\n        appendTo: 'body',\n        positionStyle: null,\n        tooltipStyleClass: null,\n        tooltipZIndex: 'auto',\n        escape: true,\n        disabled: null,\n        showDelay: null,\n        hideDelay: null,\n        positionTop: null,\n        positionLeft: null,\n        life: null,\n        autoHide: true,\n        hideOnEscape: true,\n        id: UniqueComponentId() + '_tooltip'\n    };\n    _disabled;\n    container;\n    styleClass;\n    tooltipText;\n    showTimeout;\n    hideTimeout;\n    active;\n    mouseEnterListener;\n    mouseLeaveListener;\n    containerMouseleaveListener;\n    clickListener;\n    focusListener;\n    blurListener;\n    scrollHandler;\n    resizeListener;\n    constructor(platformId, el, zone, config, renderer, viewContainer) {\n        this.platformId = platformId;\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n        this.renderer = renderer;\n        this.viewContainer = viewContainer;\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                if (this.getOption('tooltipEvent') === 'hover') {\n                    this.mouseEnterListener = this.onMouseEnter.bind(this);\n                    this.mouseLeaveListener = this.onMouseLeave.bind(this);\n                    this.clickListener = this.onInputClick.bind(this);\n                    this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n                    this.el.nativeElement.addEventListener('click', this.clickListener);\n                    this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n                }\n                else if (this.getOption('tooltipEvent') === 'focus') {\n                    this.focusListener = this.onFocus.bind(this);\n                    this.blurListener = this.onBlur.bind(this);\n                    let target = this.getTarget(this.el.nativeElement);\n                    target.addEventListener('focus', this.focusListener);\n                    target.addEventListener('blur', this.blurListener);\n                }\n            });\n        }\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.tooltipPosition) {\n            this.setOption({ tooltipPosition: simpleChange.tooltipPosition.currentValue });\n        }\n        if (simpleChange.tooltipEvent) {\n            this.setOption({ tooltipEvent: simpleChange.tooltipEvent.currentValue });\n        }\n        if (simpleChange.appendTo) {\n            this.setOption({ appendTo: simpleChange.appendTo.currentValue });\n        }\n        if (simpleChange.positionStyle) {\n            this.setOption({ positionStyle: simpleChange.positionStyle.currentValue });\n        }\n        if (simpleChange.tooltipStyleClass) {\n            this.setOption({ tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue });\n        }\n        if (simpleChange.tooltipZIndex) {\n            this.setOption({ tooltipZIndex: simpleChange.tooltipZIndex.currentValue });\n        }\n        if (simpleChange.escape) {\n            this.setOption({ escape: simpleChange.escape.currentValue });\n        }\n        if (simpleChange.showDelay) {\n            this.setOption({ showDelay: simpleChange.showDelay.currentValue });\n        }\n        if (simpleChange.hideDelay) {\n            this.setOption({ hideDelay: simpleChange.hideDelay.currentValue });\n        }\n        if (simpleChange.life) {\n            this.setOption({ life: simpleChange.life.currentValue });\n        }\n        if (simpleChange.positionTop) {\n            this.setOption({ positionTop: simpleChange.positionTop.currentValue });\n        }\n        if (simpleChange.positionLeft) {\n            this.setOption({ positionLeft: simpleChange.positionLeft.currentValue });\n        }\n        if (simpleChange.disabled) {\n            this.setOption({ disabled: simpleChange.disabled.currentValue });\n        }\n        if (simpleChange.content) {\n            this.setOption({ tooltipLabel: simpleChange.content.currentValue });\n            if (this.active) {\n                if (simpleChange.content.currentValue) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n        if (simpleChange.autoHide) {\n            this.setOption({ autoHide: simpleChange.autoHide.currentValue });\n        }\n        if (simpleChange.id) {\n            this.setOption({ id: simpleChange.id.currentValue });\n        }\n        if (simpleChange.tooltipOptions) {\n            this._tooltipOptions = { ...this._tooltipOptions, ...simpleChange.tooltipOptions.currentValue };\n            this.deactivate();\n            if (this.active) {\n                if (this.getOption('tooltipLabel')) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n    }\n    isAutoHide() {\n        return this.getOption('autoHide');\n    }\n    onMouseEnter(e) {\n        if (!this.container && !this.showTimeout) {\n            this.activate();\n        }\n    }\n    onMouseLeave(e) {\n        if (!this.isAutoHide()) {\n            const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n            !valid && this.deactivate();\n        }\n        else {\n            this.deactivate();\n        }\n    }\n    onFocus(e) {\n        this.activate();\n    }\n    onBlur(e) {\n        this.deactivate();\n    }\n    onInputClick(e) {\n        this.deactivate();\n    }\n    onPressEscape() {\n        if (this.hideOnEscape) {\n            this.deactivate();\n        }\n    }\n    activate() {\n        this.active = true;\n        this.clearHideTimeout();\n        if (this.getOption('showDelay'))\n            this.showTimeout = setTimeout(() => {\n                this.show();\n            }, this.getOption('showDelay'));\n        else\n            this.show();\n        if (this.getOption('life')) {\n            let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n            this.hideTimeout = setTimeout(() => {\n                this.hide();\n            }, duration);\n        }\n    }\n    deactivate() {\n        this.active = false;\n        this.clearShowTimeout();\n        if (this.getOption('hideDelay')) {\n            this.clearHideTimeout(); //life timeout\n            this.hideTimeout = setTimeout(() => {\n                this.hide();\n            }, this.getOption('hideDelay'));\n        }\n        else {\n            this.hide();\n        }\n    }\n    create() {\n        if (this.container) {\n            this.clearHideTimeout();\n            this.remove();\n        }\n        this.container = document.createElement('div');\n        this.container.setAttribute('id', this.getOption('id'));\n        this.container.setAttribute('role', 'tooltip');\n        let tooltipArrow = document.createElement('div');\n        tooltipArrow.className = 'p-tooltip-arrow';\n        this.container.appendChild(tooltipArrow);\n        this.tooltipText = document.createElement('div');\n        this.tooltipText.className = 'p-tooltip-text';\n        this.updateText();\n        if (this.getOption('positionStyle')) {\n            this.container.style.position = this.getOption('positionStyle');\n        }\n        this.container.appendChild(this.tooltipText);\n        if (this.getOption('appendTo') === 'body')\n            document.body.appendChild(this.container);\n        else if (this.getOption('appendTo') === 'target')\n            DomHandler.appendChild(this.container, this.el.nativeElement);\n        else\n            DomHandler.appendChild(this.container, this.getOption('appendTo'));\n        this.container.style.display = 'inline-block';\n        if (this.fitContent) {\n            this.container.style.width = 'fit-content';\n        }\n        if (this.isAutoHide()) {\n            this.container.style.pointerEvents = 'none';\n        }\n        else {\n            this.container.style.pointerEvents = 'unset';\n            this.bindContainerMouseleaveListener();\n        }\n    }\n    bindContainerMouseleaveListener() {\n        if (!this.containerMouseleaveListener) {\n            const targetEl = this.container ?? this.container.nativeElement;\n            this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', (e) => {\n                this.deactivate();\n            });\n        }\n    }\n    unbindContainerMouseleaveListener() {\n        if (this.containerMouseleaveListener) {\n            this.bindContainerMouseleaveListener();\n            this.containerMouseleaveListener = null;\n        }\n    }\n    show() {\n        if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n            return;\n        }\n        this.create();\n        this.align();\n        DomHandler.fadeIn(this.container, 250);\n        if (this.getOption('tooltipZIndex') === 'auto')\n            ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);\n        else\n            this.container.style.zIndex = this.getOption('tooltipZIndex');\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n    hide() {\n        if (this.getOption('tooltipZIndex') === 'auto') {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n    }\n    updateText() {\n        const content = this.getOption('tooltipLabel');\n        if (content instanceof TemplateRef) {\n            const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n            embeddedViewRef.detectChanges();\n            embeddedViewRef.rootNodes.forEach((node) => this.tooltipText.appendChild(node));\n        }\n        else if (this.getOption('escape')) {\n            this.tooltipText.innerHTML = '';\n            this.tooltipText.appendChild(document.createTextNode(content));\n        }\n        else {\n            this.tooltipText.innerHTML = content;\n        }\n    }\n    align() {\n        let position = this.getOption('tooltipPosition');\n        switch (position) {\n            case 'top':\n                this.alignTop();\n                if (this.isOutOfBounds()) {\n                    this.alignBottom();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'bottom':\n                this.alignBottom();\n                if (this.isOutOfBounds()) {\n                    this.alignTop();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'left':\n                this.alignLeft();\n                if (this.isOutOfBounds()) {\n                    this.alignRight();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n            case 'right':\n                this.alignRight();\n                if (this.isOutOfBounds()) {\n                    this.alignLeft();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n        }\n    }\n    getHostOffset() {\n        if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n            let offset = this.el.nativeElement.getBoundingClientRect();\n            let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n            let targetTop = offset.top + DomHandler.getWindowScrollTop();\n            return { left: targetLeft, top: targetTop };\n        }\n        else {\n            return { left: 0, top: 0 };\n        }\n    }\n    alignRight() {\n        this.preAlign('right');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignLeft() {\n        this.preAlign('left');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignTop() {\n        this.preAlign('top');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignBottom() {\n        this.preAlign('bottom');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    setOption(option) {\n        this._tooltipOptions = { ...this._tooltipOptions, ...option };\n    }\n    getOption(option) {\n        return this._tooltipOptions[option];\n    }\n    getTarget(el) {\n        return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n    }\n    preAlign(position) {\n        this.container.style.left = -999 + 'px';\n        this.container.style.top = -999 + 'px';\n        let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n        this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n    isOutOfBounds() {\n        let offset = this.container.getBoundingClientRect();\n        let targetTop = offset.top;\n        let targetLeft = offset.left;\n        let width = DomHandler.getOuterWidth(this.container);\n        let height = DomHandler.getOuterHeight(this.container);\n        let viewport = DomHandler.getViewport();\n        return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n    }\n    onWindowResize(e) {\n        this.hide();\n    }\n    bindDocumentResizeListener() {\n        this.zone.runOutsideAngular(() => {\n            this.resizeListener = this.onWindowResize.bind(this);\n            window.addEventListener('resize', this.resizeListener);\n        });\n    }\n    unbindDocumentResizeListener() {\n        if (this.resizeListener) {\n            window.removeEventListener('resize', this.resizeListener);\n            this.resizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (this.container) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    unbindEvents() {\n        if (this.getOption('tooltipEvent') === 'hover') {\n            this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n            this.el.nativeElement.removeEventListener('click', this.clickListener);\n        }\n        else if (this.getOption('tooltipEvent') === 'focus') {\n            let target = this.getTarget(this.el.nativeElement);\n            target.removeEventListener('focus', this.focusListener);\n            target.removeEventListener('blur', this.blurListener);\n        }\n        this.unbindDocumentResizeListener();\n    }\n    remove() {\n        if (this.container && this.container.parentElement) {\n            if (this.getOption('appendTo') === 'body')\n                document.body.removeChild(this.container);\n            else if (this.getOption('appendTo') === 'target')\n                this.el.nativeElement.removeChild(this.container);\n            else\n                DomHandler.removeChild(this.container, this.getOption('appendTo'));\n        }\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.unbindContainerMouseleaveListener();\n        this.clearTimeouts();\n        this.container = null;\n        this.scrollHandler = null;\n    }\n    clearShowTimeout() {\n        if (this.showTimeout) {\n            clearTimeout(this.showTimeout);\n            this.showTimeout = null;\n        }\n    }\n    clearHideTimeout() {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n    }\n    clearTimeouts() {\n        this.clearShowTimeout();\n        this.clearHideTimeout();\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n        if (this.container) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Tooltip, deps: [{ token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Tooltip, selector: \"[pTooltip]\", inputs: { tooltipPosition: \"tooltipPosition\", tooltipEvent: \"tooltipEvent\", appendTo: \"appendTo\", positionStyle: \"positionStyle\", tooltipStyleClass: \"tooltipStyleClass\", tooltipZIndex: \"tooltipZIndex\", escape: \"escape\", showDelay: \"showDelay\", hideDelay: \"hideDelay\", life: \"life\", positionTop: \"positionTop\", positionLeft: \"positionLeft\", autoHide: \"autoHide\", fitContent: \"fitContent\", hideOnEscape: \"hideOnEscape\", content: [\"pTooltip\", \"content\"], disabled: [\"tooltipDisabled\", \"disabled\"], tooltipOptions: \"tooltipOptions\" }, host: { listeners: { \"document:keydown.escape\": \"onPressEscape($event)\" }, classAttribute: \"p-element\" }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Tooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pTooltip]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }], propDecorators: { tooltipPosition: [{\n                type: Input\n            }], tooltipEvent: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], positionStyle: [{\n                type: Input\n            }], tooltipStyleClass: [{\n                type: Input\n            }], tooltipZIndex: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }], showDelay: [{\n                type: Input\n            }], hideDelay: [{\n                type: Input\n            }], life: [{\n                type: Input\n            }], positionTop: [{\n                type: Input\n            }], positionLeft: [{\n                type: Input\n            }], autoHide: [{\n                type: Input\n            }], fitContent: [{\n                type: Input\n            }], hideOnEscape: [{\n                type: Input\n            }], content: [{\n                type: Input,\n                args: ['pTooltip']\n            }], disabled: [{\n                type: Input,\n                args: ['tooltipDisabled']\n            }], tooltipOptions: [{\n                type: Input\n            }], onPressEscape: [{\n                type: HostListener,\n                args: ['document:keydown.escape', ['$event']]\n            }] } });\nclass TooltipModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: TooltipModule, declarations: [Tooltip], imports: [CommonModule], exports: [Tooltip] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TooltipModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Tooltip],\n                    declarations: [Tooltip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AACjE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC1G,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAC9D,OAAO,KAAKC,EAAE,MAAM,aAAa;;AAEjC;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,UAAU;EACVC,EAAE;EACFC,IAAI;EACJC,MAAM;EACNC,QAAQ;EACRC,aAAa;EACb;AACJ;AACA;AACA;EACIC,eAAe;EACf;AACJ;AACA;AACA;EACIC,YAAY,GAAG,OAAO;EACtB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,MAAM,GAAG,IAAI;EACb;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,GAAG,EAAE;IACd,IAAI,CAACD,SAAS,GAAGC,GAAG;IACpB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;EACIC,cAAc;EACdC,eAAe,GAAG;IACdC,YAAY,EAAE,IAAI;IAClBtB,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,IAAI;IACvBC,aAAa,EAAE,MAAM;IACrBC,MAAM,EAAE,IAAI;IACZU,QAAQ,EAAE,IAAI;IACdT,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfE,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBF,IAAI,EAAE,IAAI;IACVG,QAAQ,EAAE,IAAI;IACdE,YAAY,EAAE,IAAI;IAClBS,EAAE,EAAEjC,iBAAiB,CAAC,CAAC,GAAG;EAC9B,CAAC;EACD2B,SAAS;EACTO,SAAS;EACTC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,WAAW;EACXC,MAAM;EACNC,kBAAkB;EAClBC,kBAAkB;EAClBC,2BAA2B;EAC3BC,aAAa;EACbC,aAAa;EACbC,YAAY;EACZC,aAAa;EACbC,cAAc;EACdC,WAAWA,CAAC5C,UAAU,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC/D,IAAI,CAACL,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAwC,eAAeA,CAAA,EAAG;IACd,IAAI7D,iBAAiB,CAAC,IAAI,CAACgB,UAAU,CAAC,EAAE;MACpC,IAAI,CAACE,IAAI,CAAC4C,iBAAiB,CAAC,MAAM;QAC9B,IAAI,IAAI,CAACC,SAAS,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;UAC5C,IAAI,CAACX,kBAAkB,GAAG,IAAI,CAACY,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;UACtD,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACa,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC;UACtD,IAAI,CAACV,aAAa,GAAG,IAAI,CAACY,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC;UACjD,IAAI,CAAChD,EAAE,CAACmD,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACjB,kBAAkB,CAAC;UAC7E,IAAI,CAACnC,EAAE,CAACmD,aAAa,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACd,aAAa,CAAC;UACnE,IAAI,CAACtC,EAAE,CAACmD,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAChB,kBAAkB,CAAC;QACjF,CAAC,MACI,IAAI,IAAI,CAACU,SAAS,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;UACjD,IAAI,CAACP,aAAa,GAAG,IAAI,CAACc,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC;UAC5C,IAAI,CAACR,YAAY,GAAG,IAAI,CAACc,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC;UAC1C,IAAIO,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxD,EAAE,CAACmD,aAAa,CAAC;UAClDI,MAAM,CAACH,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACb,aAAa,CAAC;UACpDgB,MAAM,CAACH,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACZ,YAAY,CAAC;QACtD;MACJ,CAAC,CAAC;IACN;EACJ;EACAiB,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAIA,YAAY,CAACrD,eAAe,EAAE;MAC9B,IAAI,CAACsD,SAAS,CAAC;QAAEtD,eAAe,EAAEqD,YAAY,CAACrD,eAAe,CAACuD;MAAa,CAAC,CAAC;IAClF;IACA,IAAIF,YAAY,CAACpD,YAAY,EAAE;MAC3B,IAAI,CAACqD,SAAS,CAAC;QAAErD,YAAY,EAAEoD,YAAY,CAACpD,YAAY,CAACsD;MAAa,CAAC,CAAC;IAC5E;IACA,IAAIF,YAAY,CAACnD,QAAQ,EAAE;MACvB,IAAI,CAACoD,SAAS,CAAC;QAAEpD,QAAQ,EAAEmD,YAAY,CAACnD,QAAQ,CAACqD;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAAClD,aAAa,EAAE;MAC5B,IAAI,CAACmD,SAAS,CAAC;QAAEnD,aAAa,EAAEkD,YAAY,CAAClD,aAAa,CAACoD;MAAa,CAAC,CAAC;IAC9E;IACA,IAAIF,YAAY,CAACjD,iBAAiB,EAAE;MAChC,IAAI,CAACkD,SAAS,CAAC;QAAElD,iBAAiB,EAAEiD,YAAY,CAACjD,iBAAiB,CAACmD;MAAa,CAAC,CAAC;IACtF;IACA,IAAIF,YAAY,CAAChD,aAAa,EAAE;MAC5B,IAAI,CAACiD,SAAS,CAAC;QAAEjD,aAAa,EAAEgD,YAAY,CAAChD,aAAa,CAACkD;MAAa,CAAC,CAAC;IAC9E;IACA,IAAIF,YAAY,CAAC/C,MAAM,EAAE;MACrB,IAAI,CAACgD,SAAS,CAAC;QAAEhD,MAAM,EAAE+C,YAAY,CAAC/C,MAAM,CAACiD;MAAa,CAAC,CAAC;IAChE;IACA,IAAIF,YAAY,CAAC9C,SAAS,EAAE;MACxB,IAAI,CAAC+C,SAAS,CAAC;QAAE/C,SAAS,EAAE8C,YAAY,CAAC9C,SAAS,CAACgD;MAAa,CAAC,CAAC;IACtE;IACA,IAAIF,YAAY,CAAC7C,SAAS,EAAE;MACxB,IAAI,CAAC8C,SAAS,CAAC;QAAE9C,SAAS,EAAE6C,YAAY,CAAC7C,SAAS,CAAC+C;MAAa,CAAC,CAAC;IACtE;IACA,IAAIF,YAAY,CAAC5C,IAAI,EAAE;MACnB,IAAI,CAAC6C,SAAS,CAAC;QAAE7C,IAAI,EAAE4C,YAAY,CAAC5C,IAAI,CAAC8C;MAAa,CAAC,CAAC;IAC5D;IACA,IAAIF,YAAY,CAAC3C,WAAW,EAAE;MAC1B,IAAI,CAAC4C,SAAS,CAAC;QAAE5C,WAAW,EAAE2C,YAAY,CAAC3C,WAAW,CAAC6C;MAAa,CAAC,CAAC;IAC1E;IACA,IAAIF,YAAY,CAAC1C,YAAY,EAAE;MAC3B,IAAI,CAAC2C,SAAS,CAAC;QAAE3C,YAAY,EAAE0C,YAAY,CAAC1C,YAAY,CAAC4C;MAAa,CAAC,CAAC;IAC5E;IACA,IAAIF,YAAY,CAACrC,QAAQ,EAAE;MACvB,IAAI,CAACsC,SAAS,CAAC;QAAEtC,QAAQ,EAAEqC,YAAY,CAACrC,QAAQ,CAACuC;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAACtC,OAAO,EAAE;MACtB,IAAI,CAACuC,SAAS,CAAC;QAAEhC,YAAY,EAAE+B,YAAY,CAACtC,OAAO,CAACwC;MAAa,CAAC,CAAC;MACnE,IAAI,IAAI,CAAC1B,MAAM,EAAE;QACb,IAAIwB,YAAY,CAACtC,OAAO,CAACwC,YAAY,EAAE;UACnC,IAAI,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACA,SAAS,CAACgC,YAAY,EAAE;YAC/C,IAAI,CAACC,UAAU,CAAC,CAAC;YACjB,IAAI,CAACC,KAAK,CAAC,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ;IACJ;IACA,IAAIP,YAAY,CAACzC,QAAQ,EAAE;MACvB,IAAI,CAAC0C,SAAS,CAAC;QAAE1C,QAAQ,EAAEyC,YAAY,CAACzC,QAAQ,CAAC2C;MAAa,CAAC,CAAC;IACpE;IACA,IAAIF,YAAY,CAAC9B,EAAE,EAAE;MACjB,IAAI,CAAC+B,SAAS,CAAC;QAAE/B,EAAE,EAAE8B,YAAY,CAAC9B,EAAE,CAACgC;MAAa,CAAC,CAAC;IACxD;IACA,IAAIF,YAAY,CAACjC,cAAc,EAAE;MAC7B,IAAI,CAACC,eAAe,GAAG;QAAE,GAAG,IAAI,CAACA,eAAe;QAAE,GAAGgC,YAAY,CAACjC,cAAc,CAACmC;MAAa,CAAC;MAC/F,IAAI,CAACpC,UAAU,CAAC,CAAC;MACjB,IAAI,IAAI,CAACU,MAAM,EAAE;QACb,IAAI,IAAI,CAACY,SAAS,CAAC,cAAc,CAAC,EAAE;UAChC,IAAI,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACgC,YAAY,EAAE;YAC/C,IAAI,CAACC,UAAU,CAAC,CAAC;YACjB,IAAI,CAACC,KAAK,CAAC,CAAC;UAChB,CAAC,MACI;YACD,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ;IACJ;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpB,SAAS,CAAC,UAAU,CAAC;EACrC;EACAC,YAAYA,CAACoB,CAAC,EAAE;IACZ,IAAI,CAAC,IAAI,CAACtC,SAAS,IAAI,CAAC,IAAI,CAACG,WAAW,EAAE;MACtC,IAAI,CAACoC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAnB,YAAYA,CAACkB,CAAC,EAAE;IACZ,IAAI,CAAC,IAAI,CAACD,UAAU,CAAC,CAAC,EAAE;MACpB,MAAMG,KAAK,GAAG5E,UAAU,CAAC6E,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,WAAW,CAAC,IAAI9E,UAAU,CAAC6E,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,gBAAgB,CAAC,IAAI9E,UAAU,CAAC6E,QAAQ,CAACH,CAAC,CAACI,aAAa,EAAE,iBAAiB,CAAC;MACpL,CAACF,KAAK,IAAI,IAAI,CAAC7C,UAAU,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACA,UAAU,CAAC,CAAC;IACrB;EACJ;EACA6B,OAAOA,CAACc,CAAC,EAAE;IACP,IAAI,CAACC,QAAQ,CAAC,CAAC;EACnB;EACAd,MAAMA,CAACa,CAAC,EAAE;IACN,IAAI,CAAC3C,UAAU,CAAC,CAAC;EACrB;EACA0B,YAAYA,CAACiB,CAAC,EAAE;IACZ,IAAI,CAAC3C,UAAU,CAAC,CAAC;EACrB;EACAgD,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACrD,YAAY,EAAE;MACnB,IAAI,CAACK,UAAU,CAAC,CAAC;IACrB;EACJ;EACA4C,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACuC,gBAAgB,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC3B,SAAS,CAAC,WAAW,CAAC,EAC3B,IAAI,CAACd,WAAW,GAAG0C,UAAU,CAAC,MAAM;MAChC,IAAI,CAACV,IAAI,CAAC,CAAC;IACf,CAAC,EAAE,IAAI,CAAClB,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,KAEhC,IAAI,CAACkB,IAAI,CAAC,CAAC;IACf,IAAI,IAAI,CAAClB,SAAS,CAAC,MAAM,CAAC,EAAE;MACxB,IAAI6B,QAAQ,GAAG,IAAI,CAAC7B,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC,MAAM,CAAC;MAC1H,IAAI,CAACb,WAAW,GAAGyC,UAAU,CAAC,MAAM;QAChC,IAAI,CAACT,IAAI,CAAC,CAAC;MACf,CAAC,EAAEU,QAAQ,CAAC;IAChB;EACJ;EACAnD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACU,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC0C,gBAAgB,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC9B,SAAS,CAAC,WAAW,CAAC,EAAE;MAC7B,IAAI,CAAC2B,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzB,IAAI,CAACxC,WAAW,GAAGyC,UAAU,CAAC,MAAM;QAChC,IAAI,CAACT,IAAI,CAAC,CAAC;MACf,CAAC,EAAE,IAAI,CAACnB,SAAS,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAACmB,IAAI,CAAC,CAAC;IACf;EACJ;EACAY,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAChD,SAAS,EAAE;MAChB,IAAI,CAAC4C,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACK,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAACjD,SAAS,GAAGkD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACnD,SAAS,CAACoD,YAAY,CAAC,IAAI,EAAE,IAAI,CAACnC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAACjB,SAAS,CAACoD,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;IAC9C,IAAIC,YAAY,GAAGH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAChDE,YAAY,CAACC,SAAS,GAAG,iBAAiB;IAC1C,IAAI,CAACtD,SAAS,CAACuD,WAAW,CAACF,YAAY,CAAC;IACxC,IAAI,CAACnD,WAAW,GAAGgD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAACjD,WAAW,CAACoD,SAAS,GAAG,gBAAgB;IAC7C,IAAI,CAACrB,UAAU,CAAC,CAAC;IACjB,IAAI,IAAI,CAAChB,SAAS,CAAC,eAAe,CAAC,EAAE;MACjC,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACxC,SAAS,CAAC,eAAe,CAAC;IACnE;IACA,IAAI,CAACjB,SAAS,CAACuD,WAAW,CAAC,IAAI,CAACrD,WAAW,CAAC;IAC5C,IAAI,IAAI,CAACe,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,EACrCiC,QAAQ,CAACQ,IAAI,CAACH,WAAW,CAAC,IAAI,CAACvD,SAAS,CAAC,CAAC,KACzC,IAAI,IAAI,CAACiB,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAC5CrD,UAAU,CAAC2F,WAAW,CAAC,IAAI,CAACvD,SAAS,EAAE,IAAI,CAAC7B,EAAE,CAACmD,aAAa,CAAC,CAAC,KAE9D1D,UAAU,CAAC2F,WAAW,CAAC,IAAI,CAACvD,SAAS,EAAE,IAAI,CAACiB,SAAS,CAAC,UAAU,CAAC,CAAC;IACtE,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACG,OAAO,GAAG,cAAc;IAC7C,IAAI,IAAI,CAACtE,UAAU,EAAE;MACjB,IAAI,CAACW,SAAS,CAACwD,KAAK,CAACI,KAAK,GAAG,aAAa;IAC9C;IACA,IAAI,IAAI,CAACvB,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACrC,SAAS,CAACwD,KAAK,CAACK,aAAa,GAAG,MAAM;IAC/C,CAAC,MACI;MACD,IAAI,CAAC7D,SAAS,CAACwD,KAAK,CAACK,aAAa,GAAG,OAAO;MAC5C,IAAI,CAACC,+BAA+B,CAAC,CAAC;IAC1C;EACJ;EACAA,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACtD,2BAA2B,EAAE;MACnC,MAAMuD,QAAQ,GAAG,IAAI,CAAC/D,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsB,aAAa;MAC/D,IAAI,CAACd,2BAA2B,GAAG,IAAI,CAAClC,QAAQ,CAAC0F,MAAM,CAACD,QAAQ,EAAE,YAAY,EAAGzB,CAAC,IAAK;QACnF,IAAI,CAAC3C,UAAU,CAAC,CAAC;MACrB,CAAC,CAAC;IACN;EACJ;EACAsE,iCAAiCA,CAAA,EAAG;IAChC,IAAI,IAAI,CAACzD,2BAA2B,EAAE;MAClC,IAAI,CAACsD,+BAA+B,CAAC,CAAC;MACtC,IAAI,CAACtD,2BAA2B,GAAG,IAAI;IAC3C;EACJ;EACA2B,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAClB,SAAS,CAAC,cAAc,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,EAAE;MAC/D;IACJ;IACA,IAAI,CAAC+B,MAAM,CAAC,CAAC;IACb,IAAI,CAACd,KAAK,CAAC,CAAC;IACZtE,UAAU,CAACsG,MAAM,CAAC,IAAI,CAAClE,SAAS,EAAE,GAAG,CAAC;IACtC,IAAI,IAAI,CAACiB,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM,EAC1ClD,WAAW,CAACoG,GAAG,CAAC,SAAS,EAAE,IAAI,CAACnE,SAAS,EAAE,IAAI,CAAC3B,MAAM,CAAC+F,MAAM,CAACC,OAAO,CAAC,CAAC,KAEvE,IAAI,CAACrE,SAAS,CAACwD,KAAK,CAACY,MAAM,GAAG,IAAI,CAACnD,SAAS,CAAC,eAAe,CAAC;IACjE,IAAI,CAACqD,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC7B;EACAnC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACnB,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;MAC5ClD,WAAW,CAACyG,KAAK,CAAC,IAAI,CAACxE,SAAS,CAAC;IACrC;IACA,IAAI,CAACiD,MAAM,CAAC,CAAC;EACjB;EACAhB,UAAUA,CAAA,EAAG;IACT,MAAM1C,OAAO,GAAG,IAAI,CAAC0B,SAAS,CAAC,cAAc,CAAC;IAC9C,IAAI1B,OAAO,YAAYlC,WAAW,EAAE;MAChC,MAAMoH,eAAe,GAAG,IAAI,CAAClG,aAAa,CAACmG,kBAAkB,CAACnF,OAAO,CAAC;MACtEkF,eAAe,CAACE,aAAa,CAAC,CAAC;MAC/BF,eAAe,CAACG,SAAS,CAACC,OAAO,CAAEC,IAAI,IAAK,IAAI,CAAC5E,WAAW,CAACqD,WAAW,CAACuB,IAAI,CAAC,CAAC;IACnF,CAAC,MACI,IAAI,IAAI,CAAC7D,SAAS,CAAC,QAAQ,CAAC,EAAE;MAC/B,IAAI,CAACf,WAAW,CAAC6E,SAAS,GAAG,EAAE;MAC/B,IAAI,CAAC7E,WAAW,CAACqD,WAAW,CAACL,QAAQ,CAAC8B,cAAc,CAACzF,OAAO,CAAC,CAAC;IAClE,CAAC,MACI;MACD,IAAI,CAACW,WAAW,CAAC6E,SAAS,GAAGxF,OAAO;IACxC;EACJ;EACA2C,KAAKA,CAAA,EAAG;IACJ,IAAIuB,QAAQ,GAAG,IAAI,CAACxC,SAAS,CAAC,iBAAiB,CAAC;IAChD,QAAQwC,QAAQ;MACZ,KAAK,KAAK;QACN,IAAI,CAACwB,QAAQ,CAAC,CAAC;QACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACC,WAAW,CAAC,CAAC;UAClB,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACE,UAAU,CAAC,CAAC;YACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACG,SAAS,CAAC,CAAC;YACpB;UACJ;QACJ;QACA;MACJ,KAAK,QAAQ;QACT,IAAI,CAACF,WAAW,CAAC,CAAC;QAClB,IAAI,IAAI,CAACD,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;UACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACE,UAAU,CAAC,CAAC;YACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACG,SAAS,CAAC,CAAC;YACpB;UACJ;QACJ;QACA;MACJ,KAAK,MAAM;QACP,IAAI,CAACA,SAAS,CAAC,CAAC;QAChB,IAAI,IAAI,CAACH,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACE,UAAU,CAAC,CAAC;UACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;YACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACC,WAAW,CAAC,CAAC;YACtB;UACJ;QACJ;QACA;MACJ,KAAK,OAAO;QACR,IAAI,CAACC,UAAU,CAAC,CAAC;QACjB,IAAI,IAAI,CAACF,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACG,SAAS,CAAC,CAAC;UAChB,IAAI,IAAI,CAACH,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACD,QAAQ,CAAC,CAAC;YACf,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;cACtB,IAAI,CAACC,WAAW,CAAC,CAAC;YACtB;UACJ;QACJ;QACA;IACR;EACJ;EACAG,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACrE,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;MAClF,IAAIsE,MAAM,GAAG,IAAI,CAACpH,EAAE,CAACmD,aAAa,CAACkE,qBAAqB,CAAC,CAAC;MAC1D,IAAIC,UAAU,GAAGF,MAAM,CAACG,IAAI,GAAG9H,UAAU,CAAC+H,mBAAmB,CAAC,CAAC;MAC/D,IAAIC,SAAS,GAAGL,MAAM,CAACM,GAAG,GAAGjI,UAAU,CAACkI,kBAAkB,CAAC,CAAC;MAC5D,OAAO;QAAEJ,IAAI,EAAED,UAAU;QAAEI,GAAG,EAAED;MAAU,CAAC;IAC/C,CAAC,MACI;MACD,OAAO;QAAEF,IAAI,EAAE,CAAC;QAAEG,GAAG,EAAE;MAAE,CAAC;IAC9B;EACJ;EACAT,UAAUA,CAAA,EAAG;IACT,IAAI,CAACW,QAAQ,CAAC,OAAO,CAAC;IACtB,IAAIC,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG9H,UAAU,CAACqI,aAAa,CAAC,IAAI,CAAC9H,EAAE,CAACmD,aAAa,CAAC;IAC5E,IAAIuE,GAAG,GAAGG,UAAU,CAACH,GAAG,GAAG,CAACjI,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAC/H,EAAE,CAACmD,aAAa,CAAC,GAAG1D,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAI,CAACA,SAAS,CAACwD,KAAK,CAACkC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAACzE,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACqC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAC5E,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAoE,SAASA,CAAA,EAAG;IACR,IAAI,CAACU,QAAQ,CAAC,MAAM,CAAC;IACrB,IAAIC,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG9H,UAAU,CAACqI,aAAa,CAAC,IAAI,CAACjG,SAAS,CAAC;IACrE,IAAI6F,GAAG,GAAGG,UAAU,CAACH,GAAG,GAAG,CAACjI,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAC/H,EAAE,CAACmD,aAAa,CAAC,GAAG1D,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAClG,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAI,CAACA,SAAS,CAACwD,KAAK,CAACkC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAACzE,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACqC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAC5E,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAgE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACc,QAAQ,CAAC,KAAK,CAAC;IACpB,IAAIC,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG,CAAC9H,UAAU,CAACqI,aAAa,CAAC,IAAI,CAAC9H,EAAE,CAACmD,aAAa,CAAC,GAAG1D,UAAU,CAACqI,aAAa,CAAC,IAAI,CAACjG,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAI6F,GAAG,GAAGG,UAAU,CAACH,GAAG,GAAGjI,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAClG,SAAS,CAAC;IACpE,IAAI,CAACA,SAAS,CAACwD,KAAK,CAACkC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAACzE,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACqC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAC5E,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAkE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,QAAQ,CAAC,QAAQ,CAAC;IACvB,IAAIC,UAAU,GAAG,IAAI,CAACV,aAAa,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG,CAAC9H,UAAU,CAACqI,aAAa,CAAC,IAAI,CAAC9H,EAAE,CAACmD,aAAa,CAAC,GAAG1D,UAAU,CAACqI,aAAa,CAAC,IAAI,CAACjG,SAAS,CAAC,IAAI,CAAC;IAC7H,IAAI6F,GAAG,GAAGG,UAAU,CAACH,GAAG,GAAGjI,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAC/H,EAAE,CAACmD,aAAa,CAAC;IAC3E,IAAI,CAACtB,SAAS,CAACwD,KAAK,CAACkC,IAAI,GAAGA,IAAI,GAAG,IAAI,CAACzE,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI;IACxE,IAAI,CAACjB,SAAS,CAACwD,KAAK,CAACqC,GAAG,GAAGA,GAAG,GAAG,IAAI,CAAC5E,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;EACzE;EACAa,SAASA,CAACqE,MAAM,EAAE;IACd,IAAI,CAACtG,eAAe,GAAG;MAAE,GAAG,IAAI,CAACA,eAAe;MAAE,GAAGsG;IAAO,CAAC;EACjE;EACAlF,SAASA,CAACkF,MAAM,EAAE;IACd,OAAO,IAAI,CAACtG,eAAe,CAACsG,MAAM,CAAC;EACvC;EACAxE,SAASA,CAACxD,EAAE,EAAE;IACV,OAAOP,UAAU,CAAC6E,QAAQ,CAACtE,EAAE,EAAE,gBAAgB,CAAC,GAAGP,UAAU,CAACwI,UAAU,CAACjI,EAAE,EAAE,OAAO,CAAC,GAAGA,EAAE;EAC9F;EACA4H,QAAQA,CAACtC,QAAQ,EAAE;IACf,IAAI,CAACzD,SAAS,CAACwD,KAAK,CAACkC,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI;IACvC,IAAI,CAAC1F,SAAS,CAACwD,KAAK,CAACqC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI;IACtC,IAAIQ,gBAAgB,GAAG,kCAAkC,GAAG5C,QAAQ;IACpE,IAAI,CAACzD,SAAS,CAACsD,SAAS,GAAG,IAAI,CAACrC,SAAS,CAAC,mBAAmB,CAAC,GAAGoF,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAACpF,SAAS,CAAC,mBAAmB,CAAC,GAAGoF,gBAAgB;EACpJ;EACAnB,aAAaA,CAAA,EAAG;IACZ,IAAIK,MAAM,GAAG,IAAI,CAACvF,SAAS,CAACwF,qBAAqB,CAAC,CAAC;IACnD,IAAII,SAAS,GAAGL,MAAM,CAACM,GAAG;IAC1B,IAAIJ,UAAU,GAAGF,MAAM,CAACG,IAAI;IAC5B,IAAI9B,KAAK,GAAGhG,UAAU,CAACqI,aAAa,CAAC,IAAI,CAACjG,SAAS,CAAC;IACpD,IAAIsG,MAAM,GAAG1I,UAAU,CAACsI,cAAc,CAAC,IAAI,CAAClG,SAAS,CAAC;IACtD,IAAIuG,QAAQ,GAAG3I,UAAU,CAAC4I,WAAW,CAAC,CAAC;IACvC,OAAOf,UAAU,GAAG7B,KAAK,GAAG2C,QAAQ,CAAC3C,KAAK,IAAI6B,UAAU,GAAG,CAAC,IAAIG,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGU,MAAM,GAAGC,QAAQ,CAACD,MAAM;EACzH;EACAG,cAAcA,CAACnE,CAAC,EAAE;IACd,IAAI,CAACF,IAAI,CAAC,CAAC;EACf;EACAkC,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAClG,IAAI,CAAC4C,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACH,cAAc,GAAG,IAAI,CAAC4F,cAAc,CAACtF,IAAI,CAAC,IAAI,CAAC;MACpDuF,MAAM,CAACnF,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACV,cAAc,CAAC;IAC1D,CAAC,CAAC;EACN;EACA8F,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAC9F,cAAc,EAAE;MACrB6F,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC/F,cAAc,CAAC;MACzD,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACA0D,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC3D,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAI/C,6BAA6B,CAAC,IAAI,CAACM,EAAE,CAACmD,aAAa,EAAE,MAAM;QAChF,IAAI,IAAI,CAACtB,SAAS,EAAE;UAChB,IAAI,CAACoC,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACxB,aAAa,CAAC2D,kBAAkB,CAAC,CAAC;EAC3C;EACAsC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACjG,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACiG,oBAAoB,CAAC,CAAC;IAC7C;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC7F,SAAS,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;MAC5C,IAAI,CAAC9C,EAAE,CAACmD,aAAa,CAACsF,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACtG,kBAAkB,CAAC;MAChF,IAAI,CAACnC,EAAE,CAACmD,aAAa,CAACsF,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACrG,kBAAkB,CAAC;MAChF,IAAI,CAACpC,EAAE,CAACmD,aAAa,CAACsF,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACnG,aAAa,CAAC;IAC1E,CAAC,MACI,IAAI,IAAI,CAACQ,SAAS,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;MACjD,IAAIS,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxD,EAAE,CAACmD,aAAa,CAAC;MAClDI,MAAM,CAACkF,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAClG,aAAa,CAAC;MACvDgB,MAAM,CAACkF,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACjG,YAAY,CAAC;IACzD;IACA,IAAI,CAACgG,4BAA4B,CAAC,CAAC;EACvC;EACA1D,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACjD,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC+G,aAAa,EAAE;MAChD,IAAI,IAAI,CAAC9F,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,EACrCiC,QAAQ,CAACQ,IAAI,CAACsD,WAAW,CAAC,IAAI,CAAChH,SAAS,CAAC,CAAC,KACzC,IAAI,IAAI,CAACiB,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAC5C,IAAI,CAAC9C,EAAE,CAACmD,aAAa,CAAC0F,WAAW,CAAC,IAAI,CAAChH,SAAS,CAAC,CAAC,KAElDpC,UAAU,CAACoJ,WAAW,CAAC,IAAI,CAAChH,SAAS,EAAE,IAAI,CAACiB,SAAS,CAAC,UAAU,CAAC,CAAC;IAC1E;IACA,IAAI,CAAC0F,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC5C,iCAAiC,CAAC,CAAC;IACxC,IAAI,CAACgD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACjH,SAAS,GAAG,IAAI;IACrB,IAAI,CAACY,aAAa,GAAG,IAAI;EAC7B;EACAmC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC5C,WAAW,EAAE;MAClB+G,YAAY,CAAC,IAAI,CAAC/G,WAAW,CAAC;MAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACAyC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACxC,WAAW,EAAE;MAClB8G,YAAY,CAAC,IAAI,CAAC9G,WAAW,CAAC;MAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA6G,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAClE,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACH,gBAAgB,CAAC,CAAC;EAC3B;EACAuE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAAC9G,SAAS,EAAE;MAChBjC,WAAW,CAACyG,KAAK,CAAC,IAAI,CAACxE,SAAS,CAAC;IACrC;IACA,IAAI,CAACiD,MAAM,CAAC,CAAC;IACb,IAAI,IAAI,CAACrC,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACwG,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACxG,aAAa,GAAG,IAAI;IAC7B;EACJ;EACA,OAAOyG,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFtJ,OAAO,EAAjBb,EAAE,CAAAoK,iBAAA,CAAiClK,WAAW,GAA9CF,EAAE,CAAAoK,iBAAA,CAAyDpK,EAAE,CAACqK,UAAU,GAAxErK,EAAE,CAAAoK,iBAAA,CAAmFpK,EAAE,CAACsK,MAAM,GAA9FtK,EAAE,CAAAoK,iBAAA,CAAyGxJ,EAAE,CAAC2J,aAAa,GAA3HvK,EAAE,CAAAoK,iBAAA,CAAsIpK,EAAE,CAACwK,SAAS,GAApJxK,EAAE,CAAAoK,iBAAA,CAA+JpK,EAAE,CAACyK,gBAAgB;EAAA;EAC7Q,OAAOC,IAAI,kBAD8E1K,EAAE,CAAA2K,iBAAA;IAAAC,IAAA,EACJ/J,OAAO;IAAAgK,SAAA;IAAAC,SAAA;IAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADLjL,EAAE,CAAAmL,UAAA,4BAAAC,0CAAAC,MAAA;UAAA,OACJH,GAAA,CAAA3F,aAAA,CAAA8F,MAAoB,CAAC;QAAA,UADnBrL,EAAE,CAAAsL,iBAAA;MAAA;IAAA;IAAAC,MAAA;MAAAnK,eAAA;MAAAC,YAAA;MAAAC,QAAA;MAAAC,aAAA;MAAAC,iBAAA;MAAAC,aAAA;MAAAC,MAAA;MAAAC,SAAA;MAAAC,SAAA;MAAAC,IAAA;MAAAC,WAAA;MAAAC,YAAA;MAAAC,QAAA;MAAAC,UAAA;MAAAC,YAAA;MAAAC,OAAA;MAAAC,QAAA;MAAAI,cAAA;IAAA;IAAAgJ,QAAA,GAAFxL,EAAE,CAAAyL,oBAAA;EAAA;AAE/F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F1L,EAAE,CAAA2L,iBAAA,CAGJ9K,OAAO,EAAc,CAAC;IACrG+J,IAAI,EAAEzK,SAAS;IACfyL,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnB,IAAI,EAAEoB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CrB,IAAI,EAAExK,MAAM;MACZwL,IAAI,EAAE,CAAC1L,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE0K,IAAI,EAAE5K,EAAE,CAACqK;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAE5K,EAAE,CAACsK;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAEhK,EAAE,CAAC2J;EAAc,CAAC,EAAE;IAAEK,IAAI,EAAE5K,EAAE,CAACwK;EAAU,CAAC,EAAE;IAAEI,IAAI,EAAE5K,EAAE,CAACyK;EAAiB,CAAC,CAAC,EAAkB;IAAErJ,eAAe,EAAE,CAAC;MAC5KwJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEgB,YAAY,EAAE,CAAC;MACfuJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEiB,QAAQ,EAAE,CAAC;MACXsJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEkB,aAAa,EAAE,CAAC;MAChBqJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEmB,iBAAiB,EAAE,CAAC;MACpBoJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEoB,aAAa,EAAE,CAAC;MAChBmJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEqB,MAAM,EAAE,CAAC;MACTkJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEsB,SAAS,EAAE,CAAC;MACZiJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEuB,SAAS,EAAE,CAAC;MACZgJ,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEwB,IAAI,EAAE,CAAC;MACP+I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEyB,WAAW,EAAE,CAAC;MACd8I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE0B,YAAY,EAAE,CAAC;MACf6I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE2B,QAAQ,EAAE,CAAC;MACX4I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE4B,UAAU,EAAE,CAAC;MACb2I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE6B,YAAY,EAAE,CAAC;MACf0I,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAE8B,OAAO,EAAE,CAAC;MACVyI,IAAI,EAAEvK,KAAK;MACXuL,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAExJ,QAAQ,EAAE,CAAC;MACXwI,IAAI,EAAEvK,KAAK;MACXuL,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEpJ,cAAc,EAAE,CAAC;MACjBoI,IAAI,EAAEvK;IACV,CAAC,CAAC;IAAEkF,aAAa,EAAE,CAAC;MAChBqF,IAAI,EAAEtK,YAAY;MAClBsL,IAAI,EAAE,CAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC;IAChD,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMM,aAAa,CAAC;EAChB,OAAOjC,IAAI,YAAAkC,sBAAAhC,CAAA;IAAA,YAAAA,CAAA,IAAwF+B,aAAa;EAAA;EAChH,OAAOE,IAAI,kBA1D8EpM,EAAE,CAAAqM,gBAAA;IAAAzB,IAAA,EA0DSsB;EAAa;EACjH,OAAOI,IAAI,kBA3D8EtM,EAAE,CAAAuM,gBAAA;IAAAC,OAAA,GA2DkCzM,YAAY;EAAA;AAC7I;AACA;EAAA,QAAA2L,SAAA,oBAAAA,SAAA,KA7D6F1L,EAAE,CAAA2L,iBAAA,CA6DJO,aAAa,EAAc,CAAC;IAC3GtB,IAAI,EAAErK,QAAQ;IACdqL,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAACzM,YAAY,CAAC;MACvB0M,OAAO,EAAE,CAAC5L,OAAO,CAAC;MAClB6L,YAAY,EAAE,CAAC7L,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,OAAO,EAAEqL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}