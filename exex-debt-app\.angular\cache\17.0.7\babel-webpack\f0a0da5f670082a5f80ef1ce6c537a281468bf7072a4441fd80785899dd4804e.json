{"ast": null, "code": "import { HttpErrorResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class OfflineModeInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    // Check if the device is offline\n    if (!navigator.onLine) {\n      // Handle offline mode (e.g., store requests for later)\n      console.error('Device is offline. Request not sent:', request.url);\n      return throwError(new HttpErrorResponse({\n        status: 0,\n        statusText: 'Offline'\n      }));\n    }\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function OfflineModeInterceptor_Factory(t) {\n    return new (t || OfflineModeInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OfflineModeInterceptor,\n    factory: OfflineModeInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpErrorResponse", "throwError", "OfflineModeInterceptor", "constructor", "intercept", "request", "next", "navigator", "onLine", "console", "error", "url", "status", "statusText", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\offline-mode.interceptor.ts"], "sourcesContent": ["import {\n  HttpErrorResponse,\n  HttpHandler,\n  HttpInterceptor,\n  HttpRequest,\n} from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { throwError } from 'rxjs';\n\n@Injectable()\nexport class OfflineModeInterceptor implements HttpInterceptor {\n  constructor() {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler) {\n    // Check if the device is offline\n    if (!navigator.onLine) {\n      // Handle offline mode (e.g., store requests for later)\n      console.error('Device is offline. Request not sent:', request.url);\n      return throwError(new HttpErrorResponse({ status: 0, statusText: 'Offline' }));\n    }\n\n    return next.handle(request);\n  }\n}"], "mappings": "AAAA,SACEA,iBAAiB,QAIZ,sBAAsB;AAE7B,SAASC,UAAU,QAAQ,MAAM;;AAGjC,OAAM,MAAOC,sBAAsB;EACjCC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE;MACrB;MACAC,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEL,OAAO,CAACM,GAAG,CAAC;MAClE,OAAOV,UAAU,CAAC,IAAID,iBAAiB,CAAC;QAAEY,MAAM,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAS,CAAE,CAAC,CAAC;;IAGhF,OAAOP,IAAI,CAACQ,MAAM,CAACT,OAAO,CAAC;EAC7B;EAAC,QAAAU,CAAA,G;qBAZUb,sBAAsB;EAAA;EAAA,QAAAc,EAAA,G;WAAtBd,sBAAsB;IAAAe,OAAA,EAAtBf,sBAAsB,CAAAgB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}