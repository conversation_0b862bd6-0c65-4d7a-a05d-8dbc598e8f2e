{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/avatar\";\nconst _c0 = () => ({\n  \"background-color\": \"#ece9fc\",\n  color: \"#2a1261\"\n});\nexport class UserComponent {\n  constructor() {\n    this.http = inject(HttpClient);\n  }\n  ngOnInit() {\n    this.http.get('https://jsonplaceholder.typicode.com/users').subscribe(res => {\n      console.log(res);\n      this.user = res[0];\n      console.log(this.user);\n    });\n  }\n  static #_ = this.ɵfac = function UserComponent_Factory(t) {\n    return new (t || UserComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserComponent,\n    selectors: [[\"app-user\"]],\n    decls: 42,\n    vars: 14,\n    consts: [[1, \"flex\", \"items-center\", \"gap-3\", \"mb-3\"], [\"icon\", \"pi pi-user\", \"image\", \"assets/user-avatar.png\", \"shape\", \"circle\", \"size\", \"xlarge\"], [1, \"align-content-center\"], [1, \"text-lg\", \"font-bold\", \"m-0\"], [1, \"text-gray-500\"], [1, \"grid-items\"], [\"target\", \"_blank\", 3, \"href\"]],\n    template: function UserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"p-avatar\", 1);\n        i0.ɵɵelementStart(2, \"section\", 2)(3, \"h3\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"p\")(9, \"strong\");\n        i0.ɵɵtext(10, \"Phone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n        i0.ɵɵtext(14, \"Website:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(15, \"\\u00A0\");\n        i0.ɵɵelementStart(16, \"a\", 6);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"p\")(19, \"strong\");\n        i0.ɵɵtext(20, \"Company:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"p\")(23, \"strong\");\n        i0.ɵɵtext(24, \"Catchphrase:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"p\")(27, \"strong\");\n        i0.ɵɵtext(28, \"Business:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"p\")(31, \"strong\");\n        i0.ɵɵtext(32, \"Street:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"p\")(35, \"strong\");\n        i0.ɵɵtext(36, \"City:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"p\")(39, \"strong\");\n        i0.ɵɵtext(40, \"Zipcode:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(41);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(13, _c0));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.user.name);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.user.email);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.phone, \"\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵpropertyInterpolate1(\"href\", \"https://\", ctx.user.website, \"\", i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate(ctx.user.website);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.company.name, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.company.catchPhrase, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.company.bs, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.address.street, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.address.city, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.address.zipcode, \"\");\n      }\n    },\n    dependencies: [i1.Avatar],\n    styles: [\".grid-items[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n}\\n@media (max-width: 739px) {\\n  .grid-items[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(1, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2Rhc2hib2FyZC91c2VyL3VzZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EscUNBQUE7QUFDSjtBQUNJO0VBSko7SUFLUSxxQ0FBQTtFQUVOO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuZ3JpZC1pdGVtcyB7XHJcbiAgICBkaXNwbGF5OiBncmlkO1xyXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTtcclxuXHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzM5cHgpIHtcclxuICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxLCAxZnIpO1xyXG4gICAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["HttpClient", "inject", "UserComponent", "constructor", "http", "ngOnInit", "get", "subscribe", "res", "console", "log", "user", "_", "_2", "selectors", "decls", "vars", "consts", "template", "UserComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate", "name", "email", "ɵɵtextInterpolate1", "phone", "ɵɵpropertyInterpolate1", "website", "ɵɵsanitizeUrl", "company", "catchPhrase", "bs", "address", "street", "city", "zipcode"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, inject, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'app-user',\r\n    templateUrl: './user.component.html',\r\n    styleUrls: ['./user.component.scss'],\r\n})\r\nexport class UserComponent implements OnInit {\r\n    http = inject(HttpClient);\r\n    user: any;\r\n    constructor() {}\r\n\r\n    ngOnInit(): void {\r\n        this.http.get('https://jsonplaceholder.typicode.com/users').subscribe((res) => {\r\n            console.log(res);\r\n            this.user = res[0];\r\n            console.log(this.user);\r\n        });\r\n    }\r\n}\r\n", "<div class=\"flex items-center gap-3 mb-3\">\r\n    <p-avatar\r\n        icon=\"pi pi-user\"\r\n        image=\"assets/user-avatar.png\"\r\n        shape=\"circle\"\r\n        size=\"xlarge\"\r\n        [style]=\"{ 'background-color': '#ece9fc', color: '#2a1261' }\"></p-avatar>\r\n    <section class=\"align-content-center\">\r\n        <h3 class=\"text-lg font-bold m-0\">{{ user.name }}</h3>\r\n        <p class=\"text-gray-500\">{{ user.email }}</p>\r\n    </section>\r\n</div>\r\n\r\n<div class=\"grid-items\">\r\n    <p><strong>Phone:</strong> {{ user.phone }}</p>\r\n    <p>\r\n        <strong>Website:</strong>&nbsp;<a href=\"https://{{ user.website }}\" target=\"_blank\">{{ user.website }}</a>\r\n    </p>\r\n    <p><strong>Company:</strong> {{ user.company.name }}</p>\r\n    <p><strong>Catchphrase:</strong> {{ user.company.catchPhrase }}</p>\r\n    <p><strong>Business:</strong> {{ user.company.bs }}</p>\r\n\r\n    <p><strong>Street:</strong> {{ user.address.street }}</p>\r\n    <p><strong>City:</strong> {{ user.address.city }}</p>\r\n    <p><strong>Zipcode:</strong> {{ user.address.zipcode }}</p>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAAoBC,MAAM,QAAgB,eAAe;;;;;;;AAOzD,OAAM,MAAOC,aAAa;EAGtBC,YAAA;IAFA,KAAAC,IAAI,GAAGH,MAAM,CAACD,UAAU,CAAC;EAEV;EAEfK,QAAQA,CAAA;IACJ,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,4CAA4C,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;MAC1EC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAChB,IAAI,CAACG,IAAI,GAAGH,GAAG,CAAC,CAAC,CAAC;MAClBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,IAAI,CAAC;IAC1B,CAAC,CAAC;EACN;EAAC,QAAAC,CAAA,G;qBAXQV,aAAa;EAAA;EAAA,QAAAW,EAAA,G;UAAbX,aAAa;IAAAY,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR1BE,EAAA,CAAAC,cAAA,aAA0C;QACtCD,EAAA,CAAAE,SAAA,kBAK6E;QAC7EF,EAAA,CAAAC,cAAA,iBAAsC;QACAD,EAAA,CAAAG,MAAA,GAAe;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACtDJ,EAAA,CAAAC,cAAA,WAAyB;QAAAD,EAAA,CAAAG,MAAA,GAAgB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAIrDJ,EAAA,CAAAC,cAAA,aAAwB;QACTD,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAAgB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAC/CJ,EAAA,CAAAC,cAAA,SAAG;QACSD,EAAA,CAAAG,MAAA,gBAAQ;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAAAJ,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAC,cAAA,YAAqD;QAAAD,EAAA,CAAAG,MAAA,IAAkB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAE9GJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,gBAAQ;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAAuB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QACxDJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,oBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAA8B;QAAAH,EAAA,CAAAI,YAAA,EAAI;QACnEJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,iBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAAqB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAEvDJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,eAAO;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAAyB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QACzDJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,aAAK;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAAuB;QAAAH,EAAA,CAAAI,YAAA,EAAI;QACrDJ,EAAA,CAAAC,cAAA,SAAG;QAAQD,EAAA,CAAAG,MAAA,gBAAQ;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAACJ,EAAA,CAAAG,MAAA,IAA0B;QAAAH,EAAA,CAAAI,YAAA,EAAI;;;QAlBvDJ,EAAA,CAAAK,SAAA,GAA6D;QAA7DL,EAAA,CAAAM,UAAA,CAAAN,EAAA,CAAAO,eAAA,KAAAC,GAAA,EAA6D;QAE3BR,EAAA,CAAAK,SAAA,GAAe;QAAfL,EAAA,CAAAS,iBAAA,CAAAV,GAAA,CAAAV,IAAA,CAAAqB,IAAA,CAAe;QACxBV,EAAA,CAAAK,SAAA,GAAgB;QAAhBL,EAAA,CAAAS,iBAAA,CAAAV,GAAA,CAAAV,IAAA,CAAAsB,KAAA,CAAgB;QAKlBX,EAAA,CAAAK,SAAA,GAAgB;QAAhBL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAAwB,KAAA,KAAgB;QAELb,EAAA,CAAAK,SAAA,GAAiC;QAAjCL,EAAA,CAAAc,sBAAA,qBAAAf,GAAA,CAAAV,IAAA,CAAA0B,OAAA,MAAAf,EAAA,CAAAgB,aAAA,CAAiC;QAAiBhB,EAAA,CAAAK,SAAA,GAAkB;QAAlBL,EAAA,CAAAS,iBAAA,CAAAV,GAAA,CAAAV,IAAA,CAAA0B,OAAA,CAAkB;QAE7Ef,EAAA,CAAAK,SAAA,GAAuB;QAAvBL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA4B,OAAA,CAAAP,IAAA,KAAuB;QACnBV,EAAA,CAAAK,SAAA,GAA8B;QAA9BL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA4B,OAAA,CAAAC,WAAA,KAA8B;QACjClB,EAAA,CAAAK,SAAA,GAAqB;QAArBL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA4B,OAAA,CAAAE,EAAA,KAAqB;QAEvBnB,EAAA,CAAAK,SAAA,GAAyB;QAAzBL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA+B,OAAA,CAAAC,MAAA,KAAyB;QAC3BrB,EAAA,CAAAK,SAAA,GAAuB;QAAvBL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA+B,OAAA,CAAAE,IAAA,KAAuB;QACpBtB,EAAA,CAAAK,SAAA,GAA0B;QAA1BL,EAAA,CAAAY,kBAAA,MAAAb,GAAA,CAAAV,IAAA,CAAA+B,OAAA,CAAAG,OAAA,KAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}