{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-spinner\";\nexport class LoadingInterceptor {\n  constructor(spinner) {\n    this.spinner = spinner;\n  }\n  intercept(request, next) {\n    this.spinner.show();\n    return next.handle(request).pipe(finalize(() => {\n      this.spinner.hide();\n    }));\n  }\n  static #_ = this.ɵfac = function LoadingInterceptor_Factory(t) {\n    return new (t || LoadingInterceptor)(i0.ɵɵinject(i1.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoadingInterceptor,\n    factory: LoadingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["finalize", "LoadingInterceptor", "constructor", "spinner", "intercept", "request", "next", "show", "handle", "pipe", "hide", "_", "i0", "ɵɵinject", "i1", "NgxSpinnerService", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\loading.interceptor.ts"], "sourcesContent": ["import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\nimport { finalize } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class LoadingInterceptor implements HttpInterceptor {\r\n    constructor(private spinner: NgxSpinnerService) {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        this.spinner.show();\r\n        return next.handle(request).pipe(\r\n            finalize(() => {\r\n                this.spinner.hide();\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAGA,SAASA,QAAQ,QAAQ,gBAAgB;;;AAGzC,OAAM,MAAOC,kBAAkB;EAC3BC,YAAoBC,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;EAAsB;EAEjDC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,IAAI,CAACH,OAAO,CAACI,IAAI,EAAE;IACnB,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAC5BT,QAAQ,CAAC,MAAK;MACV,IAAI,CAACG,OAAO,CAACO,IAAI,EAAE;IACvB,CAAC,CAAC,CACL;EACL;EAAC,QAAAC,CAAA,G;qBAVQV,kBAAkB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAlBf,kBAAkB;IAAAgB,OAAA,EAAlBhB,kBAAkB,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}