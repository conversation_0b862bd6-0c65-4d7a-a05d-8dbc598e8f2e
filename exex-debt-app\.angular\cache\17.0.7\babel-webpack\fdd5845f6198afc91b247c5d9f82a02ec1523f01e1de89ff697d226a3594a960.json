{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\nimport { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Path } from '../enums/path.enum';\nexport const authGuard = (route, state) => {\n  const isAuth = inject(AuthService).isAuthenticated();\n  const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\n  return isAuth ? true : redirectToLogin;\n};", "map": {"version": 3, "names": ["AuthService", "inject", "Router", "Path", "<PERSON>th<PERSON><PERSON>", "route", "state", "isAuth", "isAuthenticated", "redirectToLogin", "createUrlTree", "AUTH_LOGIN"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\nimport { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { Path } from '../enums/path.enum';\r\n\r\nexport const authGuard: CanActivateFn = (route, state) => {\r\n    const isAuth = inject(AuthService).isAuthenticated();\r\n    const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\r\n    return isAuth ? true : redirectToLogin;\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,IAAI,QAAQ,oBAAoB;AAEzC,OAAO,MAAMC,SAAS,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACrD,MAAMC,MAAM,GAAGN,MAAM,CAACD,WAAW,CAAC,CAACQ,eAAe,EAAE;EACpD,MAAMC,eAAe,GAAGR,MAAM,CAACC,MAAM,CAAC,CAACQ,aAAa,CAAC,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAC;EACvE,OAAOJ,MAAM,GAAG,IAAI,GAAGE,eAAe;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}