{"ast": null, "code": "import { inject } from '@angular/core';\nimport { catchError } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport { AuthService } from '../service/auth.service';\nimport * as i0 from \"@angular/core\";\nexport class ErrorInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      // Handle and log the error here\n      console.error('HTTP Error:', error);\n      // Optionally rethrow the error to propagate it\n      // if (error) {\n      //     if (error.status === 401 || error.status === 403) {\n      //         this.authService.logout();\n      //     }\n      //     if (error.status === 429) {\n      //         alert(\n      //             'Something went wrong, please try again after a few seconds!'\n      //         );\n      //     }\n      // }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["inject", "catchError", "throwError", "AuthService", "ErrorInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "console", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\error.interceptor.ts"], "sourcesContent": ["import { inject, Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\nimport { AuthService } from '../service/auth.service';\r\n\r\n@Injectable()\r\nexport class ErrorInterceptor implements HttpInterceptor {\r\n    authService = inject(AuthService);\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler) {\r\n        return next.handle(request).pipe(\r\n            catchError((error: HttpErrorResponse) => {\r\n                // Handle and log the error here\r\n                console.error('HTTP Error:', error);\r\n                // Optionally rethrow the error to propagate it\r\n\r\n                // if (error) {\r\n                //     if (error.status === 401 || error.status === 403) {\r\n                //         this.authService.logout();\r\n                //     }\r\n\r\n                //     if (error.status === 429) {\r\n                //         alert(\r\n                //             'Something went wrong, please try again after a few seconds!'\r\n                //         );\r\n                //     }\r\n                // }\r\n\r\n                return throwError(error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAoB,eAAe;AAElD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,WAAW,QAAQ,yBAAyB;;AAGrD,OAAM,MAAOC,gBAAgB;EAD7BC,YAAA;IAEI,KAAAC,WAAW,GAAGN,MAAM,CAACG,WAAW,CAAC;;EACjCI,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BV,UAAU,CAAEW,KAAwB,IAAI;MACpC;MACAC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC;MAEA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA,OAAOV,UAAU,CAACU,KAAK,CAAC;IAC5B,CAAC,CAAC,CACL;EACL;EAAC,QAAAE,CAAA,G;qBAxBQV,gBAAgB;EAAA;EAAA,QAAAW,EAAA,G;WAAhBX,gBAAgB;IAAAY,OAAA,EAAhBZ,gBAAgB,CAAAa;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}