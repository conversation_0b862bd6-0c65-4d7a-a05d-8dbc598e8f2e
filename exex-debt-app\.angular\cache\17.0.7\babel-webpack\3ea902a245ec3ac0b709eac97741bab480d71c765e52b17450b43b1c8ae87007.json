{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"./app.menu.component\";\nexport class AppSidebarComponent {\n  constructor(layoutService, el) {\n    this.layoutService = layoutService;\n    this.el = el;\n  }\n  static #_ = this.ɵfac = function AppSidebarComponent_Factory(t) {\n    return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppSidebarComponent,\n    selectors: [[\"app-sidebar\"]],\n    decls: 1,\n    vars: 0,\n    template: function AppSidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-menu\");\n      }\n    },\n    dependencies: [i2.AppMenuComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "_2", "selectors", "decls", "vars", "template", "AppSidebarComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.sidebar.component.ts"], "sourcesContent": ["import { Component, ElementRef } from '@angular/core';\r\nimport { LayoutService } from './app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    template: `<app-menu></app-menu>`,\r\n})\r\nexport class AppSidebarComponent {\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n    ) {}\r\n}\r\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAC5BC,YACWC,aAA4B,EAC5BC,EAAc;IADd,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;EACV;EAAC,QAAAC,CAAA,G;qBAJKJ,mBAAmB,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBV,mBAAmB;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAFjBX,EAAA,CAAAa,SAAA,eAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}