{"ast": null, "code": "/**\n * @license Angular v17.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n  return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n  const prev = activeConsumer;\n  activeConsumer = consumer;\n  return prev;\n}\nfunction getActiveConsumer() {\n  return activeConsumer;\n}\nfunction isInNotificationPhase() {\n  return inNotificationPhase;\n}\nfunction isReactive(value) {\n  return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n  version: 0,\n  lastCleanEpoch: 0,\n  dirty: false,\n  producerNode: undefined,\n  producerLastReadVersion: undefined,\n  producerIndexOfThis: undefined,\n  nextProducerIndex: 0,\n  liveConsumerNode: undefined,\n  liveConsumerIndexOfThis: undefined,\n  consumerAllowSignalWrites: false,\n  consumerIsAlwaysLive: false,\n  producerMustRecompute: () => false,\n  producerRecomputeValue: () => {},\n  consumerMarkedDirty: () => {},\n  consumerOnSignalRead: () => {}\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n  if (inNotificationPhase) {\n    throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? `Assertion error: signal read during notification phase` : '');\n  }\n  if (activeConsumer === null) {\n    // Accessed outside of a reactive context, so nothing to record.\n    return;\n  }\n  activeConsumer.consumerOnSignalRead(node);\n  // This producer is the `idx`th dependency of `activeConsumer`.\n  const idx = activeConsumer.nextProducerIndex++;\n  assertConsumerNode(activeConsumer);\n  if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n    // There's been a change in producers since the last execution of `activeConsumer`.\n    // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n    // replaced with `this`.\n    //\n    // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n    // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n    // to remove it from the stale producer's `liveConsumer`s.\n    if (consumerIsLive(activeConsumer)) {\n      const staleProducer = activeConsumer.producerNode[idx];\n      producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n      // At this point, the only record of `staleProducer` is the reference at\n      // `activeConsumer.producerNode[idx]` which will be overwritten below.\n    }\n  }\n\n  if (activeConsumer.producerNode[idx] !== node) {\n    // We're a new dependency of the consumer (at `idx`).\n    activeConsumer.producerNode[idx] = node;\n    // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n    // placeholder value.\n    activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;\n  }\n  activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n  epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n  if (consumerIsLive(node) && !node.dirty) {\n    // A live consumer will be marked dirty by producers, so a clean state means that its version\n    // is guaranteed to be up-to-date.\n    return;\n  }\n  if (!node.dirty && node.lastCleanEpoch === epoch) {\n    // Even non-live consumers can skip polling if they previously found themselves to be clean at\n    // the current epoch, since their dependencies could not possibly have changed (such a change\n    // would've increased the epoch).\n    return;\n  }\n  if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n    // None of our producers report a change since the last time they were read, so no\n    // recomputation of our value is necessary, and we can consider ourselves clean.\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n    return;\n  }\n  node.producerRecomputeValue(node);\n  // After recomputing the value, we're no longer dirty.\n  node.dirty = false;\n  node.lastCleanEpoch = epoch;\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n  if (node.liveConsumerNode === undefined) {\n    return;\n  }\n  // Prevent signal reads when we're updating the graph\n  const prev = inNotificationPhase;\n  inNotificationPhase = true;\n  try {\n    for (const consumer of node.liveConsumerNode) {\n      if (!consumer.dirty) {\n        consumerMarkDirty(consumer);\n      }\n    }\n  } finally {\n    inNotificationPhase = prev;\n  }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n  return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n  node.dirty = true;\n  producerNotifyConsumers(node);\n  node.consumerMarkedDirty?.(node);\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n  node && (node.nextProducerIndex = 0);\n  return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n  setActiveConsumer(prevConsumer);\n  if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined || node.producerLastReadVersion === undefined) {\n    return;\n  }\n  if (consumerIsLive(node)) {\n    // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n    // which weren't dependencies after the recomputation.\n    for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate the producer tracking arrays.\n  // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n  // benchmarking has shown that individual pop operations are faster.\n  while (node.producerNode.length > node.nextProducerIndex) {\n    node.producerNode.pop();\n    node.producerLastReadVersion.pop();\n    node.producerIndexOfThis.pop();\n  }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n  assertConsumerNode(node);\n  // Poll producers for change.\n  for (let i = 0; i < node.producerNode.length; i++) {\n    const producer = node.producerNode[i];\n    const seenVersion = node.producerLastReadVersion[i];\n    // First check the versions. A mismatch means that the producer's value is known to have\n    // changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n    // The producer's version is the same as the last time we read it, but it might itself be\n    // stale. Force the producer to recompute its version (calculating a new value if necessary).\n    producerUpdateValueVersion(producer);\n    // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n    // versions still match then it has not changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n  }\n  return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n  assertConsumerNode(node);\n  if (consumerIsLive(node)) {\n    // Drop all connections from the graph to this node.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate all the arrays to drop all connection from this node to the graph.\n  node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length = 0;\n  if (node.liveConsumerNode) {\n    node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n  }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n  assertProducerNode(node);\n  assertConsumerNode(node);\n  if (node.liveConsumerNode.length === 0) {\n    // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n    }\n  }\n  node.liveConsumerIndexOfThis.push(indexOfThis);\n  return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n  assertProducerNode(node);\n  assertConsumerNode(node);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n    throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n  }\n  if (node.liveConsumerNode.length === 1) {\n    // When removing the last live consumer, we will no longer be live. We need to remove\n    // ourselves from our producers' tracking (which may cause consumer-producers to lose\n    // liveness as well).\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n  // live consumer, this is a no-op.\n  const lastIdx = node.liveConsumerNode.length - 1;\n  node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n  node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n  // Truncate the array.\n  node.liveConsumerNode.length--;\n  node.liveConsumerIndexOfThis.length--;\n  // If the index is still valid, then we need to fix the index pointer from the producer to this\n  // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n  if (idx < node.liveConsumerNode.length) {\n    const idxProducer = node.liveConsumerIndexOfThis[idx];\n    const consumer = node.liveConsumerNode[idx];\n    assertConsumerNode(consumer);\n    consumer.producerIndexOfThis[idxProducer] = idx;\n  }\n}\nfunction consumerIsLive(node) {\n  return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n  node.producerNode ??= [];\n  node.producerIndexOfThis ??= [];\n  node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n  node.liveConsumerNode ??= [];\n  node.liveConsumerIndexOfThis ??= [];\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation) {\n  const node = Object.create(COMPUTED_NODE);\n  node.computation = computation;\n  const computed = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  computed[SIGNAL] = node;\n  return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error('Detected cycle in computations.');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      try {\n        newValue = node.computation();\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (oldValue !== UNSET && oldValue !== ERRORED && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\nfunction defaultThrowError() {\n  throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError() {\n  throwInvalidWriteToSignalErrorFn();\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n  throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue) {\n  const node = Object.create(SIGNAL_NODE);\n  node.value = initialValue;\n  const getter = () => {\n    producerAccessed(node);\n    return node.value;\n  };\n  getter[SIGNAL] = node;\n  return getter;\n}\nfunction setPostSignalSetFn(fn) {\n  const prev = postSignalSetFn;\n  postSignalSetFn = fn;\n  return prev;\n}\nfunction signalGetFn() {\n  producerAccessed(this);\n  return this.value;\n}\nfunction signalSetFn(node, newValue) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n  if (!node.equal(node.value, newValue)) {\n    node.value = newValue;\n    signalValueChanged(node);\n  }\n}\nfunction signalUpdateFn(node, updater) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n  signalSetFn(node, updater(node.value));\n}\nfunction signalMutateFn(node, mutator) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n  // Mutate bypasses equality checks as it's by definition changing the value.\n  mutator(node.value);\n  signalValueChanged(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    equal: defaultEquals,\n    value: undefined\n  };\n})();\nfunction signalValueChanged(node) {\n  node.version++;\n  producerIncrementEpoch();\n  producerNotifyConsumers(node);\n  postSignalSetFn?.();\n}\nfunction createWatch(fn, schedule, allowSignalWrites) {\n  const node = Object.create(WATCH_NODE);\n  if (allowSignalWrites) {\n    node.consumerAllowSignalWrites = true;\n  }\n  node.fn = fn;\n  node.schedule = schedule;\n  const registerOnCleanup = cleanupFn => {\n    node.cleanupFn = cleanupFn;\n  };\n  function isWatchNodeDestroyed(node) {\n    return node.fn === null && node.schedule === null;\n  }\n  function destroyWatchNode(node) {\n    if (!isWatchNodeDestroyed(node)) {\n      consumerDestroy(node); // disconnect watcher from the reactive graph\n      node.cleanupFn();\n      // nullify references to the integration functions to mark node as destroyed\n      node.fn = null;\n      node.schedule = null;\n      node.cleanupFn = NOOP_CLEANUP_FN;\n    }\n  }\n  const run = () => {\n    if (node.fn === null) {\n      // trying to run a destroyed watch is noop\n      return;\n    }\n    if (isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    node.dirty = false;\n    if (node.hasRun && !consumerPollProducersForChange(node)) {\n      return;\n    }\n    node.hasRun = true;\n    const prevConsumer = consumerBeforeComputation(node);\n    try {\n      node.cleanupFn();\n      node.cleanupFn = NOOP_CLEANUP_FN;\n      node.fn(registerOnCleanup);\n    } finally {\n      consumerAfterComputation(node, prevConsumer);\n    }\n  };\n  node.ref = {\n    notify: () => consumerMarkDirty(node),\n    run,\n    cleanup: () => node.cleanupFn(),\n    destroy: () => destroyWatchNode(node),\n    [SIGNAL]: node\n  };\n  return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => {};\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: false,\n    consumerMarkedDirty: node => {\n      if (node.schedule !== null) {\n        node.schedule(node.ref);\n      }\n    },\n    hasRun: false,\n    cleanupFn: NOOP_CLEANUP_FN\n  };\n})();\nfunction setAlternateWeakRefImpl(impl) {\n  // TODO: remove this function\n}\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerPollProducersForChange, createComputed, createSignal, createWatch, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalMutateFn, signalSetFn, signalUpdateFn };", "map": {"version": 3, "names": ["defaultEquals", "a", "b", "Object", "is", "activeConsumer", "inNotificationPhase", "epoch", "SIGNAL", "Symbol", "setActiveConsumer", "consumer", "prev", "getActiveConsumer", "isInNotificationPhase", "isReactive", "value", "undefined", "REACTIVE_NODE", "version", "lastCleanEpoch", "dirty", "producerNode", "producerLastReadVersion", "producerIndexOfThis", "nextProducerIndex", "liveConsumerNode", "liveConsumerIndexOfThis", "consumerAllowSignalWrites", "consumerIsAlwaysLive", "producerMustRecompute", "producerRecomputeValue", "consumerMarkedDirty", "consumerOnSignalRead", "producerAccessed", "node", "Error", "ngDevMode", "idx", "assertConsumerNode", "length", "consumerIsLive", "staleProducer", "producerRemoveLiveConsumerAtIndex", "producerAddLiveConsumer", "producerIncrementEpoch", "producerUpdateValueVersion", "consumerPollProducersForChange", "producerNotifyConsumers", "consumerMarkDirty", "producer<PERSON><PERSON>datesAllowed", "consumerBeforeComputation", "consumerAfterComputation", "prevConsumer", "i", "pop", "producer", "seenVersion", "consumerDestroy", "indexOfThis", "assertProducerNode", "push", "lastIdx", "idxProducer", "createComputed", "computation", "create", "COMPUTED_NODE", "computed", "ERRORED", "error", "UNSET", "COMPUTING", "equal", "oldValue", "newValue", "err", "defaultThrowError", "throwInvalidWriteToSignalErrorFn", "throwInvalidWriteToSignalError", "setThrowInvalidWriteToSignalError", "fn", "postSignalSetFn", "createSignal", "initialValue", "SIGNAL_NODE", "getter", "setPostSignalSetFn", "signalGetFn", "signalSetFn", "signalValueChanged", "signalUpdateFn", "updater", "signalMutateFn", "mutator", "createWatch", "schedule", "allowSignalWrites", "WATCH_NODE", "registerOnCleanup", "cleanupFn", "isWatchNodeDestroyed", "destroyWatchNode", "NOOP_CLEANUP_FN", "run", "<PERSON><PERSON>un", "ref", "notify", "cleanup", "destroy", "setAlternateWeakRefImpl", "impl"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/@angular/core/fesm2022/primitives/signals.mjs"], "sourcesContent": ["/**\n * @license Angular v17.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n    return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n    const prev = activeConsumer;\n    activeConsumer = consumer;\n    return prev;\n}\nfunction getActiveConsumer() {\n    return activeConsumer;\n}\nfunction isInNotificationPhase() {\n    return inNotificationPhase;\n}\nfunction isReactive(value) {\n    return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n    version: 0,\n    lastCleanEpoch: 0,\n    dirty: false,\n    producerNode: undefined,\n    producerLastReadVersion: undefined,\n    producerIndexOfThis: undefined,\n    nextProducerIndex: 0,\n    liveConsumerNode: undefined,\n    liveConsumerIndexOfThis: undefined,\n    consumerAllowSignalWrites: false,\n    consumerIsAlwaysLive: false,\n    producerMustRecompute: () => false,\n    producerRecomputeValue: () => { },\n    consumerMarkedDirty: () => { },\n    consumerOnSignalRead: () => { },\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n    if (inNotificationPhase) {\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ?\n            `Assertion error: signal read during notification phase` :\n            '');\n    }\n    if (activeConsumer === null) {\n        // Accessed outside of a reactive context, so nothing to record.\n        return;\n    }\n    activeConsumer.consumerOnSignalRead(node);\n    // This producer is the `idx`th dependency of `activeConsumer`.\n    const idx = activeConsumer.nextProducerIndex++;\n    assertConsumerNode(activeConsumer);\n    if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n        // There's been a change in producers since the last execution of `activeConsumer`.\n        // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n        // replaced with `this`.\n        //\n        // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n        // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n        // to remove it from the stale producer's `liveConsumer`s.\n        if (consumerIsLive(activeConsumer)) {\n            const staleProducer = activeConsumer.producerNode[idx];\n            producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n            // At this point, the only record of `staleProducer` is the reference at\n            // `activeConsumer.producerNode[idx]` which will be overwritten below.\n        }\n    }\n    if (activeConsumer.producerNode[idx] !== node) {\n        // We're a new dependency of the consumer (at `idx`).\n        activeConsumer.producerNode[idx] = node;\n        // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n        // placeholder value.\n        activeConsumer.producerIndexOfThis[idx] =\n            consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;\n    }\n    activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n    epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n    if (consumerIsLive(node) && !node.dirty) {\n        // A live consumer will be marked dirty by producers, so a clean state means that its version\n        // is guaranteed to be up-to-date.\n        return;\n    }\n    if (!node.dirty && node.lastCleanEpoch === epoch) {\n        // Even non-live consumers can skip polling if they previously found themselves to be clean at\n        // the current epoch, since their dependencies could not possibly have changed (such a change\n        // would've increased the epoch).\n        return;\n    }\n    if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n        // None of our producers report a change since the last time they were read, so no\n        // recomputation of our value is necessary, and we can consider ourselves clean.\n        node.dirty = false;\n        node.lastCleanEpoch = epoch;\n        return;\n    }\n    node.producerRecomputeValue(node);\n    // After recomputing the value, we're no longer dirty.\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n    if (node.liveConsumerNode === undefined) {\n        return;\n    }\n    // Prevent signal reads when we're updating the graph\n    const prev = inNotificationPhase;\n    inNotificationPhase = true;\n    try {\n        for (const consumer of node.liveConsumerNode) {\n            if (!consumer.dirty) {\n                consumerMarkDirty(consumer);\n            }\n        }\n    }\n    finally {\n        inNotificationPhase = prev;\n    }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n    return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n    node.dirty = true;\n    producerNotifyConsumers(node);\n    node.consumerMarkedDirty?.(node);\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n    node && (node.nextProducerIndex = 0);\n    return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n    setActiveConsumer(prevConsumer);\n    if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined ||\n        node.producerLastReadVersion === undefined) {\n        return;\n    }\n    if (consumerIsLive(node)) {\n        // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n        // which weren't dependencies after the recomputation.\n        for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate the producer tracking arrays.\n    // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n    // benchmarking has shown that individual pop operations are faster.\n    while (node.producerNode.length > node.nextProducerIndex) {\n        node.producerNode.pop();\n        node.producerLastReadVersion.pop();\n        node.producerIndexOfThis.pop();\n    }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n    assertConsumerNode(node);\n    // Poll producers for change.\n    for (let i = 0; i < node.producerNode.length; i++) {\n        const producer = node.producerNode[i];\n        const seenVersion = node.producerLastReadVersion[i];\n        // First check the versions. A mismatch means that the producer's value is known to have\n        // changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n        // The producer's version is the same as the last time we read it, but it might itself be\n        // stale. Force the producer to recompute its version (calculating a new value if necessary).\n        producerUpdateValueVersion(producer);\n        // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n        // versions still match then it has not changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n    assertConsumerNode(node);\n    if (consumerIsLive(node)) {\n        // Drop all connections from the graph to this node.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate all the arrays to drop all connection from this node to the graph.\n    node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length =\n        0;\n    if (node.liveConsumerNode) {\n        node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n    }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n    assertProducerNode(node);\n    assertConsumerNode(node);\n    if (node.liveConsumerNode.length === 0) {\n        // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n        }\n    }\n    node.liveConsumerIndexOfThis.push(indexOfThis);\n    return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n    assertProducerNode(node);\n    assertConsumerNode(node);\n    if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n        throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n    }\n    if (node.liveConsumerNode.length === 1) {\n        // When removing the last live consumer, we will no longer be live. We need to remove\n        // ourselves from our producers' tracking (which may cause consumer-producers to lose\n        // liveness as well).\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n    // live consumer, this is a no-op.\n    const lastIdx = node.liveConsumerNode.length - 1;\n    node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n    node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n    // Truncate the array.\n    node.liveConsumerNode.length--;\n    node.liveConsumerIndexOfThis.length--;\n    // If the index is still valid, then we need to fix the index pointer from the producer to this\n    // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n    if (idx < node.liveConsumerNode.length) {\n        const idxProducer = node.liveConsumerIndexOfThis[idx];\n        const consumer = node.liveConsumerNode[idx];\n        assertConsumerNode(consumer);\n        consumer.producerIndexOfThis[idxProducer] = idx;\n    }\n}\nfunction consumerIsLive(node) {\n    return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n    node.producerNode ??= [];\n    node.producerIndexOfThis ??= [];\n    node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n    node.liveConsumerNode ??= [];\n    node.liveConsumerIndexOfThis ??= [];\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation) {\n    const node = Object.create(COMPUTED_NODE);\n    node.computation = computation;\n    const computed = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    computed[SIGNAL] = node;\n    return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */ Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */ Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */ Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error('Detected cycle in computations.');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            try {\n                newValue = node.computation();\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (oldValue !== UNSET && oldValue !== ERRORED && newValue !== ERRORED &&\n                node.equal(oldValue, newValue)) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\nfunction defaultThrowError() {\n    throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError() {\n    throwInvalidWriteToSignalErrorFn();\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n    throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue) {\n    const node = Object.create(SIGNAL_NODE);\n    node.value = initialValue;\n    const getter = (() => {\n        producerAccessed(node);\n        return node.value;\n    });\n    getter[SIGNAL] = node;\n    return getter;\n}\nfunction setPostSignalSetFn(fn) {\n    const prev = postSignalSetFn;\n    postSignalSetFn = fn;\n    return prev;\n}\nfunction signalGetFn() {\n    producerAccessed(this);\n    return this.value;\n}\nfunction signalSetFn(node, newValue) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError();\n    }\n    if (!node.equal(node.value, newValue)) {\n        node.value = newValue;\n        signalValueChanged(node);\n    }\n}\nfunction signalUpdateFn(node, updater) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError();\n    }\n    signalSetFn(node, updater(node.value));\n}\nfunction signalMutateFn(node, mutator) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError();\n    }\n    // Mutate bypasses equality checks as it's by definition changing the value.\n    mutator(node.value);\n    signalValueChanged(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        equal: defaultEquals,\n        value: undefined,\n    };\n})();\nfunction signalValueChanged(node) {\n    node.version++;\n    producerIncrementEpoch();\n    producerNotifyConsumers(node);\n    postSignalSetFn?.();\n}\n\nfunction createWatch(fn, schedule, allowSignalWrites) {\n    const node = Object.create(WATCH_NODE);\n    if (allowSignalWrites) {\n        node.consumerAllowSignalWrites = true;\n    }\n    node.fn = fn;\n    node.schedule = schedule;\n    const registerOnCleanup = (cleanupFn) => {\n        node.cleanupFn = cleanupFn;\n    };\n    function isWatchNodeDestroyed(node) {\n        return node.fn === null && node.schedule === null;\n    }\n    function destroyWatchNode(node) {\n        if (!isWatchNodeDestroyed(node)) {\n            consumerDestroy(node); // disconnect watcher from the reactive graph\n            node.cleanupFn();\n            // nullify references to the integration functions to mark node as destroyed\n            node.fn = null;\n            node.schedule = null;\n            node.cleanupFn = NOOP_CLEANUP_FN;\n        }\n    }\n    const run = () => {\n        if (node.fn === null) {\n            // trying to run a destroyed watch is noop\n            return;\n        }\n        if (isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        node.dirty = false;\n        if (node.hasRun && !consumerPollProducersForChange(node)) {\n            return;\n        }\n        node.hasRun = true;\n        const prevConsumer = consumerBeforeComputation(node);\n        try {\n            node.cleanupFn();\n            node.cleanupFn = NOOP_CLEANUP_FN;\n            node.fn(registerOnCleanup);\n        }\n        finally {\n            consumerAfterComputation(node, prevConsumer);\n        }\n    };\n    node.ref = {\n        notify: () => consumerMarkDirty(node),\n        run,\n        cleanup: () => node.cleanupFn(),\n        destroy: () => destroyWatchNode(node),\n        [SIGNAL]: node,\n    };\n    return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => { };\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        consumerIsAlwaysLive: true,\n        consumerAllowSignalWrites: false,\n        consumerMarkedDirty: (node) => {\n            if (node.schedule !== null) {\n                node.schedule(node.ref);\n            }\n        },\n        hasRun: false,\n        cleanupFn: NOOP_CLEANUP_FN,\n    };\n})();\n\nfunction setAlternateWeakRefImpl(impl) {\n    // TODO: remove this function\n}\n\nexport { REACTIVE_NODE, SIGNAL, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerPollProducersForChange, createComputed, createSignal, createWatch, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, producerAccessed, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalMutateFn, signalSetFn, signalUpdateFn };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,MAAM,CAACC,EAAE,CAACH,CAAC,EAAEC,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIG,cAAc,GAAG,IAAI;AACzB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B;AACA;AACA;AACA,IAAIC,KAAK,GAAG,CAAC;AACb;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,eAAgBC,MAAM,CAAC,QAAQ,CAAC;AAC/C,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,IAAI,GAAGP,cAAc;EAC3BA,cAAc,GAAGM,QAAQ;EACzB,OAAOC,IAAI;AACf;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOR,cAAc;AACzB;AACA,SAASS,qBAAqBA,CAAA,EAAG;EAC7B,OAAOR,mBAAmB;AAC9B;AACA,SAASS,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACR,MAAM,CAAC,KAAKS,SAAS;AACtC;AACA,MAAMC,aAAa,GAAG;EAClBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,CAAC;EACjBC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAEL,SAAS;EACvBM,uBAAuB,EAAEN,SAAS;EAClCO,mBAAmB,EAAEP,SAAS;EAC9BQ,iBAAiB,EAAE,CAAC;EACpBC,gBAAgB,EAAET,SAAS;EAC3BU,uBAAuB,EAAEV,SAAS;EAClCW,yBAAyB,EAAE,KAAK;EAChCC,oBAAoB,EAAE,KAAK;EAC3BC,qBAAqB,EAAEA,CAAA,KAAM,KAAK;EAClCC,sBAAsB,EAAEA,CAAA,KAAM,CAAE,CAAC;EACjCC,mBAAmB,EAAEA,CAAA,KAAM,CAAE,CAAC;EAC9BC,oBAAoB,EAAEA,CAAA,KAAM,CAAE;AAClC,CAAC;AACD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAI7B,mBAAmB,EAAE;IACrB,MAAM,IAAI8B,KAAK,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,GACxD,wDAAuD,GACxD,EAAE,CAAC;EACX;EACA,IAAIhC,cAAc,KAAK,IAAI,EAAE;IACzB;IACA;EACJ;EACAA,cAAc,CAAC4B,oBAAoB,CAACE,IAAI,CAAC;EACzC;EACA,MAAMG,GAAG,GAAGjC,cAAc,CAACoB,iBAAiB,EAAE;EAC9Cc,kBAAkB,CAAClC,cAAc,CAAC;EAClC,IAAIiC,GAAG,GAAGjC,cAAc,CAACiB,YAAY,CAACkB,MAAM,IAAInC,cAAc,CAACiB,YAAY,CAACgB,GAAG,CAAC,KAAKH,IAAI,EAAE;IACvF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIM,cAAc,CAACpC,cAAc,CAAC,EAAE;MAChC,MAAMqC,aAAa,GAAGrC,cAAc,CAACiB,YAAY,CAACgB,GAAG,CAAC;MACtDK,iCAAiC,CAACD,aAAa,EAAErC,cAAc,CAACmB,mBAAmB,CAACc,GAAG,CAAC,CAAC;MACzF;MACA;IACJ;EACJ;;EACA,IAAIjC,cAAc,CAACiB,YAAY,CAACgB,GAAG,CAAC,KAAKH,IAAI,EAAE;IAC3C;IACA9B,cAAc,CAACiB,YAAY,CAACgB,GAAG,CAAC,GAAGH,IAAI;IACvC;IACA;IACA9B,cAAc,CAACmB,mBAAmB,CAACc,GAAG,CAAC,GACnCG,cAAc,CAACpC,cAAc,CAAC,GAAGuC,uBAAuB,CAACT,IAAI,EAAE9B,cAAc,EAAEiC,GAAG,CAAC,GAAG,CAAC;EAC/F;EACAjC,cAAc,CAACkB,uBAAuB,CAACe,GAAG,CAAC,GAAGH,IAAI,CAAChB,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0B,sBAAsBA,CAAA,EAAG;EAC9BtC,KAAK,EAAE;AACX;AACA;AACA;AACA;AACA,SAASuC,0BAA0BA,CAACX,IAAI,EAAE;EACtC,IAAIM,cAAc,CAACN,IAAI,CAAC,IAAI,CAACA,IAAI,CAACd,KAAK,EAAE;IACrC;IACA;IACA;EACJ;EACA,IAAI,CAACc,IAAI,CAACd,KAAK,IAAIc,IAAI,CAACf,cAAc,KAAKb,KAAK,EAAE;IAC9C;IACA;IACA;IACA;EACJ;EACA,IAAI,CAAC4B,IAAI,CAACL,qBAAqB,CAACK,IAAI,CAAC,IAAI,CAACY,8BAA8B,CAACZ,IAAI,CAAC,EAAE;IAC5E;IACA;IACAA,IAAI,CAACd,KAAK,GAAG,KAAK;IAClBc,IAAI,CAACf,cAAc,GAAGb,KAAK;IAC3B;EACJ;EACA4B,IAAI,CAACJ,sBAAsB,CAACI,IAAI,CAAC;EACjC;EACAA,IAAI,CAACd,KAAK,GAAG,KAAK;EAClBc,IAAI,CAACf,cAAc,GAAGb,KAAK;AAC/B;AACA;AACA;AACA;AACA,SAASyC,uBAAuBA,CAACb,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACT,gBAAgB,KAAKT,SAAS,EAAE;IACrC;EACJ;EACA;EACA,MAAML,IAAI,GAAGN,mBAAmB;EAChCA,mBAAmB,GAAG,IAAI;EAC1B,IAAI;IACA,KAAK,MAAMK,QAAQ,IAAIwB,IAAI,CAACT,gBAAgB,EAAE;MAC1C,IAAI,CAACf,QAAQ,CAACU,KAAK,EAAE;QACjB4B,iBAAiB,CAACtC,QAAQ,CAAC;MAC/B;IACJ;EACJ,CAAC,SACO;IACJL,mBAAmB,GAAGM,IAAI;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASsC,sBAAsBA,CAAA,EAAG;EAC9B,OAAO7C,cAAc,EAAEuB,yBAAyB,KAAK,KAAK;AAC9D;AACA,SAASqB,iBAAiBA,CAACd,IAAI,EAAE;EAC7BA,IAAI,CAACd,KAAK,GAAG,IAAI;EACjB2B,uBAAuB,CAACb,IAAI,CAAC;EAC7BA,IAAI,CAACH,mBAAmB,GAAGG,IAAI,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,yBAAyBA,CAAChB,IAAI,EAAE;EACrCA,IAAI,KAAKA,IAAI,CAACV,iBAAiB,GAAG,CAAC,CAAC;EACpC,OAAOf,iBAAiB,CAACyB,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,wBAAwBA,CAACjB,IAAI,EAAEkB,YAAY,EAAE;EAClD3C,iBAAiB,CAAC2C,YAAY,CAAC;EAC/B,IAAI,CAAClB,IAAI,IAAIA,IAAI,CAACb,YAAY,KAAKL,SAAS,IAAIkB,IAAI,CAACX,mBAAmB,KAAKP,SAAS,IAClFkB,IAAI,CAACZ,uBAAuB,KAAKN,SAAS,EAAE;IAC5C;EACJ;EACA,IAAIwB,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA;IACA,KAAK,IAAImB,CAAC,GAAGnB,IAAI,CAACV,iBAAiB,EAAE6B,CAAC,GAAGnB,IAAI,CAACb,YAAY,CAACkB,MAAM,EAAEc,CAAC,EAAE,EAAE;MACpEX,iCAAiC,CAACR,IAAI,CAACb,YAAY,CAACgC,CAAC,CAAC,EAAEnB,IAAI,CAACX,mBAAmB,CAAC8B,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA;EACA,OAAOnB,IAAI,CAACb,YAAY,CAACkB,MAAM,GAAGL,IAAI,CAACV,iBAAiB,EAAE;IACtDU,IAAI,CAACb,YAAY,CAACiC,GAAG,CAAC,CAAC;IACvBpB,IAAI,CAACZ,uBAAuB,CAACgC,GAAG,CAAC,CAAC;IAClCpB,IAAI,CAACX,mBAAmB,CAAC+B,GAAG,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASR,8BAA8BA,CAACZ,IAAI,EAAE;EAC1CI,kBAAkB,CAACJ,IAAI,CAAC;EACxB;EACA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACb,YAAY,CAACkB,MAAM,EAAEc,CAAC,EAAE,EAAE;IAC/C,MAAME,QAAQ,GAAGrB,IAAI,CAACb,YAAY,CAACgC,CAAC,CAAC;IACrC,MAAMG,WAAW,GAAGtB,IAAI,CAACZ,uBAAuB,CAAC+B,CAAC,CAAC;IACnD;IACA;IACA,IAAIG,WAAW,KAAKD,QAAQ,CAACrC,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;IACA;IACA;IACA2B,0BAA0B,CAACU,QAAQ,CAAC;IACpC;IACA;IACA,IAAIC,WAAW,KAAKD,QAAQ,CAACrC,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASuC,eAAeA,CAACvB,IAAI,EAAE;EAC3BI,kBAAkB,CAACJ,IAAI,CAAC;EACxB,IAAIM,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACb,YAAY,CAACkB,MAAM,EAAEc,CAAC,EAAE,EAAE;MAC/CX,iCAAiC,CAACR,IAAI,CAACb,YAAY,CAACgC,CAAC,CAAC,EAAEnB,IAAI,CAACX,mBAAmB,CAAC8B,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACAnB,IAAI,CAACb,YAAY,CAACkB,MAAM,GAAGL,IAAI,CAACZ,uBAAuB,CAACiB,MAAM,GAAGL,IAAI,CAACX,mBAAmB,CAACgB,MAAM,GAC5F,CAAC;EACL,IAAIL,IAAI,CAACT,gBAAgB,EAAE;IACvBS,IAAI,CAACT,gBAAgB,CAACc,MAAM,GAAGL,IAAI,CAACR,uBAAuB,CAACa,MAAM,GAAG,CAAC;EAC1E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAACT,IAAI,EAAExB,QAAQ,EAAEgD,WAAW,EAAE;EAC1DC,kBAAkB,CAACzB,IAAI,CAAC;EACxBI,kBAAkB,CAACJ,IAAI,CAAC;EACxB,IAAIA,IAAI,CAACT,gBAAgB,CAACc,MAAM,KAAK,CAAC,EAAE;IACpC;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACb,YAAY,CAACkB,MAAM,EAAEc,CAAC,EAAE,EAAE;MAC/CnB,IAAI,CAACX,mBAAmB,CAAC8B,CAAC,CAAC,GAAGV,uBAAuB,CAACT,IAAI,CAACb,YAAY,CAACgC,CAAC,CAAC,EAAEnB,IAAI,EAAEmB,CAAC,CAAC;IACxF;EACJ;EACAnB,IAAI,CAACR,uBAAuB,CAACkC,IAAI,CAACF,WAAW,CAAC;EAC9C,OAAOxB,IAAI,CAACT,gBAAgB,CAACmC,IAAI,CAAClD,QAAQ,CAAC,GAAG,CAAC;AACnD;AACA;AACA;AACA;AACA,SAASgC,iCAAiCA,CAACR,IAAI,EAAEG,GAAG,EAAE;EAClDsB,kBAAkB,CAACzB,IAAI,CAAC;EACxBI,kBAAkB,CAACJ,IAAI,CAAC;EACxB,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,IAAIC,GAAG,IAAIH,IAAI,CAACT,gBAAgB,CAACc,MAAM,EAAE;IACtF,MAAM,IAAIJ,KAAK,CAAE,0CAAyCE,GAAI,wBAAuBH,IAAI,CAACT,gBAAgB,CAACc,MAAO,aAAY,CAAC;EACnI;EACA,IAAIL,IAAI,CAACT,gBAAgB,CAACc,MAAM,KAAK,CAAC,EAAE;IACpC;IACA;IACA;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACb,YAAY,CAACkB,MAAM,EAAEc,CAAC,EAAE,EAAE;MAC/CX,iCAAiC,CAACR,IAAI,CAACb,YAAY,CAACgC,CAAC,CAAC,EAAEnB,IAAI,CAACX,mBAAmB,CAAC8B,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA,MAAMQ,OAAO,GAAG3B,IAAI,CAACT,gBAAgB,CAACc,MAAM,GAAG,CAAC;EAChDL,IAAI,CAACT,gBAAgB,CAACY,GAAG,CAAC,GAAGH,IAAI,CAACT,gBAAgB,CAACoC,OAAO,CAAC;EAC3D3B,IAAI,CAACR,uBAAuB,CAACW,GAAG,CAAC,GAAGH,IAAI,CAACR,uBAAuB,CAACmC,OAAO,CAAC;EACzE;EACA3B,IAAI,CAACT,gBAAgB,CAACc,MAAM,EAAE;EAC9BL,IAAI,CAACR,uBAAuB,CAACa,MAAM,EAAE;EACrC;EACA;EACA,IAAIF,GAAG,GAAGH,IAAI,CAACT,gBAAgB,CAACc,MAAM,EAAE;IACpC,MAAMuB,WAAW,GAAG5B,IAAI,CAACR,uBAAuB,CAACW,GAAG,CAAC;IACrD,MAAM3B,QAAQ,GAAGwB,IAAI,CAACT,gBAAgB,CAACY,GAAG,CAAC;IAC3CC,kBAAkB,CAAC5B,QAAQ,CAAC;IAC5BA,QAAQ,CAACa,mBAAmB,CAACuC,WAAW,CAAC,GAAGzB,GAAG;EACnD;AACJ;AACA,SAASG,cAAcA,CAACN,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACN,oBAAoB,IAAI,CAACM,IAAI,EAAET,gBAAgB,EAAEc,MAAM,IAAI,CAAC,IAAI,CAAC;AACjF;AACA,SAASD,kBAAkBA,CAACJ,IAAI,EAAE;EAC9BA,IAAI,CAACb,YAAY,KAAK,EAAE;EACxBa,IAAI,CAACX,mBAAmB,KAAK,EAAE;EAC/BW,IAAI,CAACZ,uBAAuB,KAAK,EAAE;AACvC;AACA,SAASqC,kBAAkBA,CAACzB,IAAI,EAAE;EAC9BA,IAAI,CAACT,gBAAgB,KAAK,EAAE;EAC5BS,IAAI,CAACR,uBAAuB,KAAK,EAAE;AACvC;;AAEA;AACA;AACA;AACA,SAASqC,cAAcA,CAACC,WAAW,EAAE;EACjC,MAAM9B,IAAI,GAAGhC,MAAM,CAAC+D,MAAM,CAACC,aAAa,CAAC;EACzChC,IAAI,CAAC8B,WAAW,GAAGA,WAAW;EAC9B,MAAMG,QAAQ,GAAGA,CAAA,KAAM;IACnB;IACAtB,0BAA0B,CAACX,IAAI,CAAC;IAChC;IACAD,gBAAgB,CAACC,IAAI,CAAC;IACtB,IAAIA,IAAI,CAACnB,KAAK,KAAKqD,OAAO,EAAE;MACxB,MAAMlC,IAAI,CAACmC,KAAK;IACpB;IACA,OAAOnC,IAAI,CAACnB,KAAK;EACrB,CAAC;EACDoD,QAAQ,CAAC5D,MAAM,CAAC,GAAG2B,IAAI;EACvB,OAAOiC,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,GAAG,eAAgB9D,MAAM,CAAC,OAAO,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,MAAM+D,SAAS,GAAG,eAAgB/D,MAAM,CAAC,WAAW,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,MAAM4D,OAAO,GAAG,eAAgB5D,MAAM,CAAC,SAAS,CAAC;AACjD;AACA;AACA;AACA,MAAM0D,aAAa,GAAG,eAAgB,CAAC,MAAM;EACzC,OAAO;IACH,GAAGjD,aAAa;IAChBF,KAAK,EAAEuD,KAAK;IACZlD,KAAK,EAAE,IAAI;IACXiD,KAAK,EAAE,IAAI;IACXG,KAAK,EAAEzE,aAAa;IACpB8B,qBAAqBA,CAACK,IAAI,EAAE;MACxB;MACA;MACA,OAAOA,IAAI,CAACnB,KAAK,KAAKuD,KAAK,IAAIpC,IAAI,CAACnB,KAAK,KAAKwD,SAAS;IAC3D,CAAC;IACDzC,sBAAsBA,CAACI,IAAI,EAAE;MACzB,IAAIA,IAAI,CAACnB,KAAK,KAAKwD,SAAS,EAAE;QAC1B;QACA,MAAM,IAAIpC,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACA,MAAMsC,QAAQ,GAAGvC,IAAI,CAACnB,KAAK;MAC3BmB,IAAI,CAACnB,KAAK,GAAGwD,SAAS;MACtB,MAAMnB,YAAY,GAAGF,yBAAyB,CAAChB,IAAI,CAAC;MACpD,IAAIwC,QAAQ;MACZ,IAAI;QACAA,QAAQ,GAAGxC,IAAI,CAAC8B,WAAW,CAAC,CAAC;MACjC,CAAC,CACD,OAAOW,GAAG,EAAE;QACRD,QAAQ,GAAGN,OAAO;QAClBlC,IAAI,CAACmC,KAAK,GAAGM,GAAG;MACpB,CAAC,SACO;QACJxB,wBAAwB,CAACjB,IAAI,EAAEkB,YAAY,CAAC;MAChD;MACA,IAAIqB,QAAQ,KAAKH,KAAK,IAAIG,QAAQ,KAAKL,OAAO,IAAIM,QAAQ,KAAKN,OAAO,IAClElC,IAAI,CAACsC,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAChC;QACA;QACAxC,IAAI,CAACnB,KAAK,GAAG0D,QAAQ;QACrB;MACJ;MACAvC,IAAI,CAACnB,KAAK,GAAG2D,QAAQ;MACrBxC,IAAI,CAAChB,OAAO,EAAE;IAClB;EACJ,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAAS0D,iBAAiBA,CAAA,EAAG;EACzB,MAAM,IAAIzC,KAAK,CAAC,CAAC;AACrB;AACA,IAAI0C,gCAAgC,GAAGD,iBAAiB;AACxD,SAASE,8BAA8BA,CAAA,EAAG;EACtCD,gCAAgC,CAAC,CAAC;AACtC;AACA,SAASE,iCAAiCA,CAACC,EAAE,EAAE;EAC3CH,gCAAgC,GAAGG,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,IAAI;AAC1B;AACA;AACA;AACA,SAASC,YAAYA,CAACC,YAAY,EAAE;EAChC,MAAMjD,IAAI,GAAGhC,MAAM,CAAC+D,MAAM,CAACmB,WAAW,CAAC;EACvClD,IAAI,CAACnB,KAAK,GAAGoE,YAAY;EACzB,MAAME,MAAM,GAAIA,CAAA,KAAM;IAClBpD,gBAAgB,CAACC,IAAI,CAAC;IACtB,OAAOA,IAAI,CAACnB,KAAK;EACrB,CAAE;EACFsE,MAAM,CAAC9E,MAAM,CAAC,GAAG2B,IAAI;EACrB,OAAOmD,MAAM;AACjB;AACA,SAASC,kBAAkBA,CAACN,EAAE,EAAE;EAC5B,MAAMrE,IAAI,GAAGsE,eAAe;EAC5BA,eAAe,GAAGD,EAAE;EACpB,OAAOrE,IAAI;AACf;AACA,SAAS4E,WAAWA,CAAA,EAAG;EACnBtD,gBAAgB,CAAC,IAAI,CAAC;EACtB,OAAO,IAAI,CAAClB,KAAK;AACrB;AACA,SAASyE,WAAWA,CAACtD,IAAI,EAAEwC,QAAQ,EAAE;EACjC,IAAI,CAACzB,sBAAsB,CAAC,CAAC,EAAE;IAC3B6B,8BAA8B,CAAC,CAAC;EACpC;EACA,IAAI,CAAC5C,IAAI,CAACsC,KAAK,CAACtC,IAAI,CAACnB,KAAK,EAAE2D,QAAQ,CAAC,EAAE;IACnCxC,IAAI,CAACnB,KAAK,GAAG2D,QAAQ;IACrBe,kBAAkB,CAACvD,IAAI,CAAC;EAC5B;AACJ;AACA,SAASwD,cAAcA,CAACxD,IAAI,EAAEyD,OAAO,EAAE;EACnC,IAAI,CAAC1C,sBAAsB,CAAC,CAAC,EAAE;IAC3B6B,8BAA8B,CAAC,CAAC;EACpC;EACAU,WAAW,CAACtD,IAAI,EAAEyD,OAAO,CAACzD,IAAI,CAACnB,KAAK,CAAC,CAAC;AAC1C;AACA,SAAS6E,cAAcA,CAAC1D,IAAI,EAAE2D,OAAO,EAAE;EACnC,IAAI,CAAC5C,sBAAsB,CAAC,CAAC,EAAE;IAC3B6B,8BAA8B,CAAC,CAAC;EACpC;EACA;EACAe,OAAO,CAAC3D,IAAI,CAACnB,KAAK,CAAC;EACnB0E,kBAAkB,CAACvD,IAAI,CAAC;AAC5B;AACA;AACA;AACA;AACA,MAAMkD,WAAW,GAAG,eAAgB,CAAC,MAAM;EACvC,OAAO;IACH,GAAGnE,aAAa;IAChBuD,KAAK,EAAEzE,aAAa;IACpBgB,KAAK,EAAEC;EACX,CAAC;AACL,CAAC,EAAE,CAAC;AACJ,SAASyE,kBAAkBA,CAACvD,IAAI,EAAE;EAC9BA,IAAI,CAAChB,OAAO,EAAE;EACd0B,sBAAsB,CAAC,CAAC;EACxBG,uBAAuB,CAACb,IAAI,CAAC;EAC7B+C,eAAe,GAAG,CAAC;AACvB;AAEA,SAASa,WAAWA,CAACd,EAAE,EAAEe,QAAQ,EAAEC,iBAAiB,EAAE;EAClD,MAAM9D,IAAI,GAAGhC,MAAM,CAAC+D,MAAM,CAACgC,UAAU,CAAC;EACtC,IAAID,iBAAiB,EAAE;IACnB9D,IAAI,CAACP,yBAAyB,GAAG,IAAI;EACzC;EACAO,IAAI,CAAC8C,EAAE,GAAGA,EAAE;EACZ9C,IAAI,CAAC6D,QAAQ,GAAGA,QAAQ;EACxB,MAAMG,iBAAiB,GAAIC,SAAS,IAAK;IACrCjE,IAAI,CAACiE,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD,SAASC,oBAAoBA,CAAClE,IAAI,EAAE;IAChC,OAAOA,IAAI,CAAC8C,EAAE,KAAK,IAAI,IAAI9C,IAAI,CAAC6D,QAAQ,KAAK,IAAI;EACrD;EACA,SAASM,gBAAgBA,CAACnE,IAAI,EAAE;IAC5B,IAAI,CAACkE,oBAAoB,CAAClE,IAAI,CAAC,EAAE;MAC7BuB,eAAe,CAACvB,IAAI,CAAC,CAAC,CAAC;MACvBA,IAAI,CAACiE,SAAS,CAAC,CAAC;MAChB;MACAjE,IAAI,CAAC8C,EAAE,GAAG,IAAI;MACd9C,IAAI,CAAC6D,QAAQ,GAAG,IAAI;MACpB7D,IAAI,CAACiE,SAAS,GAAGG,eAAe;IACpC;EACJ;EACA,MAAMC,GAAG,GAAGA,CAAA,KAAM;IACd,IAAIrE,IAAI,CAAC8C,EAAE,KAAK,IAAI,EAAE;MAClB;MACA;IACJ;IACA,IAAInE,qBAAqB,CAAC,CAAC,EAAE;MACzB,MAAM,IAAIsB,KAAK,CAAE,mEAAkE,CAAC;IACxF;IACAD,IAAI,CAACd,KAAK,GAAG,KAAK;IAClB,IAAIc,IAAI,CAACsE,MAAM,IAAI,CAAC1D,8BAA8B,CAACZ,IAAI,CAAC,EAAE;MACtD;IACJ;IACAA,IAAI,CAACsE,MAAM,GAAG,IAAI;IAClB,MAAMpD,YAAY,GAAGF,yBAAyB,CAAChB,IAAI,CAAC;IACpD,IAAI;MACAA,IAAI,CAACiE,SAAS,CAAC,CAAC;MAChBjE,IAAI,CAACiE,SAAS,GAAGG,eAAe;MAChCpE,IAAI,CAAC8C,EAAE,CAACkB,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJ/C,wBAAwB,CAACjB,IAAI,EAAEkB,YAAY,CAAC;IAChD;EACJ,CAAC;EACDlB,IAAI,CAACuE,GAAG,GAAG;IACPC,MAAM,EAAEA,CAAA,KAAM1D,iBAAiB,CAACd,IAAI,CAAC;IACrCqE,GAAG;IACHI,OAAO,EAAEA,CAAA,KAAMzE,IAAI,CAACiE,SAAS,CAAC,CAAC;IAC/BS,OAAO,EAAEA,CAAA,KAAMP,gBAAgB,CAACnE,IAAI,CAAC;IACrC,CAAC3B,MAAM,GAAG2B;EACd,CAAC;EACD,OAAOA,IAAI,CAACuE,GAAG;AACnB;AACA,MAAMH,eAAe,GAAGA,CAAA,KAAM,CAAE,CAAC;AACjC;AACA;AACA;AACA,MAAML,UAAU,GAAG,eAAgB,CAAC,MAAM;EACtC,OAAO;IACH,GAAGhF,aAAa;IAChBW,oBAAoB,EAAE,IAAI;IAC1BD,yBAAyB,EAAE,KAAK;IAChCI,mBAAmB,EAAGG,IAAI,IAAK;MAC3B,IAAIA,IAAI,CAAC6D,QAAQ,KAAK,IAAI,EAAE;QACxB7D,IAAI,CAAC6D,QAAQ,CAAC7D,IAAI,CAACuE,GAAG,CAAC;MAC3B;IACJ,CAAC;IACDD,MAAM,EAAE,KAAK;IACbL,SAAS,EAAEG;EACf,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAASO,uBAAuBA,CAACC,IAAI,EAAE;EACnC;AAAA;AAGJ,SAAS7F,aAAa,EAAEV,MAAM,EAAE4C,wBAAwB,EAAED,yBAAyB,EAAEO,eAAe,EAAEX,8BAA8B,EAAEiB,cAAc,EAAEmB,YAAY,EAAEY,WAAW,EAAE/F,aAAa,EAAEa,iBAAiB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEmB,gBAAgB,EAAEc,uBAAuB,EAAEF,0BAA0B,EAAEI,sBAAsB,EAAExC,iBAAiB,EAAEoG,uBAAuB,EAAEvB,kBAAkB,EAAEP,iCAAiC,EAAEa,cAAc,EAAEJ,WAAW,EAAEE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}