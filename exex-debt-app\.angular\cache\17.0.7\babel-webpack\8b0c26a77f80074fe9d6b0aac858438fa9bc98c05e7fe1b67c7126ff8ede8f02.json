{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class StatusBadgePipe {\n  transform(status) {\n    if (!status) return '';\n    const statusLower = status.toLowerCase();\n    let badgeClass = '';\n    let icon = '';\n    switch (statusLower) {\n      case 'active':\n        badgeClass = 'status-badge-active';\n        icon = 'pi pi-check-circle';\n        break;\n      case 'inactive':\n        badgeClass = 'status-badge-inactive';\n        icon = 'pi pi-times-circle';\n        break;\n      case 'pending':\n        badgeClass = 'status-badge-pending';\n        icon = 'pi pi-clock';\n        break;\n      case 'suspended':\n        badgeClass = 'status-badge-suspended';\n        icon = 'pi pi-ban';\n        break;\n      case 'vip':\n        badgeClass = 'status-badge-vip';\n        icon = 'pi pi-star-fill';\n        break;\n      default:\n        badgeClass = 'status-badge-default';\n        icon = 'pi pi-info-circle';\n    }\n    return `<span class=\"status-badge ${badgeClass}\">\n                    <i class=\"${icon}\"></i>\n                    <span class=\"status-text\">${status}</span>\n                </span>`;\n  }\n  static #_ = this.ɵfac = function StatusBadgePipe_Factory(t) {\n    return new (t || StatusBadgePipe)();\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"statusBadge\",\n    type: StatusBadgePipe,\n    pure: true\n  });\n}", "map": {"version": 3, "names": ["StatusBadgePipe", "transform", "status", "statusLower", "toLowerCase", "badgeClass", "icon", "_", "_2", "pure"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\pipes\\status-badge.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n    name: 'statusBadge'\n})\nexport class StatusBadgePipe implements PipeTransform {\n    transform(status: string): string {\n        if (!status) return '';\n        \n        const statusLower = status.toLowerCase();\n        let badgeClass = '';\n        let icon = '';\n        \n        switch (statusLower) {\n            case 'active':\n                badgeClass = 'status-badge-active';\n                icon = 'pi pi-check-circle';\n                break;\n            case 'inactive':\n                badgeClass = 'status-badge-inactive';\n                icon = 'pi pi-times-circle';\n                break;\n            case 'pending':\n                badgeClass = 'status-badge-pending';\n                icon = 'pi pi-clock';\n                break;\n            case 'suspended':\n                badgeClass = 'status-badge-suspended';\n                icon = 'pi pi-ban';\n                break;\n            case 'vip':\n                badgeClass = 'status-badge-vip';\n                icon = 'pi pi-star-fill';\n                break;\n            default:\n                badgeClass = 'status-badge-default';\n                icon = 'pi pi-info-circle';\n        }\n        \n        return `<span class=\"status-badge ${badgeClass}\">\n                    <i class=\"${icon}\"></i>\n                    <span class=\"status-text\">${status}</span>\n                </span>`;\n    }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EACxBC,SAASA,CAACC,MAAc;IACpB,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IAEtB,MAAMC,WAAW,GAAGD,MAAM,CAACE,WAAW,EAAE;IACxC,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,IAAI,GAAG,EAAE;IAEb,QAAQH,WAAW;MACf,KAAK,QAAQ;QACTE,UAAU,GAAG,qBAAqB;QAClCC,IAAI,GAAG,oBAAoB;QAC3B;MACJ,KAAK,UAAU;QACXD,UAAU,GAAG,uBAAuB;QACpCC,IAAI,GAAG,oBAAoB;QAC3B;MACJ,KAAK,SAAS;QACVD,UAAU,GAAG,sBAAsB;QACnCC,IAAI,GAAG,aAAa;QACpB;MACJ,KAAK,WAAW;QACZD,UAAU,GAAG,wBAAwB;QACrCC,IAAI,GAAG,WAAW;QAClB;MACJ,KAAK,KAAK;QACND,UAAU,GAAG,kBAAkB;QAC/BC,IAAI,GAAG,iBAAiB;QACxB;MACJ;QACID,UAAU,GAAG,sBAAsB;QACnCC,IAAI,GAAG,mBAAmB;;IAGlC,OAAO,6BAA6BD,UAAU;gCACtBC,IAAI;gDACYJ,MAAM;wBAC9B;EACpB;EAAC,QAAAK,CAAA,G;qBAtCQP,eAAe;EAAA;EAAA,QAAAQ,EAAA,G;;UAAfR,eAAe;IAAAS,IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}