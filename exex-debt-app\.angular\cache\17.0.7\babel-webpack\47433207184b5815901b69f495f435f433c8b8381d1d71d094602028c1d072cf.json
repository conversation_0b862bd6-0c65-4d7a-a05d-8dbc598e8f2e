{"ast": null, "code": "import { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { TranslocoModule } from '@jsverse/transloco';\nimport { BadgeModule } from 'primeng/badge';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { RippleModule } from 'primeng/ripple';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { AppFooterComponent } from './app.footer.component';\nimport { AppLayoutComponent } from './app.layout.component';\nimport { AppMenuComponent } from './app.menu.component';\nimport { AppMenuitemComponent } from './app.menuitem.component';\nimport { AppSidebarComponent } from './app.sidebar.component';\nimport { AppTopBarComponent } from './app.topbar.component';\nimport { AppConfigModule } from './config/config.module';\nimport { ImageModule } from 'primeng/image';\nimport * as i0 from \"@angular/core\";\nexport class AppLayoutModule {\n  static #_ = this.ɵfac = function AppLayoutModule_Factory(t) {\n    return new (t || AppLayoutModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppLayoutModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, RippleModule, RouterModule, AppConfigModule, TranslocoModule, ImageModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppLayoutModule, {\n    declarations: [AppMenuitemComponent, AppTopBarComponent, AppFooterComponent, AppMenuComponent, AppSidebarComponent, AppLayoutComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, RippleModule, RouterModule, AppConfigModule, TranslocoModule, ImageModule],\n    exports: [AppLayoutComponent]\n  });\n})();", "map": {"version": 3, "names": ["HttpClientModule", "FormsModule", "BrowserModule", "BrowserAnimationsModule", "RouterModule", "TranslocoModule", "BadgeModule", "InputSwitchModule", "InputTextModule", "RadioButtonModule", "RippleModule", "SidebarModule", "AppFooterComponent", "AppLayoutComponent", "AppMenuComponent", "AppMenuitemComponent", "AppSidebarComponent", "AppTopBarComponent", "AppConfigModule", "ImageModule", "AppLayoutModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.layout.module.ts"], "sourcesContent": ["import { HttpClientModule } from '@angular/common/http';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { RouterModule } from '@angular/router';\r\nimport { TranslocoModule } from '@jsverse/transloco';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { AppFooterComponent } from './app.footer.component';\r\nimport { AppLayoutComponent } from './app.layout.component';\r\nimport { AppMenuComponent } from './app.menu.component';\r\nimport { AppMenuitemComponent } from './app.menuitem.component';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AppTopBarComponent } from './app.topbar.component';\r\nimport { AppConfigModule } from './config/config.module';\r\nimport { ImageModule } from 'primeng/image';\r\n\r\n@NgModule({\r\n    declarations: [\r\n        AppMenuitemComponent,\r\n        AppTopBarComponent,\r\n        AppFooterComponent,\r\n        AppMenuComponent,\r\n        AppSidebarComponent,\r\n        AppLayoutComponent,\r\n    ],\r\n    imports: [\r\n        BrowserModule,\r\n        FormsModule,\r\n        HttpClientModule,\r\n        BrowserAnimationsModule,\r\n        InputTextModule,\r\n        SidebarModule,\r\n        BadgeModule,\r\n        RadioButtonModule,\r\n        InputSwitchModule,\r\n        RippleModule,\r\n        RouterModule,\r\n        AppConfigModule,\r\n        TranslocoModule,\r\n        ImageModule\r\n    ],\r\n    exports: [AppLayoutComponent],\r\n})\r\nexport class AppLayoutModule {}\r\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,WAAW,QAAQ,eAAe;;AA6B3C,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAjBpBrB,aAAa,EACbD,WAAW,EACXD,gBAAgB,EAChBG,uBAAuB,EACvBK,eAAe,EACfG,aAAa,EACbL,WAAW,EACXG,iBAAiB,EACjBF,iBAAiB,EACjBG,YAAY,EACZN,YAAY,EACZc,eAAe,EACfb,eAAe,EACfc,WAAW;EAAA;;;2EAINC,eAAe;IAAAI,YAAA,GAzBpBT,oBAAoB,EACpBE,kBAAkB,EAClBL,kBAAkB,EAClBE,gBAAgB,EAChBE,mBAAmB,EACnBH,kBAAkB;IAAAY,OAAA,GAGlBvB,aAAa,EACbD,WAAW,EACXD,gBAAgB,EAChBG,uBAAuB,EACvBK,eAAe,EACfG,aAAa,EACbL,WAAW,EACXG,iBAAiB,EACjBF,iBAAiB,EACjBG,YAAY,EACZN,YAAY,EACZc,eAAe,EACfb,eAAe,EACfc,WAAW;IAAAO,OAAA,GAELb,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}