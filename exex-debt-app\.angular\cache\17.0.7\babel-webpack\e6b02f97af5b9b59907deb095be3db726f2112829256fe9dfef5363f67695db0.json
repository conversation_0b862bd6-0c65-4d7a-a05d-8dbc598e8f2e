{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/service/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/avatar\";\nimport * as i4 from \"primeng/skeleton\";\nfunction UserComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelement(2, \"p-skeleton\", 2);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵelement(4, \"p-skeleton\", 3)(5, \"p-skeleton\", 4)(6, \"p-skeleton\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"p-skeleton\", 6);\n    i0.ɵɵelementStart(8, \"div\", 7);\n    i0.ɵɵelement(9, \"p-skeleton\", 8)(10, \"p-skeleton\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UserComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵelement(2, \"p-avatar\", 10);\n    i0.ɵɵelementStart(3, \"section\", 11)(4, \"h3\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Phone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Website:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \"\\u00A0\");\n    i0.ɵɵelementStart(17, \"a\", 15);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n    i0.ɵɵtext(21, \"Company:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25, \"Catchphrase:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n    i0.ɵɵtext(29, \"Business:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\")(32, \"strong\");\n    i0.ɵɵtext(33, \"Street:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\")(36, \"strong\");\n    i0.ɵɵtext(37, \"City:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\")(40, \"strong\");\n    i0.ɵɵtext(41, \"Zipcode:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.user.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.user.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.phone, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"https://\", ctx_r1.user.website, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.user.website);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.company.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.company.catchPhrase, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.company.bs, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.address.street, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.address.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.address.zipcode, \"\");\n  }\n}\nexport class UserComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.isShowSkeleton = true;\n  }\n  ngOnInit() {\n    this.authService.mockUser().subscribe(res => {\n      this.user = res[0];\n      this.isShowSkeleton = false;\n    });\n  }\n  static #_ = this.ɵfac = function UserComponent_Factory(t) {\n    return new (t || UserComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserComponent,\n    selectors: [[\"app-user\"]],\n    decls: 2,\n    vars: 2,\n    consts: [[4, \"ngIf\"], [1, \"flex\", \"mb-3\"], [\"shape\", \"circle\", \"size\", \"4rem\", \"styleClass\", \"mr-2\"], [\"width\", \"10rem\", \"styleClass\", \"mb-2\"], [\"width\", \"5rem\", \"styleClass\", \"mb-2\"], [\"height\", \".5rem\"], [\"width\", \"100%\", \"height\", \"150px\"], [1, \"flex\", \"justify-content-between\", \"mt-3\"], [\"width\", \"4rem\", \"height\", \"2rem\"], [1, \"flex\", \"items-center\", \"gap-3\", \"mb-3\"], [\"icon\", \"pi pi-user\", \"image\", \"assets/user-avatar.png\", \"shape\", \"circle\", \"size\", \"xlarge\", 1, \"custom-avatar\"], [1, \"align-content-center\"], [1, \"text-lg\", \"font-bold\", \"m-0\"], [1, \"text-gray-500\"], [1, \"grid-items\"], [\"target\", \"_blank\", 3, \"href\"]],\n    template: function UserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, UserComponent_ng_container_0_Template, 11, 0, \"ng-container\", 0)(1, UserComponent_ng_container_1_Template, 43, 11, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isShowSkeleton);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isShowSkeleton);\n      }\n    },\n    dependencies: [i2.NgIf, i3.Avatar, i4.Skeleton],\n    styles: [\".grid-items[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n}\\n@media (max-width: 739px) {\\n  .grid-items[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(1, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2Rhc2hib2FyZC91c2VyL3VzZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EscUNBQUE7QUFDSjtBQUNJO0VBSko7SUFLUSxxQ0FBQTtFQUVOO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuZ3JpZC1pdGVtcyB7XHJcbiAgICBkaXNwbGF5OiBncmlkO1xyXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTtcclxuXHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzM5cHgpIHtcclxuICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxLCAxZnIpO1xyXG4gICAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "user", "name", "email", "ɵɵtextInterpolate1", "phone", "ɵɵpropertyInterpolate1", "website", "ɵɵsanitizeUrl", "company", "catchPhrase", "bs", "address", "street", "city", "zipcode", "UserComponent", "constructor", "authService", "isShowSkeleton", "ngOnInit", "mockUser", "subscribe", "res", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "UserComponent_Template", "rf", "ctx", "ɵɵtemplate", "UserComponent_ng_container_0_Template", "UserComponent_ng_container_1_Template", "ɵɵproperty"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AuthService } from 'src/app/core/service/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-user',\r\n    templateUrl: './user.component.html',\r\n    styleUrls: ['./user.component.scss'],\r\n})\r\nexport class UserComponent implements OnInit {\r\n    user: any;\r\n    isShowSkeleton: boolean = true;\r\n    constructor(private authService: AuthService) {}\r\n\r\n    ngOnInit(): void {\r\n        this.authService.mockUser().subscribe((res) => {\r\n            this.user = res[0];\r\n            this.isShowSkeleton = false;\r\n        });\r\n    }\r\n}\r\n", "<ng-container *ngIf=\"isShowSkeleton\">\r\n    <div class=\"flex mb-3\">\r\n        <p-skeleton shape=\"circle\" size=\"4rem\" styleClass=\"mr-2\" />\r\n        <div>\r\n            <p-skeleton width=\"10rem\" styleClass=\"mb-2\" />\r\n            <p-skeleton width=\"5rem\" styleClass=\"mb-2\" />\r\n            <p-skeleton height=\".5rem\" />\r\n        </div>\r\n    </div>\r\n    <p-skeleton width=\"100%\" height=\"150px\" />\r\n    <div class=\"flex justify-content-between mt-3\">\r\n        <p-skeleton width=\"4rem\" height=\"2rem\" />\r\n        <p-skeleton width=\"4rem\" height=\"2rem\" />\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"!isShowSkeleton\">\r\n    <div class=\"flex items-center gap-3 mb-3\">\r\n        <p-avatar\r\n            class=\"custom-avatar\"\r\n            icon=\"pi pi-user\"\r\n            image=\"assets/user-avatar.png\"\r\n            shape=\"circle\"\r\n            size=\"xlarge\"></p-avatar>\r\n        <section class=\"align-content-center\">\r\n            <h3 class=\"text-lg font-bold m-0\">{{ user.name }}</h3>\r\n            <p class=\"text-gray-500\">{{ user.email }}</p>\r\n        </section>\r\n    </div>\r\n\r\n    <div class=\"grid-items\">\r\n        <p><strong>Phone:</strong> {{ user.phone }}</p>\r\n        <p>\r\n            <strong>Website:</strong>&nbsp;<a href=\"https://{{ user.website }}\" target=\"_blank\">{{ user.website }}</a>\r\n        </p>\r\n        <p><strong>Company:</strong> {{ user.company.name }}</p>\r\n        <p><strong>Catchphrase:</strong> {{ user.company.catchPhrase }}</p>\r\n        <p><strong>Business:</strong> {{ user.company.bs }}</p>\r\n\r\n        <p><strong>Street:</strong> {{ user.address.street }}</p>\r\n        <p><strong>City:</strong> {{ user.address.city }}</p>\r\n        <p><strong>Zipcode:</strong> {{ user.address.zipcode }}</p>\r\n    </div>\r\n</ng-container>\r\n"], "mappings": ";;;;;;;ICAAA,EAAA,CAAAC,uBAAA,GAAqC;IACjCD,EAAA,CAAAE,cAAA,aAAuB;IACnBF,EAAA,CAAAG,SAAA,oBAA2D;IAC3DH,EAAA,CAAAE,cAAA,UAAK;IACDF,EAAA,CAAAG,SAAA,oBAA8C;IAGlDH,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAG,SAAA,oBAA0C;IAC1CH,EAAA,CAAAE,cAAA,aAA+C;IAC3CF,EAAA,CAAAG,SAAA,oBAAyC;IAE7CH,EAAA,CAAAI,YAAA,EAAM;IACVJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IAEfL,EAAA,CAAAC,uBAAA,GAAsC;IAClCD,EAAA,CAAAE,cAAA,aAA0C;IACtCF,EAAA,CAAAG,SAAA,mBAK6B;IAC7BH,EAAA,CAAAE,cAAA,kBAAsC;IACAF,EAAA,CAAAM,MAAA,GAAe;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACtDJ,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAM,MAAA,GAAgB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAIrDJ,EAAA,CAAAE,cAAA,cAAwB;IACTF,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAAgB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAC/CJ,EAAA,CAAAE,cAAA,SAAG;IACSF,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAAAJ,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAE,cAAA,aAAqD;IAAAF,EAAA,CAAAM,MAAA,IAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAE9GJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAAuB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACxDJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,oBAAY;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAA8B;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACnEJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,iBAAS;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAAqB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAEvDJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAAyB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACzDJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAAuB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACrDJ,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAM,MAAA,IAA0B;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EAAA,CAAAK,qBAAA,EAAe;;;;IAlB+BL,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,IAAA,CAAe;IACxBX,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAE,KAAA,CAAgB;IAKlBZ,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAI,KAAA,KAAgB;IAELd,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAe,sBAAA,qBAAAN,MAAA,CAAAC,IAAA,CAAAM,OAAA,MAAAhB,EAAA,CAAAiB,aAAA,CAAiC;IAAiBjB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAM,OAAA,CAAkB;IAE7EhB,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAQ,OAAA,CAAAP,IAAA,KAAuB;IACnBX,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAQ,OAAA,CAAAC,WAAA,KAA8B;IACjCnB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAQ,OAAA,CAAAE,EAAA,KAAqB;IAEvBpB,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAW,OAAA,CAAAC,MAAA,KAAyB;IAC3BtB,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAW,OAAA,CAAAE,IAAA,KAAuB;IACpBvB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAa,kBAAA,MAAAJ,MAAA,CAAAC,IAAA,CAAAW,OAAA,CAAAG,OAAA,KAA0B;;;ADjC/D,OAAM,MAAOC,aAAa;EAGtBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAD/B,KAAAC,cAAc,GAAY,IAAI;EACiB;EAE/CC,QAAQA,CAAA;IACJ,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAI;MAC1C,IAAI,CAACtB,IAAI,GAAGsB,GAAG,CAAC,CAAC,CAAC;MAClB,IAAI,CAACJ,cAAc,GAAG,KAAK;IAC/B,CAAC,CAAC;EACN;EAAC,QAAAK,CAAA,G;qBAVQR,aAAa,EAAAzB,EAAA,CAAAkC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAbZ,aAAa;IAAAa,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR1B5C,EAAA,CAAA8C,UAAA,IAAAC,qCAAA,2BAce,IAAAC,qCAAA;;;QAdAhD,EAAA,CAAAiD,UAAA,SAAAJ,GAAA,CAAAjB,cAAA,CAAoB;QAgBpB5B,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAiD,UAAA,UAAAJ,GAAA,CAAAjB,cAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}