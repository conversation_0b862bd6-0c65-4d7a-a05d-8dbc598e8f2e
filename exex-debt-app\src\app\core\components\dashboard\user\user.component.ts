import { Component, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth.service';

@Component({
    selector: 'app-user',
    templateUrl: './user.component.html',
    styleUrls: ['./user.component.scss'],
})
export class UserComponent implements OnInit {
    user: any;
    isShowSkeleton: boolean = true;
    constructor(private authService: AuthService) {}

    ngOnInit(): void {
        this.authService.mockUser().subscribe((res) => {
            this.user = res[0];
            this.isShowSkeleton = false;
        });
    }
}
