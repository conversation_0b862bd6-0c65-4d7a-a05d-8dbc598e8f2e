{"ast": null, "code": "import { inject } from \"@angular/core\";\nimport { HttpClient } from \"@angular/common/http\";\nimport * as i0 from \"@angular/core\";\nexport class TranslocoHttpLoader {\n  constructor() {\n    this.http = inject(HttpClient);\n  }\n  getTranslation(lang) {\n    return this.http.get(`/assets/i18n/${lang}.json`);\n  }\n  static #_ = this.ɵfac = function TranslocoHttpLoader_Factory(t) {\n    return new (t || TranslocoHttpLoader)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TranslocoHttpLoader,\n    factory: TranslocoHttpLoader.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "HttpClient", "TranslocoHttpLoader", "constructor", "http", "getTranslation", "lang", "get", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\transloco-loader.ts"], "sourcesContent": ["import { inject, Injectable } from \"@angular/core\";\nimport { Translation, TranslocoLoader } from \"@jsverse/transloco\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { environment } from \"src/environments/environment\";\n\n@Injectable({ providedIn: 'root' })\nexport class TranslocoHttpLoader implements TranslocoLoader {\n    private http = inject(HttpClient);\n\n    getTranslation(lang: string) {\n        return this.http.get<Translation>(`/assets/i18n/${lang}.json`);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAoB,eAAe;AAElD,SAASC,UAAU,QAAQ,sBAAsB;;AAIjD,OAAM,MAAOC,mBAAmB;EADhCC,YAAA;IAEY,KAAAC,IAAI,GAAGJ,MAAM,CAACC,UAAU,CAAC;;EAEjCI,cAAcA,CAACC,IAAY;IACvB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAc,gBAAgBD,IAAI,OAAO,CAAC;EAClE;EAAC,QAAAE,CAAA,G;qBALQN,mBAAmB;EAAA;EAAA,QAAAO,EAAA,G;WAAnBP,mBAAmB;IAAAQ,OAAA,EAAnBR,mBAAmB,CAAAS,IAAA;IAAAC,UAAA,EADN;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}