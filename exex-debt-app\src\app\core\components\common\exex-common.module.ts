import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { FileUploadModule } from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { KeyFilterModule } from 'primeng/keyfilter';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ToolbarModule } from 'primeng/toolbar';
import { MoneyPipe } from '../../pipes/money.pipe';
import { TextAlignPipe } from '../../pipes/text-align.pipe';
import { ExexTableComponent } from './exex-table/exex-table.component';

@NgModule({
    declarations: [ExexTableComponent],
    exports: [ExexTableComponent],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        ToastModule,
        ToolbarModule,
        TableModule,
        DialogModule,
        ConfirmDialogModule,
        InputTextModule,
        FileUploadModule,
        InputTextareaModule,
        KeyFilterModule,
        ButtonModule,
        TextAlignPipe,
        MoneyPipe,
    ],
    providers: [MessageService, ConfirmationService],
})
export class ExexCommonModule {}
