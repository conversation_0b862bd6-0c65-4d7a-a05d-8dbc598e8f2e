{"ast": null, "code": "export var Auth;\n(function (Auth) {\n  Auth[\"ACCESS_TOKEN\"] = \"access_token\";\n  Auth[\"REFRESH_TOKEN\"] = \"refresh_token\";\n})(Auth || (Auth = {}));", "map": {"version": 3, "names": ["<PERSON><PERSON>"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\enums\\auth.enum.ts"], "sourcesContent": ["export enum Auth {\n    ACCESS_TOKEN = 'access_token',\n    REFRESH_TOKEN = 'refresh_token'\n}\n"], "mappings": "AAAA,WAAYA,IAGX;AAHD,WAAYA,IAAI;EACZA,IAAA,iCAA6B;EAC7BA,IAAA,mCAA+B;AACnC,CAAC,EAHWA,IAAI,KAAJA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}