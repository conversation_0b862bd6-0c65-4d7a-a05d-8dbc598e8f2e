{"name": "dashboard", "version": "17.0.0", "scripts": {"ng": "ng", "start": "npm run format && ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "watch": "ng build --watch --configuration development", "format": "prettier --write \"**/*.{js,mjs,ts,mts,d.ts,html}\" --cache"}, "private": true, "dependencies": {"@angular/animations": "^17.0.5", "@angular/cdk": "^17.0.2", "@angular/common": "^17.0.5", "@angular/compiler": "^17.0.5", "@angular/core": "^17.0.5", "@angular/forms": "^17.0.5", "@angular/platform-browser": "^17.0.5", "@angular/platform-browser-dynamic": "^17.0.5", "@angular/router": "^17.0.5", "@fullcalendar/angular": "^6.0.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/timegrid": "^6.0.3", "@jsverse/transloco": "^7.4.1", "chart.js": "^3.3.2", "ngx-spinner": "^17.0.0", "npm": "^11.2.0", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "17.2.0", "prismjs": "^1.29.0", "quill": "^1.3.7", "rxjs": "~7.8.1", "tslib": "^2.5.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.5", "@angular/cli": "^17.0.5", "@angular/compiler-cli": "^17.0.5", "@types/jasmine": "~4.3.1", "eslint-plugin-prettier": "^5.2.3", "jasmine-core": "~4.6.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "3.5.3", "typescript": "~5.2.2"}}