{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet UserComponent = class UserComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.isShowSkeleton = true;\n  }\n  ngOnInit() {\n    this.authService.mockUser().subscribe(res => {\n      this.user = res[0];\n      this.isShowSkeleton = false;\n    });\n  }\n};\nUserComponent = __decorate([Component({\n  selector: 'app-user',\n  templateUrl: './user.component.html',\n  styleUrls: ['./user.component.scss']\n})], UserComponent);\nexport { UserComponent };", "map": {"version": 3, "names": ["Component", "UserComponent", "constructor", "authService", "isShowSkeleton", "ngOnInit", "mockUser", "subscribe", "res", "user", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AuthService } from 'src/app/core/service/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-user',\r\n    templateUrl: './user.component.html',\r\n    styleUrls: ['./user.component.scss'],\r\n})\r\nexport class UserComponent implements OnInit {\r\n    user: any;\r\n    isShowSkeleton: boolean = true;\r\n    constructor(private authService: AuthService) {}\r\n\r\n    ngOnInit(): void {\r\n        this.authService.mockUser().subscribe((res) => {\r\n            this.user = res[0];\r\n            this.isShowSkeleton = false;\r\n        });\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAQ1C,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAGtBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAD/B,KAAAC,cAAc,GAAY,IAAI;EACiB;EAE/CC,QAAQA,CAAA;IACJ,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAI;MAC1C,IAAI,CAACC,IAAI,GAAGD,GAAG,CAAC,CAAC,CAAC;MAClB,IAAI,CAACJ,cAAc,GAAG,KAAK;IAC/B,CAAC,CAAC;EACN;CACH;AAXYH,aAAa,GAAAS,UAAA,EALzBV,SAAS,CAAC;EACPW,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB;CACtC,CAAC,C,EACWZ,aAAa,CAWzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}