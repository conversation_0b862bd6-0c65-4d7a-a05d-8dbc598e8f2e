{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CustomerService {\n  constructor() {}\n  getCustomerData() {\n    const statuses = ['Active', 'Inactive', 'Pending', 'Suspended', 'VIP'];\n    const addresses = ['HCM', '<PERSON><PERSON>', '<PERSON> Nang', '<PERSON> Tho', '<PERSON>'];\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        customerId: 'COD' + (i + 1).toString().padStart(3, '0'),\n        customerName: 'Customer ' + (i + 1),\n        phoneNumber: '0398765' + (400 + i).toString().padStart(3, '0'),\n        email: 'customer' + (i + 1) + '@gmail.com',\n        address: addresses[i % addresses.length],\n        status: statuses[i % statuses.length],\n        creditLimit: (i + 1) * 100000,\n        currentBalance: 4900 + i * 150,\n        currencyId: 'VND'\n      };\n    });\n    return mockData;\n  }\n  getInvoiceData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        invoiceId: 'Inv' + i,\n        customerId: 'Customer' + i,\n        invoiceDate: '02/02/2025',\n        dueDate: '02/02/2025',\n        totalAmount: 4900 + i,\n        paidAmount: 2900 + i,\n        remainingAmount: 1000000,\n        status: 'Active',\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getCustomers() {\n    return Promise.resolve(this.getCustomerData());\n  }\n  getInvoices() {\n    return Promise.resolve(this.getInvoiceData());\n  }\n  static #_ = this.ɵfac = function CustomerService_Factory(t) {\n    return new (t || CustomerService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomerService,\n    factory: CustomerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["CustomerService", "constructor", "getCustomerData", "statuses", "addresses", "mockData", "Array", "from", "length", "map", "_", "i", "id", "customerId", "toString", "padStart", "customerName", "phoneNumber", "email", "address", "status", "creditLimit", "currentBalance", "currencyId", "getInvoiceData", "invoiceId", "invoiceDate", "dueDate", "totalAmount", "paidAmount", "remainingAmount", "getCustomers", "Promise", "resolve", "getInvoices", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\customer.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n    constructor() {}\r\n\r\n    getCustomerData() {\r\n        const statuses = ['Active', 'Inactive', 'Pending', 'Suspended', 'VIP'];\r\n        const addresses = ['HCM', '<PERSON><PERSON>', '<PERSON> Nang', '<PERSON> Tho', 'Hai Phong'];\r\n\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                customerId: 'COD' + (i + 1).toString().padStart(3, '0'),\r\n                customerName: 'Customer ' + (i + 1),\r\n                phoneNumber: '0398765' + (400 + i).toString().padStart(3, '0'),\r\n                email: 'customer' + (i + 1) + '@gmail.com',\r\n                address: addresses[i % addresses.length],\r\n                status: statuses[i % statuses.length],\r\n                creditLimit: (i + 1) * 100000,\r\n                currentBalance: 4900 + (i * 150),\r\n                currencyId: 'VND',\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getInvoiceData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                invoiceId: 'Inv' + i,\r\n                customerId: 'Customer' + i,\r\n                invoiceDate: '02/02/2025',\r\n                dueDate: '02/02/2025',\r\n                totalAmount: 4900 + i,\r\n                paidAmount: 2900 + i,\r\n                remainingAmount: 1000000,\r\n                status: 'Active',\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getCustomers() {\r\n        return Promise.resolve(this.getCustomerData());\r\n    }\r\n\r\n    getInvoices() {\r\n        return Promise.resolve(this.getInvoiceData());\r\n    }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EACxBC,YAAA,GAAe;EAEfC,eAAeA,CAAA;IACX,MAAMC,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC;IACtE,MAAMC,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;IAErE,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLE,UAAU,EAAE,KAAK,GAAG,CAACF,CAAC,GAAG,CAAC,EAAEG,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACvDC,YAAY,EAAE,WAAW,IAAIL,CAAC,GAAG,CAAC,CAAC;QACnCM,WAAW,EAAE,SAAS,GAAG,CAAC,GAAG,GAAGN,CAAC,EAAEG,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC9DG,KAAK,EAAE,UAAU,IAAIP,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;QAC1CQ,OAAO,EAAEf,SAAS,CAACO,CAAC,GAAGP,SAAS,CAACI,MAAM,CAAC;QACxCY,MAAM,EAAEjB,QAAQ,CAACQ,CAAC,GAAGR,QAAQ,CAACK,MAAM,CAAC;QACrCa,WAAW,EAAE,CAACV,CAAC,GAAG,CAAC,IAAI,MAAM;QAC7BW,cAAc,EAAE,IAAI,GAAIX,CAAC,GAAG,GAAI;QAChCY,UAAU,EAAE;OACf;IACL,CAAC,CAAC;IAEF,OAAOlB,QAAQ;EACnB;EAEAmB,cAAcA,CAAA;IACV,MAAMnB,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLc,SAAS,EAAE,KAAK,GAAGd,CAAC;QACpBE,UAAU,EAAE,UAAU,GAAGF,CAAC;QAC1Be,WAAW,EAAE,YAAY;QACzBC,OAAO,EAAE,YAAY;QACrBC,WAAW,EAAE,IAAI,GAAGjB,CAAC;QACrBkB,UAAU,EAAE,IAAI,GAAGlB,CAAC;QACpBmB,eAAe,EAAE,OAAO;QACxBV,MAAM,EAAE,QAAQ;QAChBG,UAAU,EAAE,KAAK,GAAGZ;OACvB;IACL,CAAC,CAAC;IAEF,OAAON,QAAQ;EACnB;EAEA0B,YAAYA,CAAA;IACR,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC/B,eAAe,EAAE,CAAC;EAClD;EAEAgC,WAAWA,CAAA;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACT,cAAc,EAAE,CAAC;EACjD;EAAC,QAAAd,CAAA,G;qBAlDQV,eAAe;EAAA;EAAA,QAAAmC,EAAA,G;WAAfnC,eAAe;IAAAoC,OAAA,EAAfpC,eAAe,CAAAqC,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}