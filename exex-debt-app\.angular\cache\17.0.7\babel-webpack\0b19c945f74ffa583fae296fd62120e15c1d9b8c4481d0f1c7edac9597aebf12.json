{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nexport class OperatorSubscriber extends Subscriber {\n  constructor(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n    super(destination);\n    this.onFinalize = onFinalize;\n    this.shouldUnsubscribe = shouldUnsubscribe;\n    this._next = onNext ? function (value) {\n      try {\n        onNext(value);\n      } catch (err) {\n        destination.error(err);\n      }\n    } : super._next;\n    this._error = onError ? function (err) {\n      try {\n        onError(err);\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : super._error;\n    this._complete = onComplete ? function () {\n      try {\n        onComplete();\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : super._complete;\n  }\n  unsubscribe() {\n    var _a;\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      const {\n        closed\n      } = this;\n      super.unsubscribe();\n      !closed && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n    }\n  }\n}", "map": {"version": 3, "names": ["Subscriber", "createOperatorSubscriber", "destination", "onNext", "onComplete", "onError", "onFinalize", "OperatorSubscriber", "constructor", "shouldUnsubscribe", "_next", "value", "err", "error", "_error", "unsubscribe", "_complete", "_a", "closed", "call"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/rxjs/dist/esm/internal/operators/OperatorSubscriber.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n    return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nexport class OperatorSubscriber extends Subscriber {\n    constructor(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n        super(destination);\n        this.onFinalize = onFinalize;\n        this.shouldUnsubscribe = shouldUnsubscribe;\n        this._next = onNext\n            ? function (value) {\n                try {\n                    onNext(value);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n            }\n            : super._next;\n        this._error = onError\n            ? function (err) {\n                try {\n                    onError(err);\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : super._error;\n        this._complete = onComplete\n            ? function () {\n                try {\n                    onComplete();\n                }\n                catch (err) {\n                    destination.error(err);\n                }\n                finally {\n                    this.unsubscribe();\n                }\n            }\n            : super._complete;\n    }\n    unsubscribe() {\n        var _a;\n        if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n            const { closed } = this;\n            super.unsubscribe();\n            !closed && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,wBAAwBA,CAACC,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAC3F,OAAO,IAAIC,kBAAkB,CAACL,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,CAAC;AACvF;AACA,OAAO,MAAMC,kBAAkB,SAASP,UAAU,CAAC;EAC/CQ,WAAWA,CAACN,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEG,iBAAiB,EAAE;IACjF,KAAK,CAACP,WAAW,CAAC;IAClB,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACG,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,KAAK,GAAGP,MAAM,GACb,UAAUQ,KAAK,EAAE;MACf,IAAI;QACAR,MAAM,CAACQ,KAAK,CAAC;MACjB,CAAC,CACD,OAAOC,GAAG,EAAE;QACRV,WAAW,CAACW,KAAK,CAACD,GAAG,CAAC;MAC1B;IACJ,CAAC,GACC,KAAK,CAACF,KAAK;IACjB,IAAI,CAACI,MAAM,GAAGT,OAAO,GACf,UAAUO,GAAG,EAAE;MACb,IAAI;QACAP,OAAO,CAACO,GAAG,CAAC;MAChB,CAAC,CACD,OAAOA,GAAG,EAAE;QACRV,WAAW,CAACW,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACG,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACC,KAAK,CAACD,MAAM;IAClB,IAAI,CAACE,SAAS,GAAGZ,UAAU,GACrB,YAAY;MACV,IAAI;QACAA,UAAU,CAAC,CAAC;MAChB,CAAC,CACD,OAAOQ,GAAG,EAAE;QACRV,WAAW,CAACW,KAAK,CAACD,GAAG,CAAC;MAC1B,CAAC,SACO;QACJ,IAAI,CAACG,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,GACC,KAAK,CAACC,SAAS;EACzB;EACAD,WAAWA,CAAA,EAAG;IACV,IAAIE,EAAE;IACN,IAAI,CAAC,IAAI,CAACR,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC,EAAE;MACrD,MAAM;QAAES;MAAO,CAAC,GAAG,IAAI;MACvB,KAAK,CAACH,WAAW,CAAC,CAAC;MACnB,CAACG,MAAM,KAAK,CAACD,EAAE,GAAG,IAAI,CAACX,UAAU,MAAM,IAAI,IAAIW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1F;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}