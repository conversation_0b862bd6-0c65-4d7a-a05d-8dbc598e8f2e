<ng-container *ngIf="isShowSkeleton">
    <div class="flex mb-3">
        <p-skeleton shape="circle" size="4rem" styleClass="mr-2" />
        <div>
            <p-skeleton width="10rem" styleClass="mb-2" />
            <p-skeleton width="5rem" styleClass="mb-2" />
            <p-skeleton height=".5rem" />
        </div>
    </div>
    <p-skeleton width="100%" height="150px" />
    <div class="flex justify-content-between mt-3">
        <p-skeleton width="4rem" height="2rem" />
        <p-skeleton width="4rem" height="2rem" />
    </div>
</ng-container>

<ng-container *ngIf="!isShowSkeleton">
    <div class="flex items-center gap-3 mb-3">
        <p-avatar
            class="custom-avatar"
            icon="pi pi-user"
            image="assets/user-avatar.png"
            shape="circle"
            size="xlarge"></p-avatar>
        <section class="align-content-center">
            <h3 class="text-lg font-bold m-0">{{ user.name }}</h3>
            <p class="text-gray-500">{{ user.email }}</p>
        </section>
    </div>

    <div class="grid-items">
        <p><strong>Phone:</strong> {{ user.phone }}</p>
        <p>
            <strong>Website:</strong>&nbsp;<a href="https://{{ user.website }}" target="_blank">{{ user.website }}</a>
        </p>
        <p><strong>Company:</strong> {{ user.company.name }}</p>
        <p><strong>Catchphrase:</strong> {{ user.company.catchPhrase }}</p>
        <p><strong>Business:</strong> {{ user.company.bs }}</p>

        <p><strong>Street:</strong> {{ user.address.street }}</p>
        <p><strong>City:</strong> {{ user.address.city }}</p>
        <p><strong>Zipcode:</strong> {{ user.address.zipcode }}</p>
    </div>
</ng-container>
