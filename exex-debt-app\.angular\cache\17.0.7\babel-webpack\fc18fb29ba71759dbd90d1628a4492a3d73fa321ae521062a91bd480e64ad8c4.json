{"ast": null, "code": "//ng generate interceptor my-interceptor --skip-tests\nimport { inject } from '@angular/core';\nimport { AuthService } from '../service/auth.service';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    const authRequest = request.clone({\n      headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`)\n    });\n    return next.handle(authRequest);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["inject", "AuthService", "<PERSON><PERSON>", "AuthInterceptor", "constructor", "authService", "intercept", "request", "next", "authRequest", "clone", "headers", "set", "localStorage", "getItem", "ACCESS_TOKEN", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["//ng generate interceptor my-interceptor --skip-tests\n\nimport { inject, Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n} from '@angular/common/http';\nimport { AuthService } from '../service/auth.service';\nimport { Auth } from '../enums/auth.enum';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  authService = inject(AuthService);\n  intercept(request: HttpRequest<any>, next: HttpHandler) {\n    const authRequest = request.clone({\n      headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`),\n    });\n    return next.handle(authRequest);\n  }\n}"], "mappings": "AAAA;AAEA,SAASA,MAAM,QAAoB,eAAe;AAMlD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,IAAI,QAAQ,oBAAoB;;AAGzC,OAAM,MAAOC,eAAe;EAD5BC,YAAA;IAEE,KAAAC,WAAW,GAAGL,MAAM,CAACC,WAAW,CAAC;;EACjCK,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,MAAMC,WAAW,GAAGF,OAAO,CAACG,KAAK,CAAC;MAChCC,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACa,YAAY,CAAC,EAAE;KAClG,CAAC;IACF,OAAOP,IAAI,CAACQ,MAAM,CAACP,WAAW,CAAC;EACjC;EAAC,QAAAQ,CAAA,G;qBAPUd,eAAe;EAAA;EAAA,QAAAe,EAAA,G;WAAff,eAAe;IAAAgB,OAAA,EAAfhB,eAAe,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}