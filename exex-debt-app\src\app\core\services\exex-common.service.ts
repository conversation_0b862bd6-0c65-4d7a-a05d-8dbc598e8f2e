import { Injectable } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';

@Injectable({
    providedIn: 'root',
})
export class ExexCommonService {
    constructor(
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
    ) {}

    showToastSuccess(detail: string) {
        this.messageService.add({ severity: 'success', summary: 'Successful', detail });
    }

    showToastError(detail: string) {
        this.messageService.add({ severity: 'error', summary: 'Error', detail });
    }

    showToastInfo(detail: string) {
        this.messageService.add({ severity: 'info', summary: 'Info', detail });
    }

    showToastWarning(detail: string) {
        this.messageService.add({ severity: 'warn', summary: 'Warn', detail });
    }

    showDialogConfirm(acceptCallback?: () => void, rejectCallback?: () => void) {
        this.confirmationService.confirm({
            message: 'Are you sure you want to proceed with this action?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                if (acceptCallback) acceptCallback();
            },
            reject: () => {
                if (rejectCallback) rejectCallback();
            },
        });
    }
}
