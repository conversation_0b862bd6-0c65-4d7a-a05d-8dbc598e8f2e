{"ast": null, "code": "import { BehaviorSubject, fromEvent } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class WindowSizeService {\n  constructor() {\n    this.heightSubject = new BehaviorSubject(window.innerHeight);\n    this.height$ = this.heightSubject.asObservable();\n    fromEvent(window, 'resize').subscribe(() => {\n      this.heightSubject.next(window.innerHeight);\n    });\n  }\n  getHeight() {\n    return this.heightSubject.value;\n  }\n  static #_ = this.ɵfac = function WindowSizeService_Factory(t) {\n    return new (t || WindowSizeService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: WindowSizeService,\n    factory: WindowSizeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "fromEvent", "WindowSizeService", "constructor", "heightSubject", "window", "innerHeight", "height$", "asObservable", "subscribe", "next", "getHeight", "value", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\service\\window-size.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, fromEvent } from 'rxjs';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class WindowSizeService {\r\n    private heightSubject = new BehaviorSubject<number>(window.innerHeight);\r\n    height$ = this.heightSubject.asObservable();\r\n\r\n    constructor() {\r\n        fromEvent(window, 'resize').subscribe(() => {\r\n            this.heightSubject.next(window.innerHeight);\r\n        });\r\n    }\r\n\r\n    getHeight(): number {\r\n        return this.heightSubject.value;\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAEC,SAAS,QAAQ,MAAM;;AAKjD,OAAM,MAAOC,iBAAiB;EAI1BC,YAAA;IAHQ,KAAAC,aAAa,GAAG,IAAIJ,eAAe,CAASK,MAAM,CAACC,WAAW,CAAC;IACvE,KAAAC,OAAO,GAAG,IAAI,CAACH,aAAa,CAACI,YAAY,EAAE;IAGvCP,SAAS,CAACI,MAAM,EAAE,QAAQ,CAAC,CAACI,SAAS,CAAC,MAAK;MACvC,IAAI,CAACL,aAAa,CAACM,IAAI,CAACL,MAAM,CAACC,WAAW,CAAC;IAC/C,CAAC,CAAC;EACN;EAEAK,SAASA,CAAA;IACL,OAAO,IAAI,CAACP,aAAa,CAACQ,KAAK;EACnC;EAAC,QAAAC,CAAA,G;qBAZQX,iBAAiB;EAAA;EAAA,QAAAY,EAAA,G;WAAjBZ,iBAAiB;IAAAa,OAAA,EAAjBb,iBAAiB,CAAAc,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}