{"ast": null, "code": "function isBuffer(obj) {\n  return obj && obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);\n}\nfunction keyIdentity(key) {\n  return key;\n}\nexport function flatten(target, opts) {\n  opts = opts || {};\n  const delimiter = opts.delimiter || '.';\n  const maxDepth = opts.maxDepth;\n  const transformKey = opts.transformKey || keyIdentity;\n  const output = {};\n  function step(object, prev, currentDepth) {\n    currentDepth = currentDepth || 1;\n    Object.keys(object).forEach(function (key) {\n      const value = object[key];\n      const isarray = opts.safe && Array.isArray(value);\n      const type = Object.prototype.toString.call(value);\n      const isbuffer = isBuffer(value);\n      const isobject = type === '[object Object]' || type === '[object Array]';\n      const newKey = prev ? prev + delimiter + transformKey(key) : transformKey(key);\n      if (!isarray && !isbuffer && isobject && Object.keys(value).length && (!opts.maxDepth || currentDepth < maxDepth)) {\n        return step(value, newKey, currentDepth + 1);\n      }\n      output[newKey] = value;\n    });\n  }\n  step(target);\n  return output;\n}\nexport function unflatten(target, opts) {\n  opts = opts || {};\n  const delimiter = opts.delimiter || '.';\n  const overwrite = opts.overwrite || false;\n  const transformKey = opts.transformKey || keyIdentity;\n  const result = {};\n  const isbuffer = isBuffer(target);\n  if (isbuffer || Object.prototype.toString.call(target) !== '[object Object]') {\n    return target;\n  }\n\n  // safely ensure that the key is\n  // an integer.\n  function getkey(key) {\n    const parsedKey = Number(key);\n    return isNaN(parsedKey) || key.indexOf('.') !== -1 || opts.object ? key : parsedKey;\n  }\n  function addKeys(keyPrefix, recipient, target) {\n    return Object.keys(target).reduce(function (result, key) {\n      result[keyPrefix + delimiter + key] = target[key];\n      return result;\n    }, recipient);\n  }\n  function isEmpty(val) {\n    const type = Object.prototype.toString.call(val);\n    const isArray = type === '[object Array]';\n    const isObject = type === '[object Object]';\n    if (!val) {\n      return true;\n    } else if (isArray) {\n      return !val.length;\n    } else if (isObject) {\n      return !Object.keys(val).length;\n    }\n  }\n  target = Object.keys(target).reduce(function (result, key) {\n    const type = Object.prototype.toString.call(target[key]);\n    const isObject = type === '[object Object]' || type === '[object Array]';\n    if (!isObject || isEmpty(target[key])) {\n      result[key] = target[key];\n      return result;\n    } else {\n      return addKeys(key, result, flatten(target[key], opts));\n    }\n  }, {});\n  Object.keys(target).forEach(function (key) {\n    const split = key.split(delimiter).map(transformKey);\n    let key1 = getkey(split.shift());\n    let key2 = getkey(split[0]);\n    let recipient = result;\n    while (key2 !== undefined) {\n      if (key1 === '__proto__') {\n        return;\n      }\n      const type = Object.prototype.toString.call(recipient[key1]);\n      const isobject = type === '[object Object]' || type === '[object Array]';\n\n      // do not write over falsey, non-undefined values if overwrite is false\n      if (!overwrite && !isobject && typeof recipient[key1] !== 'undefined') {\n        return;\n      }\n      if (overwrite && !isobject || !overwrite && recipient[key1] == null) {\n        recipient[key1] = typeof key2 === 'number' && !opts.object ? [] : {};\n      }\n      recipient = recipient[key1];\n      if (split.length > 0) {\n        key1 = getkey(split.shift());\n        key2 = getkey(split[0]);\n      }\n    }\n\n    // unflatten again for 'messy objects'\n    recipient[key1] = unflatten(target[key], opts);\n  });\n  return result;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "obj", "constructor", "keyIdentity", "key", "flatten", "target", "opts", "delimiter", "max<PERSON><PERSON><PERSON>", "transform<PERSON>ey", "output", "step", "object", "prev", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "for<PERSON>ach", "value", "isarray", "safe", "Array", "isArray", "type", "prototype", "toString", "call", "isbuffer", "isobject", "new<PERSON>ey", "length", "unflatten", "overwrite", "result", "getkey", "parsed<PERSON><PERSON>", "Number", "isNaN", "indexOf", "add<PERSON><PERSON><PERSON>", "keyPrefix", "recipient", "reduce", "isEmpty", "val", "isObject", "split", "map", "key1", "shift", "key2", "undefined"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/@jsverse/transloco/node_modules/flat/index.js"], "sourcesContent": ["function isBuffer (obj) {\n  return obj &&\n    obj.constructor &&\n    (typeof obj.constructor.isBuffer === 'function') &&\n    obj.constructor.isBuffer(obj)\n}\n\nfunction keyIdentity (key) {\n  return key\n}\n\nexport function flatten (target, opts) {\n  opts = opts || {}\n\n  const delimiter = opts.delimiter || '.'\n  const maxDepth = opts.maxDepth\n  const transformKey = opts.transformKey || keyIdentity\n  const output = {}\n\n  function step (object, prev, currentDepth) {\n    currentDepth = currentDepth || 1\n    Object.keys(object).forEach(function (key) {\n      const value = object[key]\n      const isarray = opts.safe && Array.isArray(value)\n      const type = Object.prototype.toString.call(value)\n      const isbuffer = isBuffer(value)\n      const isobject = (\n        type === '[object Object]' ||\n        type === '[object Array]'\n      )\n\n      const newKey = prev\n        ? prev + delimiter + transformKey(key)\n        : transformKey(key)\n\n      if (!isarray && !isbuffer && isobject && Object.keys(value).length &&\n        (!opts.maxDepth || currentDepth < maxDepth)) {\n        return step(value, newKey, currentDepth + 1)\n      }\n\n      output[newKey] = value\n    })\n  }\n\n  step(target)\n\n  return output\n}\n\nexport function unflatten (target, opts) {\n  opts = opts || {}\n\n  const delimiter = opts.delimiter || '.'\n  const overwrite = opts.overwrite || false\n  const transformKey = opts.transformKey || keyIdentity\n  const result = {}\n\n  const isbuffer = isBuffer(target)\n  if (isbuffer || Object.prototype.toString.call(target) !== '[object Object]') {\n    return target\n  }\n\n  // safely ensure that the key is\n  // an integer.\n  function getkey (key) {\n    const parsedKey = Number(key)\n\n    return (\n      isNaN(parsedKey) ||\n      key.indexOf('.') !== -1 ||\n      opts.object\n    )\n      ? key\n      : parsedKey\n  }\n\n  function addKeys (keyPrefix, recipient, target) {\n    return Object.keys(target).reduce(function (result, key) {\n      result[keyPrefix + delimiter + key] = target[key]\n\n      return result\n    }, recipient)\n  }\n\n  function isEmpty (val) {\n    const type = Object.prototype.toString.call(val)\n    const isArray = type === '[object Array]'\n    const isObject = type === '[object Object]'\n\n    if (!val) {\n      return true\n    } else if (isArray) {\n      return !val.length\n    } else if (isObject) {\n      return !Object.keys(val).length\n    }\n  }\n\n  target = Object.keys(target).reduce(function (result, key) {\n    const type = Object.prototype.toString.call(target[key])\n    const isObject = (type === '[object Object]' || type === '[object Array]')\n    if (!isObject || isEmpty(target[key])) {\n      result[key] = target[key]\n      return result\n    } else {\n      return addKeys(\n        key,\n        result,\n        flatten(target[key], opts)\n      )\n    }\n  }, {})\n\n  Object.keys(target).forEach(function (key) {\n    const split = key.split(delimiter).map(transformKey)\n    let key1 = getkey(split.shift())\n    let key2 = getkey(split[0])\n    let recipient = result\n\n    while (key2 !== undefined) {\n      if (key1 === '__proto__') {\n        return\n      }\n\n      const type = Object.prototype.toString.call(recipient[key1])\n      const isobject = (\n        type === '[object Object]' ||\n        type === '[object Array]'\n      )\n\n      // do not write over falsey, non-undefined values if overwrite is false\n      if (!overwrite && !isobject && typeof recipient[key1] !== 'undefined') {\n        return\n      }\n\n      if ((overwrite && !isobject) || (!overwrite && recipient[key1] == null)) {\n        recipient[key1] = (\n          typeof key2 === 'number' &&\n          !opts.object\n            ? []\n            : {}\n        )\n      }\n\n      recipient = recipient[key1]\n      if (split.length > 0) {\n        key1 = getkey(split.shift())\n        key2 = getkey(split[0])\n      }\n    }\n\n    // unflatten again for 'messy objects'\n    recipient[key1] = unflatten(target[key], opts)\n  })\n\n  return result\n}\n"], "mappings": "AAAA,SAASA,QAAQA,CAAEC,GAAG,EAAE;EACtB,OAAOA,GAAG,IACRA,GAAG,CAACC,WAAW,IACd,OAAOD,GAAG,CAACC,WAAW,CAACF,QAAQ,KAAK,UAAW,IAChDC,GAAG,CAACC,WAAW,CAACF,QAAQ,CAACC,GAAG,CAAC;AACjC;AAEA,SAASE,WAAWA,CAAEC,GAAG,EAAE;EACzB,OAAOA,GAAG;AACZ;AAEA,OAAO,SAASC,OAAOA,CAAEC,MAAM,EAAEC,IAAI,EAAE;EACrCA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,MAAMC,SAAS,GAAGD,IAAI,CAACC,SAAS,IAAI,GAAG;EACvC,MAAMC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC9B,MAAMC,YAAY,GAAGH,IAAI,CAACG,YAAY,IAAIP,WAAW;EACrD,MAAMQ,MAAM,GAAG,CAAC,CAAC;EAEjB,SAASC,IAAIA,CAAEC,MAAM,EAAEC,IAAI,EAAEC,YAAY,EAAE;IACzCA,YAAY,GAAGA,YAAY,IAAI,CAAC;IAChCC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,OAAO,CAAC,UAAUd,GAAG,EAAE;MACzC,MAAMe,KAAK,GAAGN,MAAM,CAACT,GAAG,CAAC;MACzB,MAAMgB,OAAO,GAAGb,IAAI,CAACc,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC;MACjD,MAAMK,IAAI,GAAGR,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAK,CAAC;MAClD,MAAMS,QAAQ,GAAG5B,QAAQ,CAACmB,KAAK,CAAC;MAChC,MAAMU,QAAQ,GACZL,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,gBACV;MAED,MAAMM,MAAM,GAAGhB,IAAI,GACfA,IAAI,GAAGN,SAAS,GAAGE,YAAY,CAACN,GAAG,CAAC,GACpCM,YAAY,CAACN,GAAG,CAAC;MAErB,IAAI,CAACgB,OAAO,IAAI,CAACQ,QAAQ,IAAIC,QAAQ,IAAIb,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAACY,MAAM,KAC/D,CAACxB,IAAI,CAACE,QAAQ,IAAIM,YAAY,GAAGN,QAAQ,CAAC,EAAE;QAC7C,OAAOG,IAAI,CAACO,KAAK,EAAEW,MAAM,EAAEf,YAAY,GAAG,CAAC,CAAC;MAC9C;MAEAJ,MAAM,CAACmB,MAAM,CAAC,GAAGX,KAAK;IACxB,CAAC,CAAC;EACJ;EAEAP,IAAI,CAACN,MAAM,CAAC;EAEZ,OAAOK,MAAM;AACf;AAEA,OAAO,SAASqB,SAASA,CAAE1B,MAAM,EAAEC,IAAI,EAAE;EACvCA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,MAAMC,SAAS,GAAGD,IAAI,CAACC,SAAS,IAAI,GAAG;EACvC,MAAMyB,SAAS,GAAG1B,IAAI,CAAC0B,SAAS,IAAI,KAAK;EACzC,MAAMvB,YAAY,GAAGH,IAAI,CAACG,YAAY,IAAIP,WAAW;EACrD,MAAM+B,MAAM,GAAG,CAAC,CAAC;EAEjB,MAAMN,QAAQ,GAAG5B,QAAQ,CAACM,MAAM,CAAC;EACjC,IAAIsB,QAAQ,IAAIZ,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACrB,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAC5E,OAAOA,MAAM;EACf;;EAEA;EACA;EACA,SAAS6B,MAAMA,CAAE/B,GAAG,EAAE;IACpB,MAAMgC,SAAS,GAAGC,MAAM,CAACjC,GAAG,CAAC;IAE7B,OACEkC,KAAK,CAACF,SAAS,CAAC,IAChBhC,GAAG,CAACmC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IACvBhC,IAAI,CAACM,MAAM,GAETT,GAAG,GACHgC,SAAS;EACf;EAEA,SAASI,OAAOA,CAAEC,SAAS,EAAEC,SAAS,EAAEpC,MAAM,EAAE;IAC9C,OAAOU,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACqC,MAAM,CAAC,UAAUT,MAAM,EAAE9B,GAAG,EAAE;MACvD8B,MAAM,CAACO,SAAS,GAAGjC,SAAS,GAAGJ,GAAG,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC;MAEjD,OAAO8B,MAAM;IACf,CAAC,EAAEQ,SAAS,CAAC;EACf;EAEA,SAASE,OAAOA,CAAEC,GAAG,EAAE;IACrB,MAAMrB,IAAI,GAAGR,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACkB,GAAG,CAAC;IAChD,MAAMtB,OAAO,GAAGC,IAAI,KAAK,gBAAgB;IACzC,MAAMsB,QAAQ,GAAGtB,IAAI,KAAK,iBAAiB;IAE3C,IAAI,CAACqB,GAAG,EAAE;MACR,OAAO,IAAI;IACb,CAAC,MAAM,IAAItB,OAAO,EAAE;MAClB,OAAO,CAACsB,GAAG,CAACd,MAAM;IACpB,CAAC,MAAM,IAAIe,QAAQ,EAAE;MACnB,OAAO,CAAC9B,MAAM,CAACC,IAAI,CAAC4B,GAAG,CAAC,CAACd,MAAM;IACjC;EACF;EAEAzB,MAAM,GAAGU,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACqC,MAAM,CAAC,UAAUT,MAAM,EAAE9B,GAAG,EAAE;IACzD,MAAMoB,IAAI,GAAGR,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACrB,MAAM,CAACF,GAAG,CAAC,CAAC;IACxD,MAAM0C,QAAQ,GAAItB,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,gBAAiB;IAC1E,IAAI,CAACsB,QAAQ,IAAIF,OAAO,CAACtC,MAAM,CAACF,GAAG,CAAC,CAAC,EAAE;MACrC8B,MAAM,CAAC9B,GAAG,CAAC,GAAGE,MAAM,CAACF,GAAG,CAAC;MACzB,OAAO8B,MAAM;IACf,CAAC,MAAM;MACL,OAAOM,OAAO,CACZpC,GAAG,EACH8B,MAAM,EACN7B,OAAO,CAACC,MAAM,CAACF,GAAG,CAAC,EAAEG,IAAI,CAC3B,CAAC;IACH;EACF,CAAC,EAAE,CAAC,CAAC,CAAC;EAENS,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACY,OAAO,CAAC,UAAUd,GAAG,EAAE;IACzC,MAAM2C,KAAK,GAAG3C,GAAG,CAAC2C,KAAK,CAACvC,SAAS,CAAC,CAACwC,GAAG,CAACtC,YAAY,CAAC;IACpD,IAAIuC,IAAI,GAAGd,MAAM,CAACY,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IAChC,IAAIC,IAAI,GAAGhB,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAIL,SAAS,GAAGR,MAAM;IAEtB,OAAOiB,IAAI,KAAKC,SAAS,EAAE;MACzB,IAAIH,IAAI,KAAK,WAAW,EAAE;QACxB;MACF;MAEA,MAAMzB,IAAI,GAAGR,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACe,SAAS,CAACO,IAAI,CAAC,CAAC;MAC5D,MAAMpB,QAAQ,GACZL,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,gBACV;;MAED;MACA,IAAI,CAACS,SAAS,IAAI,CAACJ,QAAQ,IAAI,OAAOa,SAAS,CAACO,IAAI,CAAC,KAAK,WAAW,EAAE;QACrE;MACF;MAEA,IAAKhB,SAAS,IAAI,CAACJ,QAAQ,IAAM,CAACI,SAAS,IAAIS,SAAS,CAACO,IAAI,CAAC,IAAI,IAAK,EAAE;QACvEP,SAAS,CAACO,IAAI,CAAC,GACb,OAAOE,IAAI,KAAK,QAAQ,IACxB,CAAC5C,IAAI,CAACM,MAAM,GACR,EAAE,GACF,CAAC,CACN;MACH;MAEA6B,SAAS,GAAGA,SAAS,CAACO,IAAI,CAAC;MAC3B,IAAIF,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAE;QACpBkB,IAAI,GAAGd,MAAM,CAACY,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;QAC5BC,IAAI,GAAGhB,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;;IAEA;IACAL,SAAS,CAACO,IAAI,CAAC,GAAGjB,SAAS,CAAC1B,MAAM,CAACF,GAAG,CAAC,EAAEG,IAAI,CAAC;EAChD,CAAC,CAAC;EAEF,OAAO2B,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}