{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DialogModule } from 'primeng/dialog';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { KeyFilterModule } from 'primeng/keyfilter';\nimport { TableModule } from 'primeng/table';\nimport { ToastModule } from 'primeng/toast';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { MoneyPipe } from '../../pipes/money.pipe';\nimport { TextAlignPipe } from '../../pipes/text-align.pipe';\nimport { ExexTableComponent } from './exex-table/exex-table.component';\nimport * as i0 from \"@angular/core\";\nexport class ExexCommonModule {\n  static #_ = this.ɵfac = function ExexCommonModule_Factory(t) {\n    return new (t || ExexCommonModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ExexCommonModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [MessageService, ConfirmationService],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, ToastModule, ToolbarModule, TableModule, DialogModule, ConfirmDialogModule, InputTextModule, FileUploadModule, InputTextareaModule, KeyFilterModule, ButtonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ExexCommonModule, {\n    declarations: [ExexTableComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, ToastModule, ToolbarModule, TableModule, DialogModule, ConfirmDialogModule, InputTextModule, FileUploadModule, InputTextareaModule, KeyFilterModule, ButtonModule, TextAlignPipe, MoneyPipe],\n    exports: [ExexTableComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "ConfirmationService", "MessageService", "ButtonModule", "ConfirmDialogModule", "DialogModule", "FileUploadModule", "InputTextModule", "InputTextareaModule", "KeyFilterModule", "TableModule", "ToastModule", "ToolbarModule", "MoneyPipe", "TextAlignPipe", "ExexTableComponent", "ExexCommonModule", "_", "_2", "_3", "imports", "declarations", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\common\\exex-common.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { FileUploadModule } from 'primeng/fileupload';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { KeyFilterModule } from 'primeng/keyfilter';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { MoneyPipe } from '../../pipes/money.pipe';\r\nimport { TextAlignPipe } from '../../pipes/text-align.pipe';\r\nimport { StatusBadgePipe } from '../../pipes/status-badge.pipe';\r\nimport { ExexTableComponent } from './exex-table/exex-table.component';\r\n\r\n@NgModule({\r\n    declarations: [ExexTableComponent],\r\n    exports: [ExexTableComponent],\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        ToastModule,\r\n        ToolbarModule,\r\n        TableModule,\r\n        DialogModule,\r\n        ConfirmDialogModule,\r\n        InputTextModule,\r\n        FileUploadModule,\r\n        InputTextareaModule,\r\n        KeyFilterModule,\r\n        ButtonModule,\r\n        TextAlignPipe,\r\n        MoneyPipe,\r\n    ],\r\n    providers: [MessageService, ConfirmationService],\r\n})\r\nexport class ExexCommonModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,kBAAkB,QAAQ,mCAAmC;;AAwBtE,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;eAFd,CAACjB,cAAc,EAAED,mBAAmB,CAAC;IAAAmB,OAAA,GAhB5CtB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBW,WAAW,EACXC,aAAa,EACbF,WAAW,EACXL,YAAY,EACZD,mBAAmB,EACnBG,eAAe,EACfD,gBAAgB,EAChBE,mBAAmB,EACnBC,eAAe,EACfN,YAAY;EAAA;;;2EAMPa,gBAAgB;IAAAK,YAAA,GArBVN,kBAAkB;IAAAK,OAAA,GAG7BtB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBW,WAAW,EACXC,aAAa,EACbF,WAAW,EACXL,YAAY,EACZD,mBAAmB,EACnBG,eAAe,EACfD,gBAAgB,EAChBE,mBAAmB,EACnBC,eAAe,EACfN,YAAY,EACZW,aAAa,EACbD,SAAS;IAAAS,OAAA,GAhBHP,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}