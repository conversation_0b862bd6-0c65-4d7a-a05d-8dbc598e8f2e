{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { LoginComponent } from './login/login.component';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static #_ = this.ɵfac = function AuthModule_Factory(t) {\n    return new (t || AuthModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, AuthRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent],\n    imports: [CommonModule, AuthRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ButtonModule", "CheckboxModule", "InputTextModule", "PasswordModule", "AuthRoutingModule", "LoginComponent", "AuthModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\auth.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\nimport { LoginComponent } from './login/login.component';\r\n@NgModule({\r\n    declarations: [LoginComponent],\r\n    imports: [\r\n        CommonModule,\r\n        AuthRoutingModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        InputTextModule,\r\n        FormsModule,\r\n        PasswordModule,\r\n    ],\r\n})\r\nexport class AuthModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,yBAAyB;;AAaxD,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cATfX,YAAY,EACZM,iBAAiB,EACjBJ,YAAY,EACZC,cAAc,EACdC,eAAe,EACfH,WAAW,EACXI,cAAc;EAAA;;;2EAGTG,UAAU;IAAAI,YAAA,GAXJL,cAAc;IAAAM,OAAA,GAEzBb,YAAY,EACZM,iBAAiB,EACjBJ,YAAY,EACZC,cAAc,EACdC,eAAe,EACfH,WAAW,EACXI,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}