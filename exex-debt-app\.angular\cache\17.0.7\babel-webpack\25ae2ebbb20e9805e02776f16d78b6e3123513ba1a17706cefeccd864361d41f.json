{"ast": null, "code": "export var Path;\n(function (Path) {\n  Path[\"DASHBOARD_CUSTOMER\"] = \"/dashboard/customer\";\n  Path[\"DASHBOARD_USER\"] = \"/dashboard/user\";\n  Path[\"AUTH_LOGIN\"] = \"/auth/login\";\n})(Path || (Path = {}));", "map": {"version": 3, "names": ["Path"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\enums\\path.enum.ts"], "sourcesContent": ["export enum Path {\r\n    DASHBOARD_CUSTOMER = '/dashboard/customer',\r\n    DASHBOARD_USER = '/dashboard/user',\r\n    AUTH_LOGIN = '/auth/login',\r\n}\r\n"], "mappings": "AAAA,WAAYA,IAIX;AAJD,WAAYA,IAAI;EACZA,IAAA,8CAA0C;EAC1CA,IAAA,sCAAkC;EAClCA,IAAA,8BAA0B;AAC9B,CAAC,EAJWA,IAAI,KAAJA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}