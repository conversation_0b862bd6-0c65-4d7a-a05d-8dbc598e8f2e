{"ast": null, "code": "import { Subscription } from '../Subscription';\nexport class Action extends Subscription {\n  constructor(scheduler, work) {\n    super();\n  }\n  schedule(state, delay = 0) {\n    return this;\n  }\n}", "map": {"version": 3, "names": ["Subscription", "Action", "constructor", "scheduler", "work", "schedule", "state", "delay"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/rxjs/dist/esm/internal/scheduler/Action.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nexport class Action extends Subscription {\n    constructor(scheduler, work) {\n        super();\n    }\n    schedule(state, delay = 0) {\n        return this;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,MAAMC,MAAM,SAASD,YAAY,CAAC;EACrCE,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAAC,CAAC;EACX;EACAC,QAAQA,CAACC,KAAK,EAAEC,KAAK,GAAG,CAAC,EAAE;IACvB,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}