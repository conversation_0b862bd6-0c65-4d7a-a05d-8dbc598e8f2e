{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class MoneyPipe {\n  transform(value) {\n    if (!isNaN(value) && value !== null && value !== '') {\n      return new Intl.NumberFormat('vi-VN').format(Number(value)); // Format with Vietnamese thousand separators\n    }\n\n    return value; // Return original value if not a valid number\n  }\n  static #_ = this.ɵfac = function MoneyPipe_Factory(t) {\n    return new (t || MoneyPipe)();\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"money\",\n    type: MoneyPipe,\n    pure: true,\n    standalone: true\n  });\n}", "map": {"version": 3, "names": ["MoneyPipe", "transform", "value", "isNaN", "Intl", "NumberFormat", "format", "Number", "_", "_2", "pure", "standalone"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\pipes\\money.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'money',\r\n    standalone: true,\r\n})\r\nexport class MoneyPipe implements PipeTransform {\r\n    transform(value: any): string {\r\n        if (!isNaN(value) && value !== null && value !== '') {\r\n            return new Intl.NumberFormat('vi-VN').format(Number(value)); // Format with Vietnamese thousand separators\r\n        }\r\n        return value; // Return original value if not a valid number\r\n    }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,SAAS;EAClBC,SAASA,CAACC,KAAU;IAChB,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;MACjD,OAAO,IAAIE,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACC,MAAM,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;;IAEjE,OAAOA,KAAK,CAAC,CAAC;EAClB;EAAC,QAAAM,CAAA,G;qBANQR,SAAS;EAAA;EAAA,QAAAS,EAAA,G;;UAATT,SAAS;IAAAU,IAAA;IAAAC,UAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}