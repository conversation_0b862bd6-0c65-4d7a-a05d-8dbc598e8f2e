{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class LoggingInterceptor {\n  intercept(request, next) {\n    return next.handle(request).pipe(tap(event => {\n      if (event instanceof HttpResponse) {\n        console.log('HTTP Response:', event);\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function LoggingInterceptor_Factory(t) {\n    return new (t || LoggingInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoggingInterceptor,\n    factory: LoggingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpResponse", "tap", "LoggingInterceptor", "intercept", "request", "next", "handle", "pipe", "event", "console", "log", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\logging.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n  HttpResponse,\n} from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\n\n@Injectable()\nexport class LoggingInterceptor implements HttpInterceptor {\n  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {\n    return next.handle(request).pipe(\n      tap((event) => {\n        if (event instanceof HttpResponse) {\n          console.log('HTTP Response:', event);\n        }\n      })\n    );\n  }\n}"], "mappings": "AACA,SAIEA,YAAY,QACP,sBAAsB;AAC7B,SAASC,GAAG,QAAQ,gBAAgB;;AAGpC,OAAM,MAAOC,kBAAkB;EAC7BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9BN,GAAG,CAAEO,KAAK,IAAI;MACZ,IAAIA,KAAK,YAAYR,YAAY,EAAE;QACjCS,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;;IAExC,CAAC,CAAC,CACH;EACH;EAAC,QAAAG,CAAA,G;qBATUT,kBAAkB;EAAA;EAAA,QAAAU,EAAA,G;WAAlBV,kBAAkB;IAAAW,OAAA,EAAlBX,kBAAkB,CAAAY;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}