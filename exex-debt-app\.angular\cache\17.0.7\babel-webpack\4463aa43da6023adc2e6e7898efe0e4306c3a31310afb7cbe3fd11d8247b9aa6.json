{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { INVOICE_COLS } from './invoice-cols';\nlet InvoiceComponent = class InvoiceComponent {\n  constructor(customerService, fb, exexCommonService) {\n    this.customerService = customerService;\n    this.fb = fb;\n    this.exexCommonService = exexCommonService;\n    this.invoiceDialog = false;\n    this.heightTableScroll = 0;\n  }\n  ngOnInit() {\n    this.dataTable = {\n      ...this.dataTable,\n      columns: INVOICE_COLS\n    };\n    this.customerService.getInvoices().then(data => {\n      this.dataTable = {\n        ...this.dataTable,\n        value: data\n      };\n    });\n    this.invoiceForm = this.fb.group({\n      status: ['', Validators.required],\n      totalAmount: [null, [Validators.required, Validators.min(0)]],\n      paidAmount: [null, [Validators.required, Validators.min(0)]],\n      remainingAmount: [null, [Validators.required, Validators.min(0)]]\n    });\n  }\n  openNew() {\n    this.invoiceForm.reset();\n    this.invoice = {};\n    this.invoiceDialog = true;\n  }\n  deleteSelectedProducts() {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => !this.selectedInvoices?.includes(val));\n      this.selectedInvoices = null;\n      this.exexCommonService.showToastSuccess('Customers Deleted');\n    });\n  }\n  selectedRow(rows) {\n    this.selectedInvoices = [...rows];\n  }\n  editProduct(invoice) {\n    this.invoice = {\n      ...invoice\n    };\n    this.invoiceForm.patchValue({\n      status: this.invoice.status,\n      totalAmount: this.invoice.totalAmount,\n      paidAmount: this.invoice.paidAmount,\n      remainingAmount: this.invoice.remainingAmount\n    });\n    this.invoiceDialog = true;\n  }\n  deleteProduct(invoice) {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => val.customerId !== invoice.customerId);\n      this.invoice = {};\n      this.exexCommonService.showToastSuccess('Customer Deleted');\n    });\n  }\n  hideDialog() {\n    this.invoiceDialog = false;\n  }\n  saveCustomer() {\n    if (this.invoiceForm.valid) {\n      if (this.invoice.customerId) {\n        this.dataTable.value[this.findIndexById(this.invoice.customerId)] = this.invoice;\n        this.exexCommonService.showToastSuccess('Successful');\n      } else {\n        this.dataTable.value.push(this.invoice);\n        this.exexCommonService.showToastSuccess('Successful');\n      }\n      this.dataTable.value = [...this.dataTable.value];\n      this.invoiceDialog = false;\n      this.invoice = {};\n    } else {\n      this.invoiceForm.markAllAsTouched(); // Show validation errors\n    }\n  }\n\n  findIndexById(customerId) {\n    let index = -1;\n    for (let i = 0; i < this.dataTable.value.length; i++) {\n      if (this.dataTable.value[i].customerId === customerId) {\n        index = i;\n        break;\n      }\n    }\n    return index;\n  }\n};\nInvoiceComponent = __decorate([Component({\n  selector: 'app-invoice',\n  templateUrl: './invoice.component.html',\n  styleUrl: './invoice.component.scss'\n})], InvoiceComponent);\nexport { InvoiceComponent };", "map": {"version": 3, "names": ["Component", "Validators", "INVOICE_COLS", "InvoiceComponent", "constructor", "customerService", "fb", "exexCommonService", "invoiceDialog", "heightTableScroll", "ngOnInit", "dataTable", "columns", "getInvoices", "then", "data", "value", "invoiceForm", "group", "status", "required", "totalAmount", "min", "paidAmount", "remainingAmount", "openNew", "reset", "invoice", "deleteSelectedProducts", "showDialogConfirm", "filter", "val", "selectedInvoices", "includes", "showToastSuccess", "selectedRow", "rows", "editProduct", "patchValue", "deleteProduct", "customerId", "hideDialog", "saveCustomer", "valid", "findIndexById", "push", "mark<PERSON>llAsTouched", "index", "i", "length", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\invoice\\invoice.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ExexCommonService } from 'src/app/core/service/exex-common.service';\r\nimport { IPropExexTable } from '../../common/exex-table/exex-table.model';\r\nimport { CustomerService } from '../../../service/customer.service';\r\nimport { INVOICE_COLS } from './invoice-cols';\r\n\r\n@Component({\r\n    selector: 'app-invoice',\r\n    templateUrl: './invoice.component.html',\r\n    styleUrl: './invoice.component.scss',\r\n})\r\nexport class InvoiceComponent {\r\n    invoiceDialog: boolean = false;\r\n\r\n    invoices!: any[];\r\n\r\n    invoice!: any;\r\n\r\n    selectedInvoices!: any[] | null;\r\n\r\n    cols: any;\r\n\r\n    heightTableScroll: number = 0;\r\n\r\n    invoiceForm!: FormGroup;\r\n\r\n    dataTable!: IPropExexTable;\r\n\r\n    constructor(\r\n        private customerService: CustomerService,\r\n        private fb: FormBuilder,\r\n        private exexCommonService: ExexCommonService,\r\n    ) {}\r\n\r\n    ngOnInit() {\r\n        this.dataTable = {\r\n            ...this.dataTable,\r\n            columns: INVOICE_COLS,\r\n        };\r\n        this.customerService.getInvoices().then((data) => {\r\n            this.dataTable = {\r\n                ...this.dataTable,\r\n                value: data,\r\n            };\r\n        });\r\n\r\n        this.invoiceForm = this.fb.group({\r\n            status: ['', Validators.required],\r\n            totalAmount: [null, [Validators.required, Validators.min(0)]],\r\n            paidAmount: [null, [Validators.required, Validators.min(0)]],\r\n            remainingAmount: [null, [Validators.required, Validators.min(0)]],\r\n        });\r\n    }\r\n\r\n    openNew() {\r\n        this.invoiceForm.reset();\r\n        this.invoice = {};\r\n        this.invoiceDialog = true;\r\n    }\r\n\r\n    deleteSelectedProducts() {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => !this.selectedInvoices?.includes(val));\r\n            this.selectedInvoices = null;\r\n            this.exexCommonService.showToastSuccess('Customers Deleted');\r\n        });\r\n    }\r\n\r\n    selectedRow(rows) {\r\n        this.selectedInvoices = [...rows];\r\n    }\r\n\r\n    editProduct(invoice: any) {\r\n        this.invoice = { ...invoice };\r\n        this.invoiceForm.patchValue({\r\n            status: this.invoice.status,\r\n            totalAmount: this.invoice.totalAmount,\r\n            paidAmount: this.invoice.paidAmount,\r\n            remainingAmount: this.invoice.remainingAmount,\r\n        });\r\n        this.invoiceDialog = true;\r\n    }\r\n\r\n    deleteProduct(invoice: any) {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => val.customerId !== invoice.customerId);\r\n            this.invoice = {};\r\n            this.exexCommonService.showToastSuccess('Customer Deleted');\r\n        });\r\n    }\r\n\r\n    hideDialog() {\r\n        this.invoiceDialog = false;\r\n    }\r\n\r\n    saveCustomer() {\r\n        if (this.invoiceForm.valid) {\r\n            if (this.invoice.customerId) {\r\n                this.dataTable.value[this.findIndexById(this.invoice.customerId)] = this.invoice;\r\n                this.exexCommonService.showToastSuccess('Successful');\r\n            } else {\r\n                this.dataTable.value.push(this.invoice);\r\n                this.exexCommonService.showToastSuccess('Successful');\r\n            }\r\n\r\n            this.dataTable.value = [...this.dataTable.value];\r\n            this.invoiceDialog = false;\r\n            this.invoice = {};\r\n        } else {\r\n            this.invoiceForm.markAllAsTouched(); // Show validation errors\r\n        }\r\n    }\r\n\r\n    findIndexById(customerId: string): number {\r\n        let index = -1;\r\n        for (let i = 0; i < this.dataTable.value.length; i++) {\r\n            if (this.dataTable.value[i].customerId === customerId) {\r\n                index = i;\r\n                break;\r\n            }\r\n        }\r\n        return index;\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAAiCC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,YAAY,QAAQ,gBAAgB;AAOtC,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAiBzBC,YACYC,eAAgC,EAChCC,EAAe,EACfC,iBAAoC;IAFpC,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAnB7B,KAAAC,aAAa,GAAY,KAAK;IAU9B,KAAAC,iBAAiB,GAAW,CAAC;EAU1B;EAEHC,QAAQA,CAAA;IACJ,IAAI,CAACC,SAAS,GAAG;MACb,GAAG,IAAI,CAACA,SAAS;MACjBC,OAAO,EAAEV;KACZ;IACD,IAAI,CAACG,eAAe,CAACQ,WAAW,EAAE,CAACC,IAAI,CAAEC,IAAI,IAAI;MAC7C,IAAI,CAACJ,SAAS,GAAG;QACb,GAAG,IAAI,CAACA,SAAS;QACjBK,KAAK,EAAED;OACV;IACL,CAAC,CAAC;IAEF,IAAI,CAACE,WAAW,GAAG,IAAI,CAACX,EAAE,CAACY,KAAK,CAAC;MAC7BC,MAAM,EAAE,CAAC,EAAE,EAAElB,UAAU,CAACmB,QAAQ,CAAC;MACjCC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACpB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DC,UAAU,EAAE,CAAC,IAAI,EAAE,CAACtB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DE,eAAe,EAAE,CAAC,IAAI,EAAE,CAACvB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC;KACnE,CAAC;EACN;EAEAG,OAAOA,CAAA;IACH,IAAI,CAACR,WAAW,CAACS,KAAK,EAAE;IACxB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACnB,aAAa,GAAG,IAAI;EAC7B;EAEAoB,sBAAsBA,CAAA;IAClB,IAAI,CAACrB,iBAAiB,CAACsB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAAClB,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACc,MAAM,CAAEC,GAAG,IAAK,CAAC,IAAI,CAACC,gBAAgB,EAAEC,QAAQ,CAACF,GAAG,CAAC,CAAC;MAClG,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACzB,iBAAiB,CAAC2B,gBAAgB,CAAC,mBAAmB,CAAC;IAChE,CAAC,CAAC;EACN;EAEAC,WAAWA,CAACC,IAAI;IACZ,IAAI,CAACJ,gBAAgB,GAAG,CAAC,GAAGI,IAAI,CAAC;EACrC;EAEAC,WAAWA,CAACV,OAAY;IACpB,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAC7B,IAAI,CAACV,WAAW,CAACqB,UAAU,CAAC;MACxBnB,MAAM,EAAE,IAAI,CAACQ,OAAO,CAACR,MAAM;MAC3BE,WAAW,EAAE,IAAI,CAACM,OAAO,CAACN,WAAW;MACrCE,UAAU,EAAE,IAAI,CAACI,OAAO,CAACJ,UAAU;MACnCC,eAAe,EAAE,IAAI,CAACG,OAAO,CAACH;KACjC,CAAC;IACF,IAAI,CAAChB,aAAa,GAAG,IAAI;EAC7B;EAEA+B,aAAaA,CAACZ,OAAY;IACtB,IAAI,CAACpB,iBAAiB,CAACsB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAAClB,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACc,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACS,UAAU,KAAKb,OAAO,CAACa,UAAU,CAAC;MAClG,IAAI,CAACb,OAAO,GAAG,EAAE;MACjB,IAAI,CAACpB,iBAAiB,CAAC2B,gBAAgB,CAAC,kBAAkB,CAAC;IAC/D,CAAC,CAAC;EACN;EAEAO,UAAUA,CAAA;IACN,IAAI,CAACjC,aAAa,GAAG,KAAK;EAC9B;EAEAkC,YAAYA,CAAA;IACR,IAAI,IAAI,CAACzB,WAAW,CAAC0B,KAAK,EAAE;MACxB,IAAI,IAAI,CAAChB,OAAO,CAACa,UAAU,EAAE;QACzB,IAAI,CAAC7B,SAAS,CAACK,KAAK,CAAC,IAAI,CAAC4B,aAAa,CAAC,IAAI,CAACjB,OAAO,CAACa,UAAU,CAAC,CAAC,GAAG,IAAI,CAACb,OAAO;QAChF,IAAI,CAACpB,iBAAiB,CAAC2B,gBAAgB,CAAC,YAAY,CAAC;OACxD,MAAM;QACH,IAAI,CAACvB,SAAS,CAACK,KAAK,CAAC6B,IAAI,CAAC,IAAI,CAAClB,OAAO,CAAC;QACvC,IAAI,CAACpB,iBAAiB,CAAC2B,gBAAgB,CAAC,YAAY,CAAC;;MAGzD,IAAI,CAACvB,SAAS,CAACK,KAAK,GAAG,CAAC,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAAC;MAChD,IAAI,CAACR,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACmB,OAAO,GAAG,EAAE;KACpB,MAAM;MACH,IAAI,CAACV,WAAW,CAAC6B,gBAAgB,EAAE,CAAC,CAAC;;EAE7C;;EAEAF,aAAaA,CAACJ,UAAkB;IAC5B,IAAIO,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrC,SAAS,CAACK,KAAK,CAACiC,MAAM,EAAED,CAAC,EAAE,EAAE;MAClD,IAAI,IAAI,CAACrC,SAAS,CAACK,KAAK,CAACgC,CAAC,CAAC,CAACR,UAAU,KAAKA,UAAU,EAAE;QACnDO,KAAK,GAAGC,CAAC;QACT;;;IAGR,OAAOD,KAAK;EAChB;CACH;AAhHY5C,gBAAgB,GAAA+C,UAAA,EAL5BlD,SAAS,CAAC;EACPmD,QAAQ,EAAE,aAAa;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE;CACb,CAAC,C,EACWlD,gBAAgB,CAgH5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}