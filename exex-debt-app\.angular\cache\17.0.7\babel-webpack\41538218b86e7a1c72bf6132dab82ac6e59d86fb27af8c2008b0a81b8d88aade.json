{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static #_ = this.ɵfac = function AuthModule_Factory(t) {\n    return new (t || AuthModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, AuthRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    imports: [CommonModule, AuthRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AuthRoutingModule", "AuthModule", "_", "_2", "_3", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        AuthRoutingModule\r\n    ]\r\n})\r\nexport class AuthModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,uBAAuB;;AAQzD,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAJfL,YAAY,EACZC,iBAAiB;EAAA;;;2EAGZC,UAAU;IAAAI,OAAA,GAJfN,YAAY,EACZC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}