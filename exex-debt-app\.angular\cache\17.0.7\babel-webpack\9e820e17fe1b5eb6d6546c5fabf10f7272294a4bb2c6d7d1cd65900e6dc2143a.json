{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class LoginRoutingModule {\n  static #_ = this.ɵfac = function LoginRoutingModule_Factory(t) {\n    return new (t || LoginRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LoginRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: '',\n      component: LoginComponent\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "LoginRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login.component';\n\n@NgModule({\n    imports: [RouterModule.forChild([\n        { path: '', component: LoginComponent }\n    ])],\n    exports: [RouterModule]\n})\nexport class LoginRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;;;AAQlD,OAAM,MAAOC,kBAAkB;EAAA,QAAAC,CAAA,G;qBAAlBD,kBAAkB;EAAA;EAAA,QAAAE,EAAA,G;UAAlBF;EAAkB;EAAA,QAAAG,EAAA,G;cALjBL,YAAY,CAACM,QAAQ,CAAC,CAC5B;MAAEC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAEP;IAAc,CAAE,CAC1C,CAAC,EACQD,YAAY;EAAA;;;2EAEbE,kBAAkB;IAAAO,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFjBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}