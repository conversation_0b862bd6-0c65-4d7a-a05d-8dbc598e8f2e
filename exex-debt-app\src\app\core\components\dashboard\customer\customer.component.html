<div class="customer-container">
    <p-toolbar styleClass="mb-3">
        <ng-template pTemplate="left">
            <span class="p-input-icon-left">
                <i class="pi pi-search"></i>
                <input pInputText type="text" placeholder="Search customers..." />
            </span>
        </ng-template>

        <ng-template pTemplate="right">
            <p-button severity="success" label="New" icon="pi pi-plus" class="mr-2" (onClick)="openNew()" />
            <p-button
                severity="danger"
                label="Delete"
                icon="pi pi-trash"
                class="mr-2"
                (onClick)="deleteSelectedProducts()"
                [disabled]="!selectedCustomers || !selectedCustomers.length" />

            <p-fileUpload
                mode="basic"
                accept=".csv,.xls,.xlsx"
                maxFileSize="5000000"
                label="Import"
                chooseLabel="Import"
                class="mr-2 inline-block" />
            <p-button severity="help" label="Export" icon="pi pi-upload" />
        </ng-template>
    </p-toolbar>

    <exex-table
        [propExexTable]="dataTable"
        (selectedEvent)="selectedRow($event)"
        (editEvent)="editProduct($event)"
        (deleteEvent)="deleteProduct($event)"></exex-table>
</div>

<p-dialog
    [(visible)]="customerDialog"
    [style]="{ width: '420px', maxWidth: '90vw' }"
    header="Customer Details"
    [modal]="true"
    styleClass="p-fluid">
    <ng-template pTemplate="content">
        <form [formGroup]="customerForm">
            <div class="field">
                <label for="customerName">Customer Name</label>
                <input type="text" pInputText id="customerName" formControlName="customerName" autofocus />
                <small
                    class="p-error"
                    *ngIf="customerForm.get('customerName')?.invalid && customerForm.get('customerName')?.touched">
                    Customer Name is required.
                </small>
            </div>

            <div class="field">
                <label for="phoneNumber">Phone Number</label>
                <input type="text" pInputText id="phoneNumber" formControlName="phoneNumber" />
                <small
                    class="p-error"
                    *ngIf="customerForm.get('phoneNumber')?.invalid && customerForm.get('phoneNumber')?.touched">
                    Valid Phone Number (10-15 digits) is required.
                </small>
            </div>

            <div class="field">
                <label for="email">Email</label>
                <input type="email" pInputText id="email" formControlName="email" />
                <small class="p-error" *ngIf="customerForm.get('email')?.invalid && customerForm.get('email')?.touched">
                    Valid Email is required.
                </small>
            </div>

            <div class="field">
                <label for="address">Address</label>
                <input type="text" pInputText id="address" formControlName="address" />
            </div>

            <div class="field">
                <label for="creditLimit">Credit Limit</label>
                <input pInputText pKeyFilter="num" id="creditLimit" formControlName="creditLimit" />
                <small
                    class="p-error"
                    *ngIf="customerForm.get('creditLimit')?.invalid && customerForm.get('creditLimit')?.touched">
                    Credit Limit must be a positive number.
                </small>
            </div>

            <div class="field">
                <label for="currentBalance">Current Balance</label>
                <input pInputText pKeyFilter="num" id="currentBalance" formControlName="currentBalance" />
                <small
                    class="p-error"
                    *ngIf="customerForm.get('currentBalance')?.invalid && customerForm.get('currentBalance')?.touched">
                    Current Balance must be a positive number.
                </small>
            </div>
        </form>
    </ng-template>

    <ng-template pTemplate="footer">
        <p-button label="Cancel" icon="pi pi-times" [text]="true" (onClick)="hideDialog()" />
        <p-button
            label="Save"
            icon="pi pi-check"
            [text]="true"
            (onClick)="saveCustomer()"
            [disabled]="customerForm.invalid" />
    </ng-template>
</p-dialog>
