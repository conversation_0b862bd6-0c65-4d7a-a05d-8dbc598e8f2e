{"ast": null, "code": "export var Path;\n(function (Path) {\n  Path[\"DASHBOARD_USER\"] = \"/dashboard/user\";\n  Path[\"AUTH_LOGIN\"] = \"/auth/login\";\n})(Path || (Path = {}));", "map": {"version": 3, "names": ["Path"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\enums\\path.enum.ts"], "sourcesContent": ["export enum Path {\n    DASHBOARD_USER = '/dashboard/user',\n    AUTH_LOGIN = '/auth/login',\n}\n"], "mappings": "AAAA,WAAYA,IAGX;AAHD,WAAYA,IAAI;EACZA,IAAA,sCAAkC;EAClCA,IAAA,8BAA0B;AAC9B,CAAC,EAHWA,IAAI,KAAJA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}