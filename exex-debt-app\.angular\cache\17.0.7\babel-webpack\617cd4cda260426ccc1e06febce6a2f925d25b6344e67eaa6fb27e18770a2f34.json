{"ast": null, "code": "import { inject } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport class TranslocoHttpLoader {\n  constructor() {\n    this.http = inject(HttpClient);\n  }\n  getTranslation(lang) {\n    return this.http.get(`/assets/i18n/${lang}.json`);\n  }\n  static #_ = this.ɵfac = function TranslocoHttpLoader_Factory(t) {\n    return new (t || TranslocoHttpLoader)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TranslocoHttpLoader,\n    factory: TranslocoHttpLoader.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "HttpClient", "TranslocoHttpLoader", "constructor", "http", "getTranslation", "lang", "get", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\transloco-loader.ts"], "sourcesContent": ["import { inject, Injectable } from '@angular/core';\r\nimport { Translation, TranslocoLoader } from '@jsverse/transloco';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class TranslocoHttpLoader implements TranslocoLoader {\r\n    private http = inject(HttpClient);\r\n\r\n    getTranslation(lang: string) {\r\n        return this.http.get<Translation>(`/assets/i18n/${lang}.json`);\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAoB,eAAe;AAElD,SAASC,UAAU,QAAQ,sBAAsB;;AAIjD,OAAM,MAAOC,mBAAmB;EADhCC,YAAA;IAEY,KAAAC,IAAI,GAAGJ,MAAM,CAACC,UAAU,CAAC;;EAEjCI,cAAcA,CAACC,IAAY;IACvB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAc,gBAAgBD,IAAI,OAAO,CAAC;EAClE;EAAC,QAAAE,CAAA,G;qBALQN,mBAAmB;EAAA;EAAA,QAAAO,EAAA,G;WAAnBP,mBAAmB;IAAAQ,OAAA,EAAnBR,mBAAmB,CAAAS,IAAA;IAAAC,UAAA,EADN;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}