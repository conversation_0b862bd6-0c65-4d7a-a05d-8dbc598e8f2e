{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-spinner\";\nexport class LoadingInterceptor {\n  constructor(spinner) {\n    this.spinner = spinner;\n  }\n  intercept(request, next) {\n    this.spinner.show();\n    return next.handle(request).pipe(finalize(() => {\n      this.spinner.hide();\n    }));\n  }\n  static #_ = this.ɵfac = function LoadingInterceptor_Factory(t) {\n    return new (t || LoadingInterceptor)(i0.ɵɵinject(i1.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoadingInterceptor,\n    factory: LoadingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["finalize", "LoadingInterceptor", "constructor", "spinner", "intercept", "request", "next", "show", "handle", "pipe", "hide", "_", "i0", "ɵɵinject", "i1", "NgxSpinnerService", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\loading.interceptor.ts"], "sourcesContent": ["import {\n  <PERSON>ttp<PERSON><PERSON><PERSON>,\n  HttpInterceptor,\n  HttpRequest,\n} from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { finalize } from 'rxjs/operators';\n\n@Injectable()\nexport class LoadingInterceptor implements HttpInterceptor {\n  constructor(private spinner: NgxSpinnerService) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler) {\n    this.spinner.show();\n    return next.handle(request).pipe(\n      finalize(() => {\n        this.spinner.hide();\n      })\n    );\n  }\n}"], "mappings": "AAOA,SAASA,QAAQ,QAAQ,gBAAgB;;;AAGzC,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;EAAsB;EAEjDC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,IAAI,CAACH,OAAO,CAACI,IAAI,EAAE;IACnB,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAC9BT,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACG,OAAO,CAACO,IAAI,EAAE;IACrB,CAAC,CAAC,CACH;EACH;EAAC,QAAAC,CAAA,G;qBAVUV,kBAAkB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAlBf,kBAAkB;IAAAgB,OAAA,EAAlBhB,kBAAkB,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}