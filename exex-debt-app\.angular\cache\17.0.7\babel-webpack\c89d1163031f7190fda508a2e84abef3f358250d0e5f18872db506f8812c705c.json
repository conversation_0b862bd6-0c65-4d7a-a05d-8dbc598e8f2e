{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport class HeadersInterceptor {\n  intercept(request, next) {\n    const headers = new HttpHeaders({\n      // 'Access-Control-Allow-Origin': '*',\n      // 'Access-Control-Allow-Headers': '*',\n      'Content-Type': 'application/json'\n      // 'X-API-Key': 'your-api-key',\n    });\n\n    const headersRequest = request.clone({\n      headers\n    });\n    return next.handle(headersRequest);\n  }\n  static #_ = this.ɵfac = function HeadersInterceptor_Factory(t) {\n    return new (t || HeadersInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HeadersInterceptor,\n    factory: HeadersInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "HeadersInterceptor", "intercept", "request", "next", "headers", "headersRequest", "clone", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\headers.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpHeaders } from '@angular/common/http';\r\n\r\n@Injectable()\r\nexport class HeadersInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {\r\n        const headers = new HttpHeaders({\r\n            // 'Access-Control-Allow-Origin': '*',\r\n            // 'Access-Control-Allow-Headers': '*',\r\n            'Content-Type': 'application/json',\r\n            // 'X-API-Key': 'your-api-key',\r\n        });\r\n        const headersRequest = request.clone({ headers });\r\n        return next.handle(headersRequest);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAoDA,WAAW,QAAQ,sBAAsB;;AAG7F,OAAM,MAAOC,kBAAkB;EAC3BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,MAAMC,OAAO,GAAG,IAAIL,WAAW,CAAC;MAC5B;MACA;MACA,cAAc,EAAE;MAChB;KACH,CAAC;;IACF,MAAMM,cAAc,GAAGH,OAAO,CAACI,KAAK,CAAC;MAAEF;IAAO,CAAE,CAAC;IACjD,OAAOD,IAAI,CAACI,MAAM,CAACF,cAAc,CAAC;EACtC;EAAC,QAAAG,CAAA,G;qBAVQR,kBAAkB;EAAA;EAAA,QAAAS,EAAA,G;WAAlBT,kBAAkB;IAAAU,OAAA,EAAlBV,kBAAkB,CAAAW;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}