{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { LoginComponent } from './login.component';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i0 from \"@angular/core\";\nexport class LoginModule {\n  static #_ = this.ɵfac = function LoginModule_Factory(t) {\n    return new (t || LoginModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LoginModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginModule, {\n    declarations: [LoginComponent],\n    imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "LoginRoutingModule", "LoginComponent", "ButtonModule", "CheckboxModule", "FormsModule", "PasswordModule", "InputTextModule", "LoginModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { LoginRoutingModule } from './login-routing.module';\r\nimport { LoginComponent } from './login.component';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        LoginRoutingModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        InputTextModule,\r\n        FormsModule,\r\n        PasswordModule,\r\n    ],\r\n    declarations: [LoginComponent],\r\n})\r\nexport class LoginModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;;AAcnD,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA,G;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA,G;UAAXF;EAAW;EAAA,QAAAG,EAAA,G;cAVhBX,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdG,eAAe,EACfF,WAAW,EACXC,cAAc;EAAA;;;2EAITE,WAAW;IAAAI,YAAA,GAFLV,cAAc;IAAAW,OAAA,GARzBb,YAAY,EACZC,kBAAkB,EAClBE,YAAY,EACZC,cAAc,EACdG,eAAe,EACfF,WAAW,EACXC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}