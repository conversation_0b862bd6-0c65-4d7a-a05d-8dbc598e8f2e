import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Renderer2, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { TranslocoService } from '@jsverse/transloco';
import { filter, Subscription } from 'rxjs';
import { LayoutService } from './app.layout.service';
import { AppSidebarComponent } from './app.sidebar.component';
import { AppTopBarComponent } from './app.topbar.component';
import { MENU_LIST } from './menu-list';

@Component({
    selector: 'app-layout',
    template: `<div class="layout-wrapper" [ngClass]="containerClass">
            <app-topbar></app-topbar>
            <div class="layout-sidebar">
                <app-sidebar></app-sidebar>
            </div>
            <div class="layout-main-container">
                <div class="layout-main">
                    <a class="header-custom">
                        <i [ngClass]="item.icon" class="layout-menuitem-icon"></i>&nbsp;
                        <strong class="layout-menuitem-text">{{ item.label }}</strong>
                    </a>
                    <section class="py-3">
                        <router-outlet></router-outlet>
                    </section>
                </div>
                <app-footer></app-footer>
            </div>
            <app-config></app-config>
            <div class="layout-mask"></div>
        </div>
        <style>
            .header-custom {
                display: block;
                border-bottom: 1px solid var(--surface-border);
                padding-bottom: 5px;
            }
        </style>`,
})
export class AppLayoutComponent implements OnInit, OnDestroy {
    overlayMenuOpenSubscription: Subscription;

    menuOutsideClickListener: any;

    profileMenuOutsideClickListener: any;

    @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;

    @ViewChild(AppTopBarComponent) appTopbar!: AppTopBarComponent;

    item: any;

    constructor(
        public layoutService: LayoutService,
        public renderer: Renderer2,
        public router: Router,
        private translocoService: TranslocoService,
    ) {
        this.overlayMenuOpenSubscription = this.layoutService.overlayOpen$.subscribe(() => {
            if (!this.menuOutsideClickListener) {
                this.menuOutsideClickListener = this.renderer.listen('document', 'click', (event) => {
                    const isOutsideClicked = !(
                        this.appSidebar.el.nativeElement.isSameNode(event.target) ||
                        this.appSidebar.el.nativeElement.contains(event.target) ||
                        this.appTopbar.menuButton.nativeElement.isSameNode(event.target) ||
                        this.appTopbar.menuButton.nativeElement.contains(event.target)
                    );

                    if (isOutsideClicked) {
                        this.hideMenu();
                    }
                });
            }

            if (!this.profileMenuOutsideClickListener) {
                this.profileMenuOutsideClickListener = this.renderer.listen('document', 'click', (event) => {
                    const isOutsideClicked = !(
                        this.appTopbar.menu.nativeElement.isSameNode(event.target) ||
                        this.appTopbar.menu.nativeElement.contains(event.target) ||
                        this.appTopbar.topbarMenuButton.nativeElement.isSameNode(event.target) ||
                        this.appTopbar.topbarMenuButton.nativeElement.contains(event.target)
                    );

                    if (isOutsideClicked) {
                        this.hideProfileMenu();
                    }
                });
            }

            if (this.layoutService.state.staticMenuMobileActive) {
                this.blockBodyScroll();
            }
        });

        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(() => {
            this.hideMenu();
            this.hideProfileMenu();

            this.item = this.findLabelByRouterLink(MENU_LIST, this.router.url);
        });
    }

    findLabelByRouterLink(menu: any[], targetLink: string): string | null {
        for (const item of menu) {
            if (item.routerLink && item.routerLink.includes(targetLink)) {
                return item;
            }
            if (item.items) {
                const found = this.findLabelByRouterLink(item.items, targetLink);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    }

    hideMenu() {
        this.layoutService.state.overlayMenuActive = false;
        this.layoutService.state.staticMenuMobileActive = false;
        this.layoutService.state.menuHoverActive = false;
        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
            this.menuOutsideClickListener = null;
        }
        this.unblockBodyScroll();
    }

    hideProfileMenu() {
        this.layoutService.state.profileSidebarVisible = false;
        if (this.profileMenuOutsideClickListener) {
            this.profileMenuOutsideClickListener();
            this.profileMenuOutsideClickListener = null;
        }
    }

    blockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.add('blocked-scroll');
        } else {
            document.body.className += ' blocked-scroll';
        }
    }

    unblockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.remove('blocked-scroll');
        } else {
            document.body.className = document.body.className.replace(
                new RegExp('(^|\\b)' + 'blocked-scroll'.split(' ').join('|') + '(\\b|$)', 'gi'),
                ' ',
            );
        }
    }

    get containerClass() {
        return {
            'layout-theme-light': this.layoutService.config().colorScheme === 'light',
            'layout-theme-dark': this.layoutService.config().colorScheme === 'dark',
            'layout-overlay': this.layoutService.config().menuMode === 'overlay',
            'layout-static': this.layoutService.config().menuMode === 'static',
            'layout-static-inactive':
                this.layoutService.state.staticMenuDesktopInactive && this.layoutService.config().menuMode === 'static',
            'layout-overlay-active': this.layoutService.state.overlayMenuActive,
            'layout-mobile-active': this.layoutService.state.staticMenuMobileActive,
            'p-input-filled': this.layoutService.config().inputStyle === 'filled',
            'p-ripple-disabled': !this.layoutService.config().ripple,
        };
    }

    ngOnInit() {
        if (localStorage.getItem('theme')) {
            this.layoutService.config.update((config) => ({
                ...config,
                theme: localStorage.getItem('theme'),
            }));
        }
        if (localStorage.getItem('colorScheme')) {
            this.layoutService.config.update((config) => ({
                ...config,
                colorScheme: localStorage.getItem('colorScheme'),
            }));
        }
        if (localStorage.getItem('scale')) {
            this.layoutService.config.update((config) => ({
                ...config,
                scale: Number(localStorage.getItem('scale')),
            }));
        }
        if (localStorage.getItem('lg')) {
            this.translocoService.setActiveLang(localStorage.getItem('lg'));
        }
    }

    ngOnDestroy() {
        if (this.overlayMenuOpenSubscription) {
            this.overlayMenuOpenSubscription.unsubscribe();
        }

        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
        }
    }
}
