{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TextAlignPipe {\n  transform(value) {\n    return typeof value === 'number' ? 'text-right' : 'text-left';\n  }\n  static #_ = this.ɵfac = function TextAlignPipe_Factory(t) {\n    return new (t || TextAlignPipe)();\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"textAlign\",\n    type: TextAlignPipe,\n    pure: true,\n    standalone: true\n  });\n}", "map": {"version": 3, "names": ["TextAlignPipe", "transform", "value", "_", "_2", "pure", "standalone"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\pipes\\text-align.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'textAlign',\r\n    standalone: true,\r\n})\r\nexport class TextAlignPipe implements PipeTransform {\r\n    transform(value: any): string {\r\n        return typeof value === 'number' ? 'text-right' : 'text-left';\r\n    }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,aAAa;EACtBC,SAASA,CAACC,KAAU;IAChB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,YAAY,GAAG,WAAW;EACjE;EAAC,QAAAC,CAAA,G;qBAHQH,aAAa;EAAA;EAAA,QAAAI,EAAA,G;;UAAbJ,aAAa;IAAAK,IAAA;IAAAC,UAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}