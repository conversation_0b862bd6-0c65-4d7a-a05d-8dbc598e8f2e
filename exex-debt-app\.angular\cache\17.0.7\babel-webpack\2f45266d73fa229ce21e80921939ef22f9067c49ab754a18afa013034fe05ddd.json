{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { CUSTOMER_COLS } from './customer-cols';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@services/customer.service\";\nimport * as i3 from \"@services/exex-common.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../common/exex-table/exex-table.component\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/toolbar\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/fileupload\";\nimport * as i12 from \"primeng/keyfilter\";\nfunction CustomerComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵelement(1, \"i\", 16)(2, \"input\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 18);\n    i0.ɵɵlistener(\"onClick\", function CustomerComponent_ng_template_26_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openNew());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 19);\n    i0.ɵɵlistener(\"onClick\", function CustomerComponent_ng_template_26_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.deleteSelectedProducts());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"p-fileUpload\", 20)(3, \"p-button\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedCustomers || !ctx_r1.selectedCustomers.length);\n  }\n}\nfunction CustomerComponent_ng_template_29_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 37);\n    i0.ɵɵtext(1, \" Customer Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_29_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 37);\n    i0.ɵɵtext(1, \" Valid Phone Number (10-15 digits) is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_29_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 37);\n    i0.ɵɵtext(1, \" Valid Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_29_small_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 37);\n    i0.ɵɵtext(1, \" Credit Limit must be a positive number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_29_small_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 37);\n    i0.ɵɵtext(1, \" Current Balance must be a positive number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 22)(1, \"div\", 23)(2, \"label\", 24);\n    i0.ɵɵtext(3, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 25);\n    i0.ɵɵtemplate(5, CustomerComponent_ng_template_29_small_5_Template, 2, 0, \"small\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"label\", 27);\n    i0.ɵɵtext(8, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 28);\n    i0.ɵɵtemplate(10, CustomerComponent_ng_template_29_small_10_Template, 2, 0, \"small\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 23)(12, \"label\", 29);\n    i0.ɵɵtext(13, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 30);\n    i0.ɵɵtemplate(15, CustomerComponent_ng_template_29_small_15_Template, 2, 0, \"small\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 23)(17, \"label\", 31);\n    i0.ɵɵtext(18, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 23)(21, \"label\", 33);\n    i0.ɵɵtext(22, \"Credit Limit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 34);\n    i0.ɵɵtemplate(24, CustomerComponent_ng_template_29_small_24_Template, 2, 0, \"small\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 23)(26, \"label\", 35);\n    i0.ɵɵtext(27, \"Current Balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 36);\n    i0.ɵɵtemplate(29, CustomerComponent_ng_template_29_small_29_Template, 2, 0, \"small\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.customerForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r2.customerForm.get(\"customerName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.customerForm.get(\"customerName\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.customerForm.get(\"phoneNumber\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.customerForm.get(\"phoneNumber\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.customerForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.customerForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.customerForm.get(\"creditLimit\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.customerForm.get(\"creditLimit\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r2.customerForm.get(\"currentBalance\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.customerForm.get(\"currentBalance\")) == null ? null : tmp_5_0.touched));\n  }\n}\nfunction CustomerComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 38);\n    i0.ɵɵlistener(\"onClick\", function CustomerComponent_ng_template_30_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.hideDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 39);\n    i0.ɵɵlistener(\"onClick\", function CustomerComponent_ng_template_30_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.saveCustomer());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true)(\"disabled\", ctx_r3.customerForm.invalid);\n  }\n}\nconst _c0 = () => ({\n  width: \"450px\"\n});\nexport class CustomerComponent {\n  constructor(fb, customerService, exexCommonService) {\n    this.fb = fb;\n    this.customerService = customerService;\n    this.exexCommonService = exexCommonService;\n    this.customerDialog = false;\n  }\n  ngOnInit() {\n    this.dataTable = {\n      ...this.dataTable,\n      columns: CUSTOMER_COLS\n    };\n    this.customerService.getCustomers().then(data => {\n      this.dataTable = {\n        ...this.dataTable,\n        value: data\n      };\n    });\n    this.customerForm = this.fb.group({\n      customerName: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\n      email: ['', [Validators.required, Validators.email]],\n      address: [''],\n      creditLimit: [null, [Validators.required, Validators.min(0)]],\n      currentBalance: [null, [Validators.required, Validators.min(0)]]\n    });\n  }\n  openNew() {\n    this.customerForm.reset();\n    this.customer = {};\n    this.customerDialog = true;\n  }\n  deleteSelectedProducts() {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => !this.selectedCustomers?.includes(val));\n      this.selectedCustomers = null;\n      this.exexCommonService.showToastSuccess('Customers Deleted');\n    });\n  }\n  selectedRow(rows) {\n    this.selectedCustomers = [...rows];\n  }\n  editProduct(customer) {\n    this.customer = {\n      ...customer\n    };\n    this.customerForm.patchValue({\n      customerName: this.customer.customerName,\n      phoneNumber: this.customer.phoneNumber,\n      email: this.customer.email,\n      address: this.customer.address,\n      creditLimit: this.customer.creditLimit,\n      currentBalance: this.customer.currentBalance,\n      status: this.customer.status,\n      currencyId: this.customer.currencyId\n    });\n    this.customerDialog = true;\n  }\n  deleteProduct(customer) {\n    this.exexCommonService.showDialogConfirm(() => {\n      this.dataTable.value = this.dataTable.value.filter(val => val.customerId !== customer.customerId);\n      this.customer = {};\n      this.exexCommonService.showToastSuccess('Customer Deleted');\n    });\n  }\n  hideDialog() {\n    this.customerDialog = false;\n  }\n  getActiveCustomers() {\n    return this.dataTable?.value?.filter(customer => customer.status === 'Active').length || 0;\n  }\n  getVipCustomers() {\n    return this.dataTable?.value?.filter(customer => customer.status === 'VIP').length || 0;\n  }\n  saveCustomer() {\n    if (this.customerForm.valid) {\n      if (this.customer.customerId) {\n        this.customer = [...this.customer, this.customerForm.value];\n        this.dataTable.value[this.findIndexById(this.customer.customerId)] = this.customer;\n        this.exexCommonService.showToastSuccess('Customer Updated');\n      } else {\n        this.dataTable.value.push(this.customerForm.value);\n        this.exexCommonService.showToastSuccess('Customer Updated');\n      }\n      this.dataTable.value = [...this.dataTable.value];\n      this.customerDialog = false;\n      this.customer = {};\n    } else {\n      this.customerForm.markAllAsTouched(); // Show validation errors\n    }\n  }\n\n  findIndexById(customerId) {\n    let index = -1;\n    for (let i = 0; i < this.dataTable.value.length; i++) {\n      if (this.dataTable.value[i].customerId === customerId) {\n        index = i;\n        break;\n      }\n    }\n    return index;\n  }\n  static #_ = this.ɵfac = function CustomerComponent_Factory(t) {\n    return new (t || CustomerComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ExexCommonService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CustomerComponent,\n    selectors: [[\"app-customer\"]],\n    decls: 31,\n    vars: 9,\n    consts: [[1, \"customer-container\"], [1, \"page-header\"], [1, \"page-title\"], [1, \"pi\", \"pi-users\"], [1, \"page-stats\"], [1, \"stat-card\"], [1, \"stat-number\"], [1, \"stat-label\"], [\"styleClass\", \"mb-3\"], [\"pTemplate\", \"left\"], [\"pTemplate\", \"right\"], [3, \"propExexTable\", \"selectedEvent\", \"editEvent\", \"deleteEvent\"], [\"header\", \"Customer Details\", \"styleClass\", \"p-fluid\", 3, \"visible\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search customers...\"], [\"severity\", \"success\", \"label\", \"New Customer\", \"icon\", \"pi pi-plus\", 1, \"mr-2\", 3, \"onClick\"], [\"severity\", \"danger\", \"label\", \"Delete\", \"icon\", \"pi pi-trash\", 1, \"mr-2\", 3, \"disabled\", \"onClick\"], [\"mode\", \"basic\", \"accept\", \".csv,.xls,.xlsx\", \"maxFileSize\", \"5000000\", \"label\", \"Import\", \"chooseLabel\", \"Import\", 1, \"mr-2\", \"inline-block\"], [\"severity\", \"help\", \"label\", \"Export\", \"icon\", \"pi pi-upload\"], [3, \"formGroup\"], [1, \"field\"], [\"for\", \"customerName\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"customerName\", \"formControlName\", \"customerName\", \"autofocus\", \"\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"phoneNumber\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\"], [\"for\", \"email\"], [\"type\", \"email\", \"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\"], [\"for\", \"address\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"address\", \"formControlName\", \"address\"], [\"for\", \"creditLimit\"], [\"pInputText\", \"\", \"pKeyFilter\", \"num\", \"id\", \"creditLimit\", \"formControlName\", \"creditLimit\"], [\"for\", \"currentBalance\"], [\"pInputText\", \"\", \"pKeyFilter\", \"num\", \"id\", \"currentBalance\", \"formControlName\", \"currentBalance\"], [1, \"p-error\"], [\"label\", \"Cancel\", \"icon\", \"pi pi-times\", 3, \"text\", \"onClick\"], [\"label\", \"Save\", \"icon\", \"pi pi-check\", 3, \"text\", \"disabled\", \"onClick\"]],\n    template: function CustomerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵelement(4, \"i\", 3);\n        i0.ɵɵtext(5, \" Customer Management \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"p\");\n        i0.ɵɵtext(7, \"Manage your customers efficiently with our comprehensive dashboard\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 5)(10, \"div\", 6);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 7);\n        i0.ɵɵtext(13, \"Total Customers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"div\", 6);\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 7);\n        i0.ɵɵtext(18, \"Active\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 5)(20, \"div\", 6);\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 7);\n        i0.ɵɵtext(23, \"VIP\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(24, \"p-toolbar\", 8);\n        i0.ɵɵtemplate(25, CustomerComponent_ng_template_25_Template, 3, 0, \"ng-template\", 9)(26, CustomerComponent_ng_template_26_Template, 4, 1, \"ng-template\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"exex-table\", 11);\n        i0.ɵɵlistener(\"selectedEvent\", function CustomerComponent_Template_exex_table_selectedEvent_27_listener($event) {\n          return ctx.selectedRow($event);\n        })(\"editEvent\", function CustomerComponent_Template_exex_table_editEvent_27_listener($event) {\n          return ctx.editProduct($event);\n        })(\"deleteEvent\", function CustomerComponent_Template_exex_table_deleteEvent_27_listener($event) {\n          return ctx.deleteProduct($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"p-dialog\", 12);\n        i0.ɵɵlistener(\"visibleChange\", function CustomerComponent_Template_p_dialog_visibleChange_28_listener($event) {\n          return ctx.customerDialog = $event;\n        });\n        i0.ɵɵtemplate(29, CustomerComponent_ng_template_29_Template, 30, 6, \"ng-template\", 13)(30, CustomerComponent_ng_template_30_Template, 2, 3, \"ng-template\", 14);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate((ctx.dataTable == null ? null : ctx.dataTable.value == null ? null : ctx.dataTable.value.length) || 0);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.getActiveCustomers());\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.getVipCustomers());\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"propExexTable\", ctx.dataTable);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(8, _c0));\n        i0.ɵɵproperty(\"visible\", ctx.customerDialog)(\"modal\", true);\n      }\n    },\n    dependencies: [i4.NgIf, i5.ExexTableComponent, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.PrimeTemplate, i7.Toolbar, i8.Dialog, i9.Button, i10.InputText, i11.FileUpload, i12.KeyFilter],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  padding: 0.75rem;\\n  background: var(--bg-gradient);\\n  min-height: 100vh;\\n}\\n\\n.p-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;\\n  border: none !important;\\n  border-radius: var(--border-radius-lg) !important;\\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3) !important;\\n  padding: 1rem 1.5rem !important;\\n  margin-bottom: 2rem !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-toolbar-group-left[_ngcontent-%COMP%], .p-toolbar[_ngcontent-%COMP%]   .p-toolbar-group-right[_ngcontent-%COMP%] {\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-input-icon-left[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-input-icon-left[_ngcontent-%COMP%]   .pi-search[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 1.1rem;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-input-icon-left[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95) !important;\\n  border: 2px solid transparent !important;\\n  border-radius: 25px !important;\\n  padding: 0.75rem 1rem 0.75rem 2.5rem !important;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-input-icon-left[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea !important;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;\\n  transform: translateY(-2px);\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-input-icon-left[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%] {\\n  border-radius: 8px !important;\\n  padding: 0.75rem 1.5rem !important;\\n  font-weight: 600 !important;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  transition: all 0.3s ease !important;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px) !important;\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;\\n  border: none !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-success[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #4a9428 0%, #96d4b8 100%) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;\\n  border: none !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-danger[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e6395f 0%, #e64327 100%) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-help[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;\\n  border: none !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-button.p-button-help[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #3d8bfe 0%, #00d9fe 100%) !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-fileupload[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;\\n  border: none !important;\\n}\\n.p-toolbar[_ngcontent-%COMP%]   .p-fileupload[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e85d87 0%, #e5c82d 100%) !important;\\n}\\n\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n  color: white !important;\\n  border-radius: 12px 12px 0 0 !important;\\n  padding: 1.5rem !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-title[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 1.25rem;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%]   .p-dialog-header-icon[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  padding: 2rem !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.95rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  padding: 0.75rem 1rem !important;\\n  border: 2px solid #e5e7eb !important;\\n  border-radius: 8px !important;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n  background: #f9fafb !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea !important;\\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;\\n  background: white !important;\\n  transform: translateY(-1px);\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover {\\n  border-color: #d1d5db !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-error[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-size: 0.875rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%] {\\n  background: #f9fafb !important;\\n  border-radius: 0 0 12px 12px !important;\\n  padding: 1.5rem !important;\\n  border-top: 1px solid #e5e7eb !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%] {\\n  margin: 0 0.5rem;\\n  padding: 0.75rem 2rem !important;\\n  font-weight: 600 !important;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  border-radius: 8px !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]   .p-button.p-button-text[_ngcontent-%COMP%] {\\n  background: transparent !important;\\n  color: #6b7280 !important;\\n  border: 2px solid #e5e7eb !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]   .p-button.p-button-text[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6 !important;\\n  color: #374151 !important;\\n  border-color: #d1d5db !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]   .p-button.p-button-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n  border: none !important;\\n}\\n.p-dialog[_ngcontent-%COMP%]   .p-dialog-footer[_ngcontent-%COMP%]   .p-button.p-button-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;\\n}\\n\\n.customer-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--border-radius-xl);\\n  box-shadow: var(--shadow-xl);\\n  overflow: hidden;\\n  margin: 1rem;\\n  position: relative;\\n}\\n.customer-container[_ngcontent-%COMP%]   .p-toolbar[_ngcontent-%COMP%], .customer-container[_ngcontent-%COMP%]   exex-table[_ngcontent-%COMP%] {\\n  margin: 0 2rem;\\n}\\n.customer-container[_ngcontent-%COMP%]   .p-toolbar[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  border-radius: var(--border-radius-lg) !important;\\n}\\n.customer-container[_ngcontent-%COMP%]   exex-table[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n[_nghost-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);\\n  color: white;\\n  padding: 2rem;\\n  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;\\n  margin-bottom: 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 2rem;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 0.75rem;\\n  border-radius: 50%;\\n  animation: pulse 2s infinite;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n  font-weight: 300;\\n  letter-spacing: 0.5px;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  flex-wrap: wrap;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: var(--border-radius-lg);\\n  padding: 1.5rem;\\n  text-align: center;\\n  min-width: 120px;\\n  transition: var(--transition-normal);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n.page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  margin-bottom: 0.5rem;\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n  opacity: 0.9;\\n  font-weight: 500;\\n}\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    justify-content: center;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    width: 100%;\\n  }\\n  .page-header[_ngcontent-%COMP%]   .page-stats[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 100px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2Rhc2hib2FyZC9jdXN0b21lci9jdXN0b21lci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNJLGNBQUE7RUFDQSxnQkFBQTtFQUNBLDhCQUFBO0VBQ0EsaUJBQUE7QUFBSjs7QUFJQTtFQUNJLG9HQUFBO0VBQ0EsdUJBQUE7RUFDQSxpREFBQTtFQUNBLDBEQUFBO0VBQ0EsK0JBQUE7RUFDQSw4QkFBQTtBQURKO0FBR0k7O0VBRUksbUJBQUE7RUFDQSxTQUFBO0FBRFI7QUFLSTtFQUNJLGtCQUFBO0FBSFI7QUFLUTtFQUNJLGNBQUE7RUFDQSxpQkFBQTtBQUhaO0FBTVE7RUFDSSxnREFBQTtFQUNBLHdDQUFBO0VBQ0EsOEJBQUE7RUFDQSwrQ0FBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSx5Q0FBQTtBQUpaO0FBTVk7RUFDSSxnQ0FBQTtFQUNBLDZEQUFBO0VBQ0EsMkJBQUE7QUFKaEI7QUFPWTtFQUNJLGNBQUE7RUFDQSxrQkFBQTtBQUxoQjtBQVdJO0VBQ0ksNkJBQUE7RUFDQSxrQ0FBQTtFQUNBLDJCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtFQUNBLG9DQUFBO0VBQ0Esb0RBQUE7QUFUUjtBQVdRO0VBQ0ksc0NBQUE7RUFDQSxvREFBQTtBQVRaO0FBWVE7RUFDSSxtQ0FBQTtBQVZaO0FBYVE7RUFDSSx3RUFBQTtFQUNBLHVCQUFBO0FBWFo7QUFhWTtFQUNJLHdFQUFBO0FBWGhCO0FBZVE7RUFDSSx3RUFBQTtFQUNBLHVCQUFBO0FBYlo7QUFlWTtFQUNJLHdFQUFBO0FBYmhCO0FBaUJRO0VBQ0ksd0VBQUE7RUFDQSx1QkFBQTtBQWZaO0FBaUJZO0VBQ0ksd0VBQUE7QUFmaEI7QUFzQlE7RUFDSSx3RUFBQTtFQUNBLHVCQUFBO0FBcEJaO0FBc0JZO0VBQ0ksd0VBQUE7QUFwQmhCOztBQTRCSTtFQUNJLHdFQUFBO0VBQ0EsdUJBQUE7RUFDQSx1Q0FBQTtFQUNBLDBCQUFBO0FBekJSO0FBMkJRO0VBQ0ksZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUF6Qlo7QUE0QlE7RUFDSSx1QkFBQTtBQTFCWjtBQTRCWTtFQUNJLCtDQUFBO0FBMUJoQjtBQStCSTtFQUNJLDhCQUFBO0VBQ0Esd0JBQUE7QUE3QlI7QUErQlE7RUFDSSxxQkFBQTtBQTdCWjtBQStCWTtFQUNJLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQTdCaEI7QUFnQ1k7RUFDSSxzQkFBQTtFQUNBLGdDQUFBO0VBQ0Esb0NBQUE7RUFDQSw2QkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLDhCQUFBO0FBOUJoQjtBQWdDZ0I7RUFDSSxnQ0FBQTtFQUNBLDZEQUFBO0VBQ0EsNEJBQUE7RUFDQSwyQkFBQTtBQTlCcEI7QUFpQ2dCO0VBQ0ksZ0NBQUE7QUEvQnBCO0FBbUNZO0VBQ0ksY0FBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQWpDaEI7QUFzQ0k7RUFDSSw4QkFBQTtFQUNBLHVDQUFBO0VBQ0EsMEJBQUE7RUFDQSx3Q0FBQTtBQXBDUjtBQXNDUTtFQUNJLGdCQUFBO0VBQ0EsZ0NBQUE7RUFDQSwyQkFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSw2QkFBQTtBQXBDWjtBQXNDWTtFQUNJLGtDQUFBO0VBQ0EseUJBQUE7RUFDQSxvQ0FBQTtBQXBDaEI7QUFzQ2dCO0VBQ0ksOEJBQUE7RUFDQSx5QkFBQTtFQUNBLGdDQUFBO0FBcENwQjtBQXdDWTtFQUNJLHdFQUFBO0VBQ0EsdUJBQUE7QUF0Q2hCO0FBd0NnQjtFQUNJLHdFQUFBO0VBQ0EsMkJBQUE7RUFDQSwwREFBQTtBQXRDcEI7O0FBOENBO0VBQ0ksaUJBQUE7RUFDQSxzQ0FBQTtFQUNBLDRCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7QUEzQ0o7QUE4Q0k7O0VBRUksY0FBQTtBQTVDUjtBQStDSTtFQUNJLGdCQUFBO0VBQ0EsaURBQUE7QUE3Q1I7QUFnREk7RUFDSSxtQkFBQTtBQTlDUjs7QUFtREE7RUFDSTtJQUNJLFVBQUE7SUFDQSwyQkFBQTtFQWhETjtFQWtERTtJQUNJLFVBQUE7SUFDQSx3QkFBQTtFQWhETjtBQUNGO0FBbURBO0VBQ0ksaUNBQUE7QUFqREo7O0FBcURBO0VBQ0kseUZBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGtFQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxTQUFBO0FBbERKO0FBb0RJO0VBQ0ksT0FBQTtFQUNBLGdCQUFBO0FBbERSO0FBb0RRO0VBQ0ksb0JBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUFsRFo7QUFvRFk7RUFDSSxlQUFBO0VBQ0Esb0NBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNEJBQUE7QUFsRGhCO0FBc0RRO0VBQ0ksU0FBQTtFQUNBLGlCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUFwRFo7QUF3REk7RUFDSSxhQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUF0RFI7QUF3RFE7RUFDSSxxQ0FBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7RUFDQSwwQ0FBQTtFQUNBLHNDQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQ0FBQTtBQXREWjtBQXdEWTtFQUNJLHFDQUFBO0VBQ0EsMkJBQUE7RUFDQSwwQ0FBQTtBQXREaEI7QUF5RFk7RUFDSSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQUFBO0VBQ0Esb0RBQUE7RUFDQSw2QkFBQTtFQUNBLG9DQUFBO0VBQ0EscUJBQUE7QUF2RGhCO0FBMERZO0VBQ0ksaUJBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0FBeERoQjtBQTZESTtFQXRGSjtJQXVGUSxzQkFBQTtJQUNBLGtCQUFBO0VBMUROO0VBNERNO0lBQ0ksZUFBQTtFQTFEVjtFQTREVTtJQUNJLGVBQUE7SUFDQSx1QkFBQTtFQTFEZDtFQThETTtJQUNJLHVCQUFBO0lBQ0EsV0FBQTtFQTVEVjtFQThEVTtJQUNJLE9BQUE7SUFDQSxnQkFBQTtFQTVEZDtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLy8gQ3VzdG9tZXIgQ29tcG9uZW50IFN0eWxlc1xyXG46aG9zdCB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIHBhZGRpbmc6IDAuNzVyZW07XHJcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1iZy1ncmFkaWVudCk7XHJcbiAgICBtaW4taGVpZ2h0OiAxMDB2aDtcclxufVxyXG5cclxuLy8gVG9vbGJhciBTdHlsaW5nXHJcbi5wLXRvb2xiYXIge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tcHJpbWFyeS1jb2xvcikgMCUsIHZhcigtLXNlY29uZGFyeS1jb2xvcikgMTAwJSkgIWltcG9ydGFudDtcclxuICAgIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50O1xyXG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy1sZykgIWltcG9ydGFudDtcclxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpICFpbXBvcnRhbnQ7XHJcbiAgICBwYWRkaW5nOiAxcmVtIDEuNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbSAhaW1wb3J0YW50O1xyXG5cclxuICAgIC5wLXRvb2xiYXItZ3JvdXAtbGVmdCxcclxuICAgIC5wLXRvb2xiYXItZ3JvdXAtcmlnaHQge1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZ2FwOiAxcmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNlYXJjaCBJbnB1dCBTdHlsaW5nXHJcbiAgICAucC1pbnB1dC1pY29uLWxlZnQge1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgLnBpLXNlYXJjaCB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjNjY3ZWVhO1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlucHV0IHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAyNXB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbSAwLjc1cmVtIDIuNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG5cclxuICAgICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM2NjdlZWEgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMjUpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICY6OnBsYWNlaG9sZGVyIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjOWNhM2FmO1xyXG4gICAgICAgICAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEJ1dHRvbiBTdHlsaW5nXHJcbiAgICAucC1idXR0b24ge1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDhweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMS41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMCAhaW1wb3J0YW50O1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4yKSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjMpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmFjdGl2ZSB7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJi5wLWJ1dHRvbi1zdWNjZXNzIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzU2YWIyZiAwJSwgI2E4ZTZjZiAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRhOTQyOCAwJSwgIzk2ZDRiOCAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmLnAtYnV0dG9uLWRhbmdlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjQxNmMgMCUsICNmZjRiMmIgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlNjM5NWYgMCUsICNlNjQzMjcgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJi5wLWJ1dHRvbi1oZWxwIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmYWNmZSAwJSwgIzAwZjJmZSAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNkOGJmZSAwJSwgIzAwZDlmZSAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEZpbGUgVXBsb2FkIFN0eWxpbmdcclxuICAgIC5wLWZpbGV1cGxvYWQge1xyXG4gICAgICAgIC5wLWJ1dHRvbiB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmYTcwOWEgMCUsICNmZWUxNDAgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlODVkODcgMCUsICNlNWM4MmQgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuLy8gRGlhbG9nIFN0eWxpbmdcclxuLnAtZGlhbG9nIHtcclxuICAgIC5wLWRpYWxvZy1oZWFkZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4IDEycHggMCAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgcGFkZGluZzogMS41cmVtICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgIC5wLWRpYWxvZy10aXRsZSB7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDFweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5wLWRpYWxvZy1oZWFkZXItaWNvbiB7XHJcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZiAhaW1wb3J0YW50O1xyXG4gICAgICAgIHBhZGRpbmc6IDJyZW0gIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgLmZpZWxkIHtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG5cclxuICAgICAgICAgICAgbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XHJcbiAgICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpbnB1dCB7XHJcbiAgICAgICAgICAgICAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbSAxcmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZTVlN2ViICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHggIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjlmYWZiICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNjY3ZWVhICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMC4ycmVtIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4yNSkgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNkMWQ1ZGIgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnAtZXJyb3Ige1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICNlZjQ0NDQ7XHJcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnAtZGlhbG9nLWZvb3RlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2Y5ZmFmYiAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDAgMCAxMnB4IDEycHggIWltcG9ydGFudDtcclxuICAgICAgICBwYWRkaW5nOiAxLjVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2U1ZTdlYiAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAucC1idXR0b24ge1xyXG4gICAgICAgICAgICBtYXJnaW46IDAgMC41cmVtO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtIDJyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweCAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgJi5wLWJ1dHRvbi10ZXh0IHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzZiNzI4MCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI2U1ZTdlYiAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmM2Y0ZjYgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzM3NDE1MSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2QxZDVkYiAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAmLnAtYnV0dG9uLXByaW1hcnkge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVhNmZkOCAwJSwgIzZhNDE5MCAxMDAlKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxuICAgICAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC40KSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyBDYXJkIENvbnRhaW5lciBmb3IgdGhlIHdob2xlIGNvbXBvbmVudFxyXG4uY3VzdG9tZXItY29udGFpbmVyIHtcclxuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy14bCk7XHJcbiAgICBib3gtc2hhZG93OiB2YXIoLS1zaGFkb3cteGwpO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIG1hcmdpbjogMXJlbTtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAvLyBUb29sYmFyIGFuZCB0YWJsZSBjb250ZW50IGFyZWFcclxuICAgIC5wLXRvb2xiYXIsXHJcbiAgICBleGV4LXRhYmxlIHtcclxuICAgICAgICBtYXJnaW46IDAgMnJlbTtcclxuICAgIH1cclxuXHJcbiAgICAucC10b29sYmFyIHtcclxuICAgICAgICBtYXJnaW4tdG9wOiAycmVtO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbGcpICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgZXhleC10YWJsZSB7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcclxuICAgIH1cclxufVxyXG5cclxuLy8gQW5pbWF0aW9uIGZvciBwYWdlIGxvYWRcclxuQGtleWZyYW1lcyBmYWRlSW5VcCB7XHJcbiAgICBmcm9tIHtcclxuICAgICAgICBvcGFjaXR5OiAwO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgzMHB4KTtcclxuICAgIH1cclxuICAgIHRvIHtcclxuICAgICAgICBvcGFjaXR5OiAxO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcclxuICAgIH1cclxufVxyXG5cclxuOmhvc3Qge1xyXG4gICAgYW5pbWF0aW9uOiBmYWRlSW5VcCAwLjZzIGVhc2Utb3V0O1xyXG59XHJcblxyXG4vLyBQYWdlIEhlYWRlciBTdHlsaW5nXHJcbi5wYWdlLWhlYWRlciB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1wcmltYXJ5LWNvbG9yKSAwJSwgdmFyKC0tc2Vjb25kYXJ5LWNvbG9yKSAxMDAlKTtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLWxnKSB2YXIoLS1ib3JkZXItcmFkaXVzLWxnKSAwIDA7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgICBnYXA6IDJyZW07XHJcblxyXG4gICAgLnBhZ2UtdGl0bGUge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgbWluLXdpZHRoOiAzMDBweDtcclxuXHJcbiAgICAgICAgaDEge1xyXG4gICAgICAgICAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAyLjVyZW07XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDFweDtcclxuXHJcbiAgICAgICAgICAgIGkge1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAycmVtO1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHAge1xyXG4gICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiAzMDA7XHJcbiAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnBhZ2Utc3RhdHMge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZ2FwOiAxLjVyZW07XHJcbiAgICAgICAgZmxleC13cmFwOiB3cmFwO1xyXG5cclxuICAgICAgICAuc3RhdC1jYXJkIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcclxuICAgICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbGcpO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgICAgbWluLXdpZHRoOiAxMjBweDtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbi1ub3JtYWwpO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMjUpO1xyXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAxMHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuc3RhdC1udW1iZXIge1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAyLjVyZW07XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICNmZmZmZmYsICNmMGYwZjApO1xyXG4gICAgICAgICAgICAgICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XHJcbiAgICAgICAgICAgICAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5zdGF0LWxhYmVsIHtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAgICAgICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAxcHg7XHJcbiAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG5cclxuICAgICAgICAucGFnZS10aXRsZSB7XHJcbiAgICAgICAgICAgIG1pbi13aWR0aDogYXV0bztcclxuXHJcbiAgICAgICAgICAgIGgxIHtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucGFnZS1zdGF0cyB7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAgICAgICAgIC5zdGF0LWNhcmQge1xyXG4gICAgICAgICAgICAgICAgZmxleDogMTtcclxuICAgICAgICAgICAgICAgIG1pbi13aWR0aDogMTAwcHg7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "CUSTOMER_COLS", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "CustomerComponent_ng_template_26_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openNew", "CustomerComponent_ng_template_26_Template_p_button_onClick_1_listener", "ctx_r6", "deleteSelectedProducts", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "selectedCustomers", "length", "ɵɵtext", "ɵɵtemplate", "CustomerComponent_ng_template_29_small_5_Template", "CustomerComponent_ng_template_29_small_10_Template", "CustomerComponent_ng_template_29_small_15_Template", "CustomerComponent_ng_template_29_small_24_Template", "CustomerComponent_ng_template_29_small_29_Template", "ctx_r2", "customerForm", "tmp_1_0", "get", "invalid", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "CustomerComponent_ng_template_30_Template_p_button_onClick_0_listener", "_r13", "ctx_r12", "hideDialog", "CustomerComponent_ng_template_30_Template_p_button_onClick_1_listener", "ctx_r14", "saveCustomer", "ctx_r3", "CustomerComponent", "constructor", "fb", "customerService", "exexCommonService", "customerDialog", "ngOnInit", "dataTable", "columns", "getCustomers", "then", "data", "value", "group", "customerName", "required", "phoneNumber", "pattern", "email", "address", "creditLimit", "min", "currentBalance", "reset", "customer", "showDialogConfirm", "filter", "val", "includes", "showToastSuccess", "selectedRow", "rows", "editProduct", "patchValue", "status", "currencyId", "deleteProduct", "customerId", "getActiveCustomers", "getVipCustomers", "valid", "findIndexById", "push", "mark<PERSON>llAsTouched", "index", "i", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "CustomerService", "i3", "ExexCommonService", "_2", "selectors", "decls", "vars", "consts", "template", "CustomerComponent_Template", "rf", "ctx", "CustomerComponent_ng_template_25_Template", "CustomerComponent_ng_template_26_Template", "CustomerComponent_Template_exex_table_selectedEvent_27_listener", "$event", "CustomerComponent_Template_exex_table_editEvent_27_listener", "CustomerComponent_Template_exex_table_deleteEvent_27_listener", "CustomerComponent_Template_p_dialog_visibleChange_28_listener", "CustomerComponent_ng_template_29_Template", "CustomerComponent_ng_template_30_Template", "ɵɵtextInterpolate", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { CustomerService } from '@services/customer.service';\r\nimport { ExexCommonService } from '@services/exex-common.service';\r\nimport { IPropExexTable } from '../../common/exex-table/exex-table.model';\r\nimport { CUSTOMER_COLS } from './customer-cols';\r\n\r\n@Component({\r\n    selector: 'app-customer',\r\n    templateUrl: './customer.component.html',\r\n    styleUrl: './customer.component.scss',\r\n})\r\nexport class CustomerComponent {\r\n    customerDialog: boolean = false;\r\n\r\n    customers!: any[];\r\n\r\n    customer!: any;\r\n\r\n    selectedCustomers!: any[] | null;\r\n\r\n    customerForm!: FormGroup;\r\n\r\n    dataTable!: IPropExexTable;\r\n\r\n    constructor(\r\n        private fb: FormBuilder,\r\n        private customerService: CustomerService,\r\n        private exexCommonService: ExexCommonService,\r\n    ) {}\r\n\r\n    ngOnInit() {\r\n        this.dataTable = {\r\n            ...this.dataTable,\r\n            columns: CUSTOMER_COLS,\r\n        };\r\n\r\n        this.customerService.getCustomers().then((data) => {\r\n            this.dataTable = {\r\n                ...this.dataTable,\r\n                value: data,\r\n            };\r\n        });\r\n\r\n        this.customerForm = this.fb.group({\r\n            customerName: ['', Validators.required],\r\n            phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,15}$/)]],\r\n            email: ['', [Validators.required, Validators.email]],\r\n            address: [''],\r\n            creditLimit: [null, [Validators.required, Validators.min(0)]],\r\n            currentBalance: [null, [Validators.required, Validators.min(0)]],\r\n        });\r\n    }\r\n\r\n    openNew() {\r\n        this.customerForm.reset();\r\n        this.customer = {};\r\n        this.customerDialog = true;\r\n    }\r\n\r\n    deleteSelectedProducts() {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => !this.selectedCustomers?.includes(val));\r\n            this.selectedCustomers = null;\r\n            this.exexCommonService.showToastSuccess('Customers Deleted');\r\n        });\r\n    }\r\n\r\n    selectedRow(rows) {\r\n        this.selectedCustomers = [...rows];\r\n    }\r\n\r\n    editProduct(customer: any) {\r\n        this.customer = { ...customer };\r\n        this.customerForm.patchValue({\r\n            customerName: this.customer.customerName,\r\n            phoneNumber: this.customer.phoneNumber,\r\n            email: this.customer.email,\r\n            address: this.customer.address,\r\n            creditLimit: this.customer.creditLimit,\r\n            currentBalance: this.customer.currentBalance,\r\n            status: this.customer.status,\r\n            currencyId: this.customer.currencyId,\r\n        });\r\n        this.customerDialog = true;\r\n    }\r\n\r\n    deleteProduct(customer: any) {\r\n        this.exexCommonService.showDialogConfirm(() => {\r\n            this.dataTable.value = this.dataTable.value.filter((val) => val.customerId !== customer.customerId);\r\n            this.customer = {};\r\n            this.exexCommonService.showToastSuccess('Customer Deleted');\r\n        });\r\n    }\r\n\r\n    hideDialog() {\r\n        this.customerDialog = false;\r\n    }\r\n\r\n    getActiveCustomers(): number {\r\n        return this.dataTable?.value?.filter(customer => customer.status === 'Active').length || 0;\r\n    }\r\n\r\n    getVipCustomers(): number {\r\n        return this.dataTable?.value?.filter(customer => customer.status === 'VIP').length || 0;\r\n    }\r\n\r\n    saveCustomer() {\r\n        if (this.customerForm.valid) {\r\n            if (this.customer.customerId) {\r\n                this.customer = [...this.customer, this.customerForm.value];\r\n                this.dataTable.value[this.findIndexById(this.customer.customerId)] = this.customer;\r\n                this.exexCommonService.showToastSuccess('Customer Updated');\r\n            } else {\r\n                this.dataTable.value.push(this.customerForm.value);\r\n                this.exexCommonService.showToastSuccess('Customer Updated');\r\n            }\r\n\r\n            this.dataTable.value = [...this.dataTable.value];\r\n            this.customerDialog = false;\r\n            this.customer = {};\r\n        } else {\r\n            this.customerForm.markAllAsTouched(); // Show validation errors\r\n        }\r\n    }\r\n\r\n    findIndexById(customerId: string): number {\r\n        let index = -1;\r\n        for (let i = 0; i < this.dataTable.value.length; i++) {\r\n            if (this.dataTable.value[i].customerId === customerId) {\r\n                index = i;\r\n                break;\r\n            }\r\n        }\r\n        return index;\r\n    }\r\n}\r\n", "<div class=\"customer-container\">\r\n    <div class=\"page-header\">\r\n        <div class=\"page-title\">\r\n            <h1>\r\n                <i class=\"pi pi-users\"></i>\r\n                Customer Management\r\n            </h1>\r\n            <p>Manage your customers efficiently with our comprehensive dashboard</p>\r\n        </div>\r\n        <div class=\"page-stats\">\r\n            <div class=\"stat-card\">\r\n                <div class=\"stat-number\">{{ dataTable?.value?.length || 0 }}</div>\r\n                <div class=\"stat-label\">Total Customers</div>\r\n            </div>\r\n            <div class=\"stat-card\">\r\n                <div class=\"stat-number\">{{ getActiveCustomers() }}</div>\r\n                <div class=\"stat-label\">Active</div>\r\n            </div>\r\n            <div class=\"stat-card\">\r\n                <div class=\"stat-number\">{{ getVipCustomers() }}</div>\r\n                <div class=\"stat-label\">VIP</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <p-toolbar styleClass=\"mb-3\">\r\n        <ng-template pTemplate=\"left\">\r\n            <span class=\"p-input-icon-left\">\r\n                <i class=\"pi pi-search\"></i>\r\n                <input pInputText type=\"text\" placeholder=\"Search customers...\" />\r\n            </span>\r\n        </ng-template>\r\n\r\n        <ng-template pTemplate=\"right\">\r\n            <p-button severity=\"success\" label=\"New Customer\" icon=\"pi pi-plus\" class=\"mr-2\" (onClick)=\"openNew()\" />\r\n            <p-button\r\n                severity=\"danger\"\r\n                label=\"Delete\"\r\n                icon=\"pi pi-trash\"\r\n                class=\"mr-2\"\r\n                (onClick)=\"deleteSelectedProducts()\"\r\n                [disabled]=\"!selectedCustomers || !selectedCustomers.length\" />\r\n\r\n            <p-fileUpload\r\n                mode=\"basic\"\r\n                accept=\".csv,.xls,.xlsx\"\r\n                maxFileSize=\"5000000\"\r\n                label=\"Import\"\r\n                chooseLabel=\"Import\"\r\n                class=\"mr-2 inline-block\" />\r\n            <p-button severity=\"help\" label=\"Export\" icon=\"pi pi-upload\" />\r\n        </ng-template>\r\n    </p-toolbar>\r\n\r\n    <exex-table\r\n        [propExexTable]=\"dataTable\"\r\n        (selectedEvent)=\"selectedRow($event)\"\r\n        (editEvent)=\"editProduct($event)\"\r\n        (deleteEvent)=\"deleteProduct($event)\"></exex-table>\r\n</div>\r\n\r\n<p-dialog\r\n    [(visible)]=\"customerDialog\"\r\n    [style]=\"{ width: '450px' }\"\r\n    header=\"Customer Details\"\r\n    [modal]=\"true\"\r\n    styleClass=\"p-fluid\">\r\n    <ng-template pTemplate=\"content\">\r\n        <form [formGroup]=\"customerForm\">\r\n            <div class=\"field\">\r\n                <label for=\"customerName\">Customer Name</label>\r\n                <input type=\"text\" pInputText id=\"customerName\" formControlName=\"customerName\" autofocus />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"customerForm.get('customerName')?.invalid && customerForm.get('customerName')?.touched\">\r\n                    Customer Name is required.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"phoneNumber\">Phone Number</label>\r\n                <input type=\"text\" pInputText id=\"phoneNumber\" formControlName=\"phoneNumber\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"customerForm.get('phoneNumber')?.invalid && customerForm.get('phoneNumber')?.touched\">\r\n                    Valid Phone Number (10-15 digits) is required.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"email\">Email</label>\r\n                <input type=\"email\" pInputText id=\"email\" formControlName=\"email\" />\r\n                <small class=\"p-error\" *ngIf=\"customerForm.get('email')?.invalid && customerForm.get('email')?.touched\">\r\n                    Valid Email is required.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"address\">Address</label>\r\n                <input type=\"text\" pInputText id=\"address\" formControlName=\"address\" />\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"creditLimit\">Credit Limit</label>\r\n                <input pInputText pKeyFilter=\"num\" id=\"creditLimit\" formControlName=\"creditLimit\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"customerForm.get('creditLimit')?.invalid && customerForm.get('creditLimit')?.touched\">\r\n                    Credit Limit must be a positive number.\r\n                </small>\r\n            </div>\r\n\r\n            <div class=\"field\">\r\n                <label for=\"currentBalance\">Current Balance</label>\r\n                <input pInputText pKeyFilter=\"num\" id=\"currentBalance\" formControlName=\"currentBalance\" />\r\n                <small\r\n                    class=\"p-error\"\r\n                    *ngIf=\"customerForm.get('currentBalance')?.invalid && customerForm.get('currentBalance')?.touched\">\r\n                    Current Balance must be a positive number.\r\n                </small>\r\n            </div>\r\n        </form>\r\n    </ng-template>\r\n\r\n    <ng-template pTemplate=\"footer\">\r\n        <p-button label=\"Cancel\" icon=\"pi pi-times\" [text]=\"true\" (onClick)=\"hideDialog()\" />\r\n        <p-button\r\n            label=\"Save\"\r\n            icon=\"pi pi-check\"\r\n            [text]=\"true\"\r\n            (onClick)=\"saveCustomer()\"\r\n            [disabled]=\"customerForm.invalid\" />\r\n    </ng-template>\r\n</p-dialog>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,SAASC,aAAa,QAAQ,iBAAiB;;;;;;;;;;;;;;;;ICsBnCC,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAIPH,EAAA,CAAAC,cAAA,mBAAyG;IAAxBD,EAAA,CAAAI,UAAA,qBAAAC,sEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAAtGX,EAAA,CAAAG,YAAA,EAAyG;IACzGH,EAAA,CAAAC,cAAA,mBAMmE;IAD/DD,EAAA,CAAAI,UAAA,qBAAAQ,sEAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAG,MAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IALxCd,EAAA,CAAAG,YAAA,EAMmE;IAEnEH,EAAA,CAAAE,SAAA,uBAMgC;;;;IAR5BF,EAAA,CAAAe,SAAA,GAA4D;IAA5Df,EAAA,CAAAgB,UAAA,cAAAC,MAAA,CAAAC,iBAAA,KAAAD,MAAA,CAAAC,iBAAA,CAAAC,MAAA,CAA4D;;;;;IA+B5DnB,EAAA,CAAAC,cAAA,gBAEmG;IAC/FD,EAAA,CAAAoB,MAAA,mCACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMRH,EAAA,CAAAC,cAAA,gBAEiG;IAC7FD,EAAA,CAAAoB,MAAA,uDACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMRH,EAAA,CAAAC,cAAA,gBAAwG;IACpGD,EAAA,CAAAoB,MAAA,iCACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAWRH,EAAA,CAAAC,cAAA,gBAEiG;IAC7FD,EAAA,CAAAoB,MAAA,gDACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMRH,EAAA,CAAAC,cAAA,gBAEuG;IACnGD,EAAA,CAAAoB,MAAA,mDACJ;IAAApB,EAAA,CAAAG,YAAA,EAAQ;;;;;IAnDhBH,EAAA,CAAAC,cAAA,eAAiC;IAECD,EAAA,CAAAoB,MAAA,oBAAa;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAC/CH,EAAA,CAAAE,SAAA,gBAA2F;IAC3FF,EAAA,CAAAqB,UAAA,IAAAC,iDAAA,oBAIQ;IACZtB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAmB;IACUD,EAAA,CAAAoB,MAAA,mBAAY;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAE,SAAA,gBAA+E;IAC/EF,EAAA,CAAAqB,UAAA,KAAAE,kDAAA,oBAIQ;IACZvB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAmB;IACID,EAAA,CAAAoB,MAAA,aAAK;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAE,SAAA,iBAAoE;IACpEF,EAAA,CAAAqB,UAAA,KAAAG,kDAAA,oBAEQ;IACZxB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAmB;IACMD,EAAA,CAAAoB,MAAA,eAAO;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IACpCH,EAAA,CAAAE,SAAA,iBAAuE;IAC3EF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAmB;IACUD,EAAA,CAAAoB,MAAA,oBAAY;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAE,SAAA,iBAAoF;IACpFF,EAAA,CAAAqB,UAAA,KAAAI,kDAAA,oBAIQ;IACZzB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAmB;IACaD,EAAA,CAAAoB,MAAA,uBAAe;IAAApB,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAE,SAAA,iBAA0F;IAC1FF,EAAA,CAAAqB,UAAA,KAAAK,kDAAA,oBAIQ;IACZ1B,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;IApDJH,EAAA,CAAAgB,UAAA,cAAAW,MAAA,CAAAC,YAAA,CAA0B;IAMnB5B,EAAA,CAAAe,SAAA,GAA4F;IAA5Ff,EAAA,CAAAgB,UAAA,WAAAa,OAAA,GAAAF,MAAA,CAAAC,YAAA,CAAAE,GAAA,mCAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAF,MAAA,CAAAC,YAAA,CAAAE,GAAA,mCAAAD,OAAA,CAAAG,OAAA,EAA4F;IAU5FhC,EAAA,CAAAe,SAAA,GAA0F;IAA1Ff,EAAA,CAAAgB,UAAA,WAAAiB,OAAA,GAAAN,MAAA,CAAAC,YAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAN,MAAA,CAAAC,YAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAD,OAAA,EAA0F;IAQvEhC,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAAgB,UAAA,WAAAkB,OAAA,GAAAP,MAAA,CAAAC,YAAA,CAAAE,GAAA,4BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAP,MAAA,CAAAC,YAAA,CAAAE,GAAA,4BAAAI,OAAA,CAAAF,OAAA,EAA8E;IAejGhC,EAAA,CAAAe,SAAA,GAA0F;IAA1Ff,EAAA,CAAAgB,UAAA,WAAAmB,OAAA,GAAAR,MAAA,CAAAC,YAAA,CAAAE,GAAA,kCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAR,MAAA,CAAAC,YAAA,CAAAE,GAAA,kCAAAK,OAAA,CAAAH,OAAA,EAA0F;IAU1FhC,EAAA,CAAAe,SAAA,GAAgG;IAAhGf,EAAA,CAAAgB,UAAA,WAAAoB,OAAA,GAAAT,MAAA,CAAAC,YAAA,CAAAE,GAAA,qCAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAT,MAAA,CAAAC,YAAA,CAAAE,GAAA,qCAAAM,OAAA,CAAAJ,OAAA,EAAgG;;;;;;IAQ7GhC,EAAA,CAAAC,cAAA,mBAAqF;IAA3BD,EAAA,CAAAI,UAAA,qBAAAiC,sEAAA;MAAArC,EAAA,CAAAM,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAA6B,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAlFxC,EAAA,CAAAG,YAAA,EAAqF;IACrFH,EAAA,CAAAC,cAAA,mBAKwC;IADpCD,EAAA,CAAAI,UAAA,qBAAAqC,sEAAA;MAAAzC,EAAA,CAAAM,aAAA,CAAAgC,IAAA;MAAA,MAAAI,OAAA,GAAA1C,EAAA,CAAAS,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAgC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAJ9B3C,EAAA,CAAAG,YAAA,EAKwC;;;;IANIH,EAAA,CAAAgB,UAAA,cAAa;IAIrDhB,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,UAAA,cAAa,aAAA4B,MAAA,CAAAhB,YAAA,CAAAG,OAAA;;;;;;ADrHzB,OAAM,MAAOc,iBAAiB;EAa1BC,YACYC,EAAe,EACfC,eAAgC,EAChCC,iBAAoC;IAFpC,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAf7B,KAAAC,cAAc,GAAY,KAAK;EAgB5B;EAEHC,QAAQA,CAAA;IACJ,IAAI,CAACC,SAAS,GAAG;MACb,GAAG,IAAI,CAACA,SAAS;MACjBC,OAAO,EAAEtD;KACZ;IAED,IAAI,CAACiD,eAAe,CAACM,YAAY,EAAE,CAACC,IAAI,CAAEC,IAAI,IAAI;MAC9C,IAAI,CAACJ,SAAS,GAAG;QACb,GAAG,IAAI,CAACA,SAAS;QACjBK,KAAK,EAAED;OACV;IACL,CAAC,CAAC;IAEF,IAAI,CAAC5B,YAAY,GAAG,IAAI,CAACmB,EAAE,CAACW,KAAK,CAAC;MAC9BC,YAAY,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;MACvCC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACgE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;MAC9EC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACiE,KAAK,CAAC,CAAC;MACpDC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,IAAI,EAAE,CAACnE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DC,cAAc,EAAE,CAAC,IAAI,EAAE,CAACrE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACoE,GAAG,CAAC,CAAC,CAAC,CAAC;KAClE,CAAC;EACN;EAEAvD,OAAOA,CAAA;IACH,IAAI,CAACiB,YAAY,CAACwC,KAAK,EAAE;IACzB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACnB,cAAc,GAAG,IAAI;EAC9B;EAEApC,sBAAsBA,CAAA;IAClB,IAAI,CAACmC,iBAAiB,CAACqB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAAClB,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACc,MAAM,CAAEC,GAAG,IAAK,CAAC,IAAI,CAACtD,iBAAiB,EAAEuD,QAAQ,CAACD,GAAG,CAAC,CAAC;MACnG,IAAI,CAACtD,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAC+B,iBAAiB,CAACyB,gBAAgB,CAAC,mBAAmB,CAAC;IAChE,CAAC,CAAC;EACN;EAEAC,WAAWA,CAACC,IAAI;IACZ,IAAI,CAAC1D,iBAAiB,GAAG,CAAC,GAAG0D,IAAI,CAAC;EACtC;EAEAC,WAAWA,CAACR,QAAa;IACrB,IAAI,CAACA,QAAQ,GAAG;MAAE,GAAGA;IAAQ,CAAE;IAC/B,IAAI,CAACzC,YAAY,CAACkD,UAAU,CAAC;MACzBnB,YAAY,EAAE,IAAI,CAACU,QAAQ,CAACV,YAAY;MACxCE,WAAW,EAAE,IAAI,CAACQ,QAAQ,CAACR,WAAW;MACtCE,KAAK,EAAE,IAAI,CAACM,QAAQ,CAACN,KAAK;MAC1BC,OAAO,EAAE,IAAI,CAACK,QAAQ,CAACL,OAAO;MAC9BC,WAAW,EAAE,IAAI,CAACI,QAAQ,CAACJ,WAAW;MACtCE,cAAc,EAAE,IAAI,CAACE,QAAQ,CAACF,cAAc;MAC5CY,MAAM,EAAE,IAAI,CAACV,QAAQ,CAACU,MAAM;MAC5BC,UAAU,EAAE,IAAI,CAACX,QAAQ,CAACW;KAC7B,CAAC;IACF,IAAI,CAAC9B,cAAc,GAAG,IAAI;EAC9B;EAEA+B,aAAaA,CAACZ,QAAa;IACvB,IAAI,CAACpB,iBAAiB,CAACqB,iBAAiB,CAAC,MAAK;MAC1C,IAAI,CAAClB,SAAS,CAACK,KAAK,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAACc,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACU,UAAU,KAAKb,QAAQ,CAACa,UAAU,CAAC;MACnG,IAAI,CAACb,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACpB,iBAAiB,CAACyB,gBAAgB,CAAC,kBAAkB,CAAC;IAC/D,CAAC,CAAC;EACN;EAEAlC,UAAUA,CAAA;IACN,IAAI,CAACU,cAAc,GAAG,KAAK;EAC/B;EAEAiC,kBAAkBA,CAAA;IACd,OAAO,IAAI,CAAC/B,SAAS,EAAEK,KAAK,EAAEc,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACU,MAAM,KAAK,QAAQ,CAAC,CAAC5D,MAAM,IAAI,CAAC;EAC9F;EAEAiE,eAAeA,CAAA;IACX,OAAO,IAAI,CAAChC,SAAS,EAAEK,KAAK,EAAEc,MAAM,CAACF,QAAQ,IAAIA,QAAQ,CAACU,MAAM,KAAK,KAAK,CAAC,CAAC5D,MAAM,IAAI,CAAC;EAC3F;EAEAwB,YAAYA,CAAA;IACR,IAAI,IAAI,CAACf,YAAY,CAACyD,KAAK,EAAE;MACzB,IAAI,IAAI,CAAChB,QAAQ,CAACa,UAAU,EAAE;QAC1B,IAAI,CAACb,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,IAAI,CAACzC,YAAY,CAAC6B,KAAK,CAAC;QAC3D,IAAI,CAACL,SAAS,CAACK,KAAK,CAAC,IAAI,CAAC6B,aAAa,CAAC,IAAI,CAACjB,QAAQ,CAACa,UAAU,CAAC,CAAC,GAAG,IAAI,CAACb,QAAQ;QAClF,IAAI,CAACpB,iBAAiB,CAACyB,gBAAgB,CAAC,kBAAkB,CAAC;OAC9D,MAAM;QACH,IAAI,CAACtB,SAAS,CAACK,KAAK,CAAC8B,IAAI,CAAC,IAAI,CAAC3D,YAAY,CAAC6B,KAAK,CAAC;QAClD,IAAI,CAACR,iBAAiB,CAACyB,gBAAgB,CAAC,kBAAkB,CAAC;;MAG/D,IAAI,CAACtB,SAAS,CAACK,KAAK,GAAG,CAAC,GAAG,IAAI,CAACL,SAAS,CAACK,KAAK,CAAC;MAChD,IAAI,CAACP,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACmB,QAAQ,GAAG,EAAE;KACrB,MAAM;MACH,IAAI,CAACzC,YAAY,CAAC4D,gBAAgB,EAAE,CAAC,CAAC;;EAE9C;;EAEAF,aAAaA,CAACJ,UAAkB;IAC5B,IAAIO,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtC,SAAS,CAACK,KAAK,CAACtC,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAClD,IAAI,IAAI,CAACtC,SAAS,CAACK,KAAK,CAACiC,CAAC,CAAC,CAACR,UAAU,KAAKA,UAAU,EAAE;QACnDO,KAAK,GAAGC,CAAC;QACT;;;IAGR,OAAOD,KAAK;EAChB;EAAC,QAAAE,CAAA,G;qBA3HQ9C,iBAAiB,EAAA7C,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9F,EAAA,CAAA4F,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAhG,EAAA,CAAA4F,iBAAA,CAAAK,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBtD,iBAAiB;IAAAuD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ9B1G,EAAA,CAAAC,cAAA,aAAgC;QAIhBD,EAAA,CAAAE,SAAA,WAA2B;QAC3BF,EAAA,CAAAoB,MAAA,4BACJ;QAAApB,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAoB,MAAA,yEAAkE;QAAApB,EAAA,CAAAG,YAAA,EAAI;QAE7EH,EAAA,CAAAC,cAAA,aAAwB;QAESD,EAAA,CAAAoB,MAAA,IAAmC;QAAApB,EAAA,CAAAG,YAAA,EAAM;QAClEH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAoB,MAAA,uBAAe;QAAApB,EAAA,CAAAG,YAAA,EAAM;QAEjDH,EAAA,CAAAC,cAAA,cAAuB;QACMD,EAAA,CAAAoB,MAAA,IAA0B;QAAApB,EAAA,CAAAG,YAAA,EAAM;QACzDH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAoB,MAAA,cAAM;QAAApB,EAAA,CAAAG,YAAA,EAAM;QAExCH,EAAA,CAAAC,cAAA,cAAuB;QACMD,EAAA,CAAAoB,MAAA,IAAuB;QAAApB,EAAA,CAAAG,YAAA,EAAM;QACtDH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAoB,MAAA,WAAG;QAAApB,EAAA,CAAAG,YAAA,EAAM;QAK7CH,EAAA,CAAAC,cAAA,oBAA6B;QACzBD,EAAA,CAAAqB,UAAA,KAAAuF,yCAAA,yBAKc,KAAAC,yCAAA;QAqBlB7G,EAAA,CAAAG,YAAA,EAAY;QAEZH,EAAA,CAAAC,cAAA,sBAI0C;QAFtCD,EAAA,CAAAI,UAAA,2BAAA0G,gEAAAC,MAAA;UAAA,OAAiBJ,GAAA,CAAAhC,WAAA,CAAAoC,MAAA,CAAmB;QAAA,EAAC,uBAAAC,4DAAAD,MAAA;UAAA,OACxBJ,GAAA,CAAA9B,WAAA,CAAAkC,MAAA,CAAmB;QAAA,EADK,yBAAAE,8DAAAF,MAAA;UAAA,OAEtBJ,GAAA,CAAA1B,aAAA,CAAA8B,MAAA,CAAqB;QAAA,EAFC;QAEC/G,EAAA,CAAAG,YAAA,EAAa;QAG3DH,EAAA,CAAAC,cAAA,oBAKyB;QAJrBD,EAAA,CAAAI,UAAA,2BAAA8G,8DAAAH,MAAA;UAAA,OAAAJ,GAAA,CAAAzD,cAAA,GAAA6D,MAAA;QAAA,EAA4B;QAK5B/G,EAAA,CAAAqB,UAAA,KAAA8F,yCAAA,2BAuDc,KAAAC,yCAAA;QAWlBpH,EAAA,CAAAG,YAAA,EAAW;;;QA1H8BH,EAAA,CAAAe,SAAA,IAAmC;QAAnCf,EAAA,CAAAqH,iBAAA,EAAAV,GAAA,CAAAvD,SAAA,kBAAAuD,GAAA,CAAAvD,SAAA,CAAAK,KAAA,kBAAAkD,GAAA,CAAAvD,SAAA,CAAAK,KAAA,CAAAtC,MAAA,OAAmC;QAInCnB,EAAA,CAAAe,SAAA,GAA0B;QAA1Bf,EAAA,CAAAqH,iBAAA,CAAAV,GAAA,CAAAxB,kBAAA,GAA0B;QAI1BnF,EAAA,CAAAe,SAAA,GAAuB;QAAvBf,EAAA,CAAAqH,iBAAA,CAAAV,GAAA,CAAAvB,eAAA,GAAuB;QAoCxDpF,EAAA,CAAAe,SAAA,GAA2B;QAA3Bf,EAAA,CAAAgB,UAAA,kBAAA2F,GAAA,CAAAvD,SAAA,CAA2B;QAQ/BpD,EAAA,CAAAe,SAAA,GAA4B;QAA5Bf,EAAA,CAAAsH,UAAA,CAAAtH,EAAA,CAAAuH,eAAA,IAAAC,GAAA,EAA4B;QAD5BxH,EAAA,CAAAgB,UAAA,YAAA2F,GAAA,CAAAzD,cAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}