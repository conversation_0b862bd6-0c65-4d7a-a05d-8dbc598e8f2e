{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { AvatarModule } from 'primeng/avatar';\nimport { ButtonModule } from 'primeng/button';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { DialogModule } from 'primeng/dialog';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { KeyFilterModule } from 'primeng/keyfilter';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { ToastModule } from 'primeng/toast';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { ExexCommonModule } from '../common/exex-common.module';\nimport { CustomerComponent } from './customer/customer.component';\nimport { DashboardsRoutingModule } from './dashboard-routing.module';\nimport { InvoiceComponent } from './invoice/invoice.component';\nimport { UserComponent } from './user/user.component';\nlet DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [CustomerComponent, UserComponent, InvoiceComponent],\n  imports: [CommonModule, ExexCommonModule, DashboardsRoutingModule, FormsModule, ReactiveFormsModule, ToastModule, ToolbarModule, DialogModule, ConfirmDialogModule, InputTextModule, FileUploadModule, InputTextareaModule, KeyFilterModule, AvatarModule, SkeletonModule, ButtonModule]\n})], DashboardModule);\nexport { DashboardModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "FormsModule", "ReactiveFormsModule", "AvatarModule", "ButtonModule", "ConfirmDialogModule", "DialogModule", "FileUploadModule", "InputTextModule", "InputTextareaModule", "KeyFilterModule", "SkeletonModule", "ToastModule", "ToolbarModule", "ExexCommonModule", "CustomerComponent", "DashboardsRoutingModule", "InvoiceComponent", "UserComponent", "DashboardModule", "__decorate", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AvatarModule } from 'primeng/avatar';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { FileUploadModule } from 'primeng/fileupload';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { KeyFilterModule } from 'primeng/keyfilter';\r\nimport { SkeletonModule } from 'primeng/skeleton';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { ExexCommonModule } from '../common/exex-common.module';\r\nimport { CustomerComponent } from './customer/customer.component';\r\nimport { DashboardsRoutingModule } from './dashboard-routing.module';\r\nimport { InvoiceComponent } from './invoice/invoice.component';\r\nimport { UserComponent } from './user/user.component';\r\n@NgModule({\r\n    declarations: [CustomerComponent, UserComponent, InvoiceComponent],\r\n    imports: [\r\n        CommonModule,\r\n        ExexCommonModule,\r\n        DashboardsRoutingModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        ToastModule,\r\n        ToolbarModule,\r\n        DialogModule,\r\n        ConfirmDialogModule,\r\n        InputTextModule,\r\n        FileUploadModule,\r\n        InputTextareaModule,\r\n        KeyFilterModule,\r\n        AvatarModule,\r\n        SkeletonModule,\r\n        ButtonModule,\r\n    ],\r\n})\r\nexport class DashboardModule {}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,uBAAuB;AAsB9C,IAAMC,eAAe,GAArB,MAAMA,eAAe,GAAG;AAAlBA,eAAe,GAAAC,UAAA,EArB3BpB,QAAQ,CAAC;EACNqB,YAAY,EAAE,CAACN,iBAAiB,EAAEG,aAAa,EAAED,gBAAgB,CAAC;EAClEK,OAAO,EAAE,CACLvB,YAAY,EACZe,gBAAgB,EAChBE,uBAAuB,EACvBf,WAAW,EACXC,mBAAmB,EACnBU,WAAW,EACXC,aAAa,EACbP,YAAY,EACZD,mBAAmB,EACnBG,eAAe,EACfD,gBAAgB,EAChBE,mBAAmB,EACnBC,eAAe,EACfP,YAAY,EACZQ,cAAc,EACdP,YAAY;CAEnB,CAAC,C,EACWe,eAAe,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}