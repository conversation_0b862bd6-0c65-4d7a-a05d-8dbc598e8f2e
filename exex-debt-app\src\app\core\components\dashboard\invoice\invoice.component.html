<p-toolbar styleClass="mb-3">
    <ng-template pTemplate="left">
        <span class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <input pInputText type="text" placeholder="Search..." />
        </span>
    </ng-template>

    <ng-template pTemplate="right">
        <p-button severity="success" label="New" icon="pi pi-plus" class="mr-2" (onClick)="openNew()" />
        <p-button
            severity="danger"
            label="Delete"
            icon="pi pi-trash"
            class="mr-2"
            (onClick)="deleteSelectedProducts()"
            [disabled]="!selectedInvoices || !selectedInvoices.length" />

        <p-fileUpload
            mode="basic"
            accept=".csv,.xls,.xlsx"
            maxFileSize="5000000"
            label="Import"
            chooseLabel="Import"
            class="mr-2 inline-block" />
        <p-button severity="help" label="Export" icon="pi pi-upload" />
    </ng-template>
</p-toolbar>

<exex-table
    [propExexTable]="dataTable"
    (selectedEvent)="selectedRow($event)"
    (editEvent)="editProduct($event)"
    (deleteEvent)="deleteProduct($event)"></exex-table>

<p-dialog
    [(visible)]="invoiceDialog"
    [style]="{ width: '450px' }"
    header="Invoice Details"
    [modal]="true"
    styleClass="p-fluid">
    <ng-template pTemplate="content">
        <form [formGroup]="invoiceForm">
            <div class="field">
                <label for="status">Status</label>
                <input type="text" pInputText id="status" formControlName="status" autofocus />
                <small class="p-error" *ngIf="invoiceForm.get('status')?.invalid && invoiceForm.get('status')?.touched">
                    Status is required.
                </small>
            </div>

            <div class="field">
                <label for="totalAmount">Total Amount</label>
                <input pInputText pKeyFilter="num" id="totalAmount" formControlName="totalAmount" />
                <small
                    class="p-error"
                    *ngIf="invoiceForm.get('totalAmount')?.invalid && invoiceForm.get('totalAmount')?.touched">
                    Total Amount must be a positive number.
                </small>
            </div>

            <div class="field">
                <label for="paidAmount">Paid Amount</label>
                <input pInputText pKeyFilter="num" id="paidAmount" formControlName="paidAmount" />
                <small
                    class="p-error"
                    *ngIf="invoiceForm.get('paidAmount')?.invalid && invoiceForm.get('paidAmount')?.touched">
                    Paid Amount must be a positive number.
                </small>
            </div>
            <div class="field">
                <label for="remainingAmount">Remaining Amount</label>
                <input pInputText pKeyFilter="num" id="remainingAmount" formControlName="remainingAmount" />
                <small
                    class="p-error"
                    *ngIf="invoiceForm.get('remainingAmount')?.invalid && invoiceForm.get('remainingAmount')?.touched">
                    Remaining Amount must be a positive number.
                </small>
            </div>
        </form>
    </ng-template>

    <ng-template pTemplate="footer">
        <p-button label="Cancel" icon="pi pi-times" [text]="true" (onClick)="hideDialog()" />
        <p-button
            label="Save"
            icon="pi pi-check"
            [text]="true"
            (onClick)="saveCustomer()"
            [disabled]="invoiceForm.invalid" />
    </ng-template>
</p-dialog>
