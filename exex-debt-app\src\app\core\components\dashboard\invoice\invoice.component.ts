import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IPropExexTable } from '../../common/exex-table/exex-table.model';
import { INVOICE_COLS } from './invoice-cols';
import { CustomerService } from '@app/core/services/customer.service';
import { ExexCommonService } from '@app/core/services/exex-common.service';

@Component({
    selector: 'app-invoice',
    templateUrl: './invoice.component.html',
    styleUrl: './invoice.component.scss',
})
export class InvoiceComponent {
    invoiceDialog: boolean = false;

    invoices!: any[];

    invoice!: any;

    selectedInvoices!: any[] | null;

    cols: any;

    heightTableScroll: number = 0;

    invoiceForm!: FormGroup;

    dataTable!: IPropExexTable;

    constructor(
        private customerService: CustomerService,
        private fb: FormBuilder,
        private exexCommonService: ExexCommonService,
    ) {}

    ngOnInit() {
        this.dataTable = {
            ...this.dataTable,
            columns: INVOICE_COLS,
        };
        this.customerService.getInvoices().then((data) => {
            this.dataTable = {
                ...this.dataTable,
                value: data,
            };
        });

        this.invoiceForm = this.fb.group({
            status: ['', Validators.required],
            totalAmount: [null, [Validators.required, Validators.min(0)]],
            paidAmount: [null, [Validators.required, Validators.min(0)]],
            remainingAmount: [null, [Validators.required, Validators.min(0)]],
        });
    }

    openNew() {
        this.invoiceForm.reset();
        this.invoice = {};
        this.invoiceDialog = true;
    }

    deleteSelectedProducts() {
        this.exexCommonService.showDialogConfirm(() => {
            this.dataTable.value = this.dataTable.value.filter((val) => !this.selectedInvoices?.includes(val));
            this.selectedInvoices = null;
            this.exexCommonService.showToastSuccess('Customers Deleted');
        });
    }

    selectedRow(rows) {
        this.selectedInvoices = [...rows];
    }

    editProduct(invoice: any) {
        this.invoice = { ...invoice };
        this.invoiceForm.patchValue({
            status: this.invoice.status,
            totalAmount: this.invoice.totalAmount,
            paidAmount: this.invoice.paidAmount,
            remainingAmount: this.invoice.remainingAmount,
        });
        this.invoiceDialog = true;
    }

    deleteProduct(invoice: any) {
        this.exexCommonService.showDialogConfirm(() => {
            this.dataTable.value = this.dataTable.value.filter((val) => val.customerId !== invoice.customerId);
            this.invoice = {};
            this.exexCommonService.showToastSuccess('Customer Deleted');
        });
    }

    hideDialog() {
        this.invoiceDialog = false;
    }

    saveCustomer() {
        if (this.invoiceForm.valid) {
            if (this.invoice.customerId) {
                this.dataTable.value[this.findIndexById(this.invoice.customerId)] = this.invoice;
                this.exexCommonService.showToastSuccess('Successful');
            } else {
                this.dataTable.value.push(this.invoice);
                this.exexCommonService.showToastSuccess('Successful');
            }

            this.dataTable.value = [...this.dataTable.value];
            this.invoiceDialog = false;
            this.invoice = {};
        } else {
            this.invoiceForm.markAllAsTouched(); // Show validation errors
        }
    }

    findIndexById(customerId: string): number {
        let index = -1;
        for (let i = 0; i < this.dataTable.value.length; i++) {
            if (this.dataTable.value[i].customerId === customerId) {
                index = i;
                break;
            }
        }
        return index;
    }
}
