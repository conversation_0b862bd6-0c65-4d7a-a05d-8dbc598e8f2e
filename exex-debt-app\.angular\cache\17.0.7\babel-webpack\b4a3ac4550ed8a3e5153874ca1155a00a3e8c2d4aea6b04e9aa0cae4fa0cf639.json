{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CustomerService {\n  constructor() {}\n  getCustomerData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        customerId: 'COD' + i,\n        customerName: 'Customer' + i,\n        phoneNumber: '039876549' + i,\n        email: 'nguyenvan' + i + '@gmail.com',\n        address: 'HCM',\n        status: 'Active',\n        creditLimit: 1000000,\n        currentBalance: 4900 + i,\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getInvoiceData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        invoiceId: 'Inv' + i,\n        customerId: 'Customer' + i,\n        invoiceDate: '02/02/2025',\n        dueDate: '02/02/2025',\n        totalAmount: 4900 + i,\n        paidAmount: 2900 + i,\n        remainingAmount: 1000000,\n        status: 'Active',\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getCustomers() {\n    return Promise.resolve(this.getCustomerData());\n  }\n  getInvoices() {\n    return Promise.resolve(this.getInvoiceData());\n  }\n  static #_ = this.ɵfac = function CustomerService_Factory(t) {\n    return new (t || CustomerService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomerService,\n    factory: CustomerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["CustomerService", "constructor", "getCustomerData", "mockData", "Array", "from", "length", "map", "_", "i", "id", "customerId", "customerName", "phoneNumber", "email", "address", "status", "creditLimit", "currentBalance", "currencyId", "getInvoiceData", "invoiceId", "invoiceDate", "dueDate", "totalAmount", "paidAmount", "remainingAmount", "getCustomers", "Promise", "resolve", "getInvoices", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\customer.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n    constructor() {}\r\n\r\n    getCustomerData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                customerId: 'COD' + i,\r\n                customerName: 'Customer' + i,\r\n                phoneNumber: '039876549' + i,\r\n                email: 'nguyenvan' + i + '@gmail.com',\r\n                address: 'HCM',\r\n                status: 'Active',\r\n                creditLimit: 1000000,\r\n                currentBalance: 4900 + i,\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getInvoiceData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                invoiceId: 'Inv' + i,\r\n                customerId: 'Customer' + i,\r\n                invoiceDate: '02/02/2025',\r\n                dueDate: '02/02/2025',\r\n                totalAmount: 4900 + i,\r\n                paidAmount: 2900 + i,\r\n                remainingAmount: 1000000,\r\n                status: 'Active',\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getCustomers() {\r\n        return Promise.resolve(this.getCustomerData());\r\n    }\r\n\r\n    getInvoices() {\r\n        return Promise.resolve(this.getInvoiceData());\r\n    }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EACxBC,YAAA,GAAe;EAEfC,eAAeA,CAAA;IACX,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLE,UAAU,EAAE,KAAK,GAAGF,CAAC;QACrBG,YAAY,EAAE,UAAU,GAAGH,CAAC;QAC5BI,WAAW,EAAE,WAAW,GAAGJ,CAAC;QAC5BK,KAAK,EAAE,WAAW,GAAGL,CAAC,GAAG,YAAY;QACrCM,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,OAAO;QACpBC,cAAc,EAAE,IAAI,GAAGT,CAAC;QACxBU,UAAU,EAAE,KAAK,GAAGV;OACvB;IACL,CAAC,CAAC;IAEF,OAAON,QAAQ;EACnB;EAEAiB,cAAcA,CAAA;IACV,MAAMjB,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLY,SAAS,EAAE,KAAK,GAAGZ,CAAC;QACpBE,UAAU,EAAE,UAAU,GAAGF,CAAC;QAC1Ba,WAAW,EAAE,YAAY;QACzBC,OAAO,EAAE,YAAY;QACrBC,WAAW,EAAE,IAAI,GAAGf,CAAC;QACrBgB,UAAU,EAAE,IAAI,GAAGhB,CAAC;QACpBiB,eAAe,EAAE,OAAO;QACxBV,MAAM,EAAE,QAAQ;QAChBG,UAAU,EAAE,KAAK,GAAGV;OACvB;IACL,CAAC,CAAC;IAEF,OAAON,QAAQ;EACnB;EAEAwB,YAAYA,CAAA;IACR,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC3B,eAAe,EAAE,CAAC;EAClD;EAEA4B,WAAWA,CAAA;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACT,cAAc,EAAE,CAAC;EACjD;EAAC,QAAAZ,CAAA,G;qBA/CQR,eAAe;EAAA;EAAA,QAAA+B,EAAA,G;WAAf/B,eAAe;IAAAgC,OAAA,EAAfhC,eAAe,CAAAiC,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}