{"ast": null, "code": "import { Path } from '../../enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = a0 => [a0];\nexport class NotfoundComponent {\n  constructor() {\n    this.dashboardUser = Path.DASHBOARD_USER;\n  }\n  static #_ = this.ɵfac = function NotfoundComponent_Factory(t) {\n    return new (t || NotfoundComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotfoundComponent,\n    selectors: [[\"app-notfound\"]],\n    decls: 11,\n    vars: 3,\n    consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", \"flex\", \"flex-column\", \"align-items-center\", 2, \"border-radius\", \"53px\"], [1, \"font-bold\", \"mb-0\"], [1, \"text-900\", \"font-bold\", \"text-3xl\", \"lg:text-5xl\", \"mb-2\"], [1, \"text-center\", 3, \"routerLink\"]],\n    template: function NotfoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n        i0.ɵɵtext(5, \"404\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"h1\", 5);\n        i0.ɵɵtext(7, \" Page Not Found \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"h5\")(9, \"a\", 6);\n        i0.ɵɵtext(10, \" Go to dashboard \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx.dashboardUser));\n      }\n    },\n    dependencies: [i1.RouterLink],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Path", "NotfoundComponent", "constructor", "dashboardUser", "DASHBOARD_USER", "_", "_2", "selectors", "decls", "vars", "consts", "template", "NotfoundComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\notfound\\notfound.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Path } from '../../enums/path.enum';\r\n\r\n@Component({\r\n    selector: 'app-notfound',\r\n    template: `<div\r\n        class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\"\r\n    >\r\n        <div class=\"flex flex-column align-items-center justify-content-center\">\r\n            <div style=\"border-radius: 56px; padding: 0.3rem\">\r\n                <div\r\n                    class=\"w-full surface-card py-8 px-5 sm:px-8 flex flex-column align-items-center\"\r\n                    style=\"border-radius: 53px\"\r\n                >\r\n                    <h1 class=\"font-bold mb-0\">404</h1>\r\n                    <h1 class=\"text-900 font-bold text-3xl lg:text-5xl mb-2\">\r\n                        Page Not Found\r\n                    </h1>\r\n                    <h5>\r\n                        <a\r\n                            [routerLink]=\"[dashboardUser]\"\r\n                            class=\"text-center\"\r\n                        >\r\n                            Go to dashboard\r\n                        </a>\r\n                    </h5>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div> `,\r\n})\r\nexport class NotfoundComponent {\r\n    dashboardUser = Path.DASHBOARD_USER;\r\n}\r\n"], "mappings": "AACA,SAASA,IAAI,QAAQ,uBAAuB;;;;AA8B5C,OAAM,MAAOC,iBAAiB;EA5B9BC,YAAA;IA6BI,KAAAC,aAAa,GAAGH,IAAI,CAACI,cAAc;;EACtC,QAAAC,CAAA,G;qBAFYJ,iBAAiB;EAAA;EAAA,QAAAK,EAAA,G;UAAjBL,iBAAiB;IAAAM,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1BfE,EAAA,CAAAC,cAAA,aAEV;QAO0CD,EAAA,CAAAE,MAAA,UAAG;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACnCH,EAAA,CAAAC,cAAA,YAAyD;QACrDD,EAAA,CAAAE,MAAA,uBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,SAAI;QAKID,EAAA,CAAAE,MAAA,yBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;QAJAH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAR,GAAA,CAAAX,aAAA,EAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}