{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet LoginComponent = class LoginComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.valCheck = ['remember'];\n    this.authService.redirectToDashboard();\n  }\n  login() {\n    this.authService.login();\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styles: [`\n            :host ::ng-deep .pi-eye,\n            :host ::ng-deep .pi-eye-slash {\n                transform: scale(1.6);\n                margin-right: 1rem;\n                color: var(--primary-color) !important;\n            }\n        `]\n})], LoginComponent);\nexport { LoginComponent };", "map": {"version": 3, "names": ["Component", "LoginComponent", "constructor", "layoutService", "authService", "val<PERSON><PERSON><PERSON>", "redirectToDashboard", "login", "__decorate", "selector", "templateUrl", "styles"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthService } from 'src/app/core/service/auth.service';\r\nimport { LayoutService } from 'src/app/layout/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styles: [\r\n        `\r\n            :host ::ng-deep .pi-eye,\r\n            :host ::ng-deep .pi-eye-slash {\r\n                transform: scale(1.6);\r\n                margin-right: 1rem;\r\n                color: var(--primary-color) !important;\r\n            }\r\n        `,\r\n    ],\r\n})\r\nexport class LoginComponent {\r\n    valCheck: string[] = ['remember'];\r\n\r\n    password!: string;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {\r\n        this.authService.redirectToDashboard();\r\n    }\r\n\r\n    login() {\r\n        this.authService.login();\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAkBlC,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAKvBC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IANvB,KAAAC,QAAQ,GAAa,CAAC,UAAU,CAAC;IAQ7B,IAAI,CAACD,WAAW,CAACE,mBAAmB,EAAE;EAC1C;EAEAC,KAAKA,CAAA;IACD,IAAI,CAACH,WAAW,CAACG,KAAK,EAAE;EAC5B;CACH;AAfYN,cAAc,GAAAO,UAAA,EAd1BR,SAAS,CAAC;EACPS,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,MAAM,EAAE,CACJ;;;;;;;SAOC;CAER,CAAC,C,EACWV,cAAc,CAe1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}