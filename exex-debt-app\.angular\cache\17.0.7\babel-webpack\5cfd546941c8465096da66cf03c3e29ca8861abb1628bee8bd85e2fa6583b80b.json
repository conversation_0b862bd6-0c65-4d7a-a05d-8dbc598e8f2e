{"ast": null, "code": "import { provideTransloco, TranslocoModule } from '@jsverse/transloco';\nimport { TranslocoHttpLoader } from './transloco-loader';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class TranslocoRootModule {\n  static #_ = this.ɵfac = function TranslocoRootModule_Factory(t) {\n    return new (t || TranslocoRootModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TranslocoRootModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [provideTransloco({\n      config: {\n        availableLangs: ['en', 'vi'],\n        defaultLang: 'en',\n        // Remove this option if your application doesn't support changing language in runtime.\n        reRenderOnLangChange: true,\n        prodMode: environment.production\n      },\n      loader: TranslocoHttpLoader\n    })],\n    imports: [TranslocoModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TranslocoRootModule, {\n    exports: [TranslocoModule]\n  });\n})();", "map": {"version": 3, "names": ["provideTransloco", "TranslocoModule", "TranslocoHttpLoader", "environment", "TranslocoRootModule", "_", "_2", "_3", "config", "availableLangs", "defaultLang", "reRenderOnLangChange", "prodMode", "production", "loader", "imports", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\transloco-root.module.ts"], "sourcesContent": ["import {\n  provideTransloco,\n  TranslocoModule\n} from '@jsverse/transloco';\nimport { NgModule } from '@angular/core';\nimport { TranslocoHttpLoader } from './transloco-loader';\nimport { environment } from 'src/environments/environment';\n\n@NgModule({\n  exports: [ TranslocoModule ],\n  providers: [\n      provideTransloco({\n        config: {\n          availableLangs: ['en', 'vi'],\n          defaultLang: 'en',\n          // Remove this option if your application doesn't support changing language in runtime.\n          reRenderOnLangChange: true,\n          prodMode: environment.production,\n        },\n        loader: TranslocoHttpLoader\n      }),\n  ],\n})\nexport class TranslocoRootModule {}\n"], "mappings": "AAAA,SACEA,gBAAgB,EAChBC,eAAe,QACV,oBAAoB;AAE3B,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,WAAW,QAAQ,8BAA8B;;AAiB1D,OAAM,MAAOC,mBAAmB;EAAA,QAAAC,CAAA,G;qBAAnBD,mBAAmB;EAAA;EAAA,QAAAE,EAAA,G;UAAnBF;EAAmB;EAAA,QAAAG,EAAA,G;eAbnB,CACPP,gBAAgB,CAAC;MACfQ,MAAM,EAAE;QACNC,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAC5BC,WAAW,EAAE,IAAI;QACjB;QACAC,oBAAoB,EAAE,IAAI;QAC1BC,QAAQ,EAAET,WAAW,CAACU;OACvB;MACDC,MAAM,EAAEZ;KACT,CAAC,CACL;IAAAa,OAAA,GAZUd,eAAe;EAAA;;;2EAcfG,mBAAmB;IAAAY,OAAA,GAdnBf,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}