{"ast": null, "code": "import { HttpErrorResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class OfflineModeInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    // Check if the device is offline\n    if (!navigator.onLine) {\n      // Handle offline mode (e.g., store requests for later)\n      console.error('Device is offline. Request not sent:', request.url);\n      return throwError(new HttpErrorResponse({\n        status: 0,\n        statusText: 'Offline'\n      }));\n    }\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function OfflineModeInterceptor_Factory(t) {\n    return new (t || OfflineModeInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OfflineModeInterceptor,\n    factory: OfflineModeInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpErrorResponse", "throwError", "OfflineModeInterceptor", "constructor", "intercept", "request", "next", "navigator", "onLine", "console", "error", "url", "status", "statusText", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\offline-mode.interceptor.ts"], "sourcesContent": ["import { HttpErrorResponse, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { throwError } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class OfflineModeInterceptor implements HttpInterceptor {\r\n    constructor() {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        // Check if the device is offline\r\n        if (!navigator.onLine) {\r\n            // Handle offline mode (e.g., store requests for later)\r\n            console.error('Device is offline. Request not sent:', request.url);\r\n            return throwError(new HttpErrorResponse({ status: 0, statusText: 'Offline' }));\r\n        }\r\n\r\n        return next.handle(request);\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAmD,sBAAsB;AAEnG,SAASC,UAAU,QAAQ,MAAM;;AAGjC,OAAM,MAAOC,sBAAsB;EAC/BC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE;MACnB;MACAC,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEL,OAAO,CAACM,GAAG,CAAC;MAClE,OAAOV,UAAU,CAAC,IAAID,iBAAiB,CAAC;QAAEY,MAAM,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAS,CAAE,CAAC,CAAC;;IAGlF,OAAOP,IAAI,CAACQ,MAAM,CAACT,OAAO,CAAC;EAC/B;EAAC,QAAAU,CAAA,G;qBAZQb,sBAAsB;EAAA;EAAA,QAAAc,EAAA,G;WAAtBd,sBAAsB;IAAAe,OAAA,EAAtBf,sBAAsB,CAAAgB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}