{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-spinner\";\nimport * as i3 from \"primeng/toast\";\nimport * as i4 from \"primeng/confirmdialog\";\nconst _c0 = () => ({\n  width: \"450px\"\n});\nexport class AppComponent {\n  constructor() {\n    this.title = 'exex-debt Angular';\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 4,\n    vars: 4,\n    consts: [[\"bdColor\", \"rgba(0, 0, 0, 0.8)\", \"size\", \"medium\", \"color\", \"#fff\", \"type\", \"ball-scale-multiple\", 3, \"fullScreen\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"ngx-spinner\", 0)(1, \"p-toast\")(2, \"p-confirmDialog\")(3, \"router-outlet\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"fullScreen\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(3, _c0));\n      }\n    },\n    dependencies: [i1.RouterOutlet, i2.NgxSpinnerComponent, i3.Toast, i4.ConfirmDialog],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵadvance", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    template: ` <ngx-spinner\r\n            bdColor=\"rgba(0, 0, 0, 0.8)\"\r\n            size=\"medium\"\r\n            color=\"#fff\"\r\n            [fullScreen]=\"true\"\r\n            type=\"ball-scale-multiple\">\r\n            <!-- <p style=\"color: white\">Loading...</p> -->\r\n        </ngx-spinner>\r\n        <p-toast></p-toast>\r\n        <p-confirmDialog [style]=\"{ width: '450px' }\" />\r\n        <router-outlet></router-outlet>`,\r\n})\r\nexport class AppComponent {\r\n    title = 'exex-debt Angular';\r\n}\r\n"], "mappings": ";;;;;;;;AAgBA,OAAM,MAAOA,YAAY;EAdzBC,YAAA;IAeI,KAAAC,KAAK,GAAG,mBAAmB;;EAC9B,QAAAC,CAAA,G;qBAFYH,YAAY;EAAA;EAAA,QAAAI,EAAA,G;UAAZJ,YAAY;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAZTE,EAAA,CAAAC,SAAA,qBAOM;;;QAHVD,EAAA,CAAAE,UAAA,oBAAmB;QAKNF,EAAA,CAAAG,SAAA,GAA4B;QAA5BH,EAAA,CAAAI,UAAA,CAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}