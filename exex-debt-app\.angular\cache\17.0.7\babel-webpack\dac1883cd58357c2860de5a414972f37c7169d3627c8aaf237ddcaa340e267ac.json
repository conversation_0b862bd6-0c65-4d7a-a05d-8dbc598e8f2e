{"ast": null, "code": "import { LocationStrategy, PathLocationStrategy } from '@angular/common';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\nimport { AppLayoutModule } from './layout/app.layout.module';\nimport { TranslocoRootModule } from './transloco-root.module';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [{\n      provide: LocationStrategy,\n      useClass: PathLocationStrategy\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: ErrorInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: LoggingInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: HeadersInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: LoadingInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: TimeoutInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: BaseUrlInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: RetryInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: OfflineModeInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtRefreshInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: RequestTimingInterceptor,\n      multi: true\n    }],\n    imports: [AppRoutingModule, AppLayoutModule, NgxSpinnerModule, BrowserAnimationsModule, BrowserModule, HttpClientModule, TranslocoRootModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, NotfoundComponent],\n    imports: [AppRoutingModule, AppLayoutModule, NgxSpinnerModule, BrowserAnimationsModule, BrowserModule, HttpClientModule, TranslocoRootModule]\n  });\n})();", "map": {"version": 3, "names": ["LocationStrategy", "PathLocationStrategy", "HTTP_INTERCEPTORS", "HttpClientModule", "BrowserModule", "BrowserAnimationsModule", "NgxSpinnerModule", "AppRoutingModule", "AppComponent", "NotfoundComponent", "AuthInterceptor", "BaseUrlInterceptor", "ErrorInterceptor", "HeadersInterceptor", "JwtRefreshInterceptor", "LoadingInterceptor", "LoggingInterceptor", "OfflineModeInterceptor", "RequestTimingInterceptor", "RetryInterceptor", "TimeoutInterceptor", "AppLayoutModule", "TranslocoRootModule", "AppModule", "_", "_2", "bootstrap", "_3", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\app.module.ts"], "sourcesContent": ["import { LocationStrategy, PathLocationStrategy } from '@angular/common';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\nimport { AppLayoutModule } from './layout/app.layout.module';\nimport { TranslocoRootModule } from './transloco-root.module';\n\n@NgModule({\n    declarations: [AppComponent, NotfoundComponent],\n    imports: [\n        AppRoutingModule,\n        AppLayoutModule,\n        NgxSpinnerModule,\n        BrowserAnimationsModule,\n        BrowserModule,\n        HttpClientModule,\n        TranslocoRootModule,\n    ],\n    providers: [\n        { provide: LocationStrategy, useClass: PathLocationStrategy },\n        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\n        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: LoggingInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: HeadersInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: LoadingInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: TimeoutInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: BaseUrlInterceptor,\n            multi: true,\n        },\n        { provide: HTTP_INTERCEPTORS, useClass: RetryInterceptor, multi: true },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: OfflineModeInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: JwtRefreshInterceptor,\n            multi: true,\n        },\n        {\n            provide: HTTP_INTERCEPTORS,\n            useClass: RequestTimingInterceptor,\n            multi: true,\n        },\n    ],\n    bootstrap: [AppComponent],\n    schemas: [CUSTOM_ELEMENTS_SCHEMA],\n})\nexport class AppModule {}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,oBAAoB,QAAQ,iBAAiB;AACxE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAE1E,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,mBAAmB,QAAQ,yBAAyB;;AA8D7D,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qBAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAHNlB,YAAY;EAAA;EAAA,QAAAmB,EAAA,G;eA9Cb,CACP;MAAEC,OAAO,EAAE5B,gBAAgB;MAAE6B,QAAQ,EAAE5B;IAAoB,CAAE,EAC7D;MAAE2B,OAAO,EAAE1B,iBAAiB;MAAE2B,QAAQ,EAAEnB,eAAe;MAAEoB,KAAK,EAAE;IAAI,CAAE,EACtE;MAAEF,OAAO,EAAE1B,iBAAiB;MAAE2B,QAAQ,EAAEjB,gBAAgB;MAAEkB,KAAK,EAAE;IAAI,CAAE,EACvE;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEb,kBAAkB;MAC5Bc,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEhB,kBAAkB;MAC5BiB,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEd,kBAAkB;MAC5Be,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAET,kBAAkB;MAC5BU,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAElB,kBAAkB;MAC5BmB,KAAK,EAAE;KACV,EACD;MAAEF,OAAO,EAAE1B,iBAAiB;MAAE2B,QAAQ,EAAEV,gBAAgB;MAAEW,KAAK,EAAE;IAAI,CAAE,EACvE;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEZ,sBAAsB;MAChCa,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEf,qBAAqB;MAC/BgB,KAAK,EAAE;KACV,EACD;MACIF,OAAO,EAAE1B,iBAAiB;MAC1B2B,QAAQ,EAAEX,wBAAwB;MAClCY,KAAK,EAAE;KACV,CACJ;IAAAC,OAAA,GArDGxB,gBAAgB,EAChBc,eAAe,EACff,gBAAgB,EAChBD,uBAAuB,EACvBD,aAAa,EACbD,gBAAgB,EAChBmB,mBAAmB;EAAA;;;2EAmDdC,SAAS;IAAAS,YAAA,GA3DHxB,YAAY,EAAEC,iBAAiB;IAAAsB,OAAA,GAE1CxB,gBAAgB,EAChBc,eAAe,EACff,gBAAgB,EAChBD,uBAAuB,EACvBD,aAAa,EACbD,gBAAgB,EAChBmB,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}