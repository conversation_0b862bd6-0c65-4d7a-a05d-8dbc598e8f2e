{"ast": null, "code": "import { Subject } from './Subject';\nexport class BehaviorSubject extends Subject {\n  constructor(_value) {\n    super();\n    this._value = _value;\n  }\n  get value() {\n    return this.getValue();\n  }\n  _subscribe(subscriber) {\n    const subscription = super._subscribe(subscriber);\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  }\n  getValue() {\n    const {\n      hasError,\n      thrownError,\n      _value\n    } = this;\n    if (hasError) {\n      throw thrownError;\n    }\n    this._throwIfClosed();\n    return _value;\n  }\n  next(value) {\n    super.next(this._value = value);\n  }\n}", "map": {"version": 3, "names": ["Subject", "BehaviorSubject", "constructor", "_value", "value", "getValue", "_subscribe", "subscriber", "subscription", "closed", "next", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "_throwIfClosed"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/rxjs/dist/esm/internal/BehaviorSubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nexport class BehaviorSubject extends Subject {\n    constructor(_value) {\n        super();\n        this._value = _value;\n    }\n    get value() {\n        return this.getValue();\n    }\n    _subscribe(subscriber) {\n        const subscription = super._subscribe(subscriber);\n        !subscription.closed && subscriber.next(this._value);\n        return subscription;\n    }\n    getValue() {\n        const { hasError, thrownError, _value } = this;\n        if (hasError) {\n            throw thrownError;\n        }\n        this._throwIfClosed();\n        return _value;\n    }\n    next(value) {\n        super.next((this._value = value));\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,eAAe,SAASD,OAAO,CAAC;EACzCE,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACAC,UAAUA,CAACC,UAAU,EAAE;IACnB,MAAMC,YAAY,GAAG,KAAK,CAACF,UAAU,CAACC,UAAU,CAAC;IACjD,CAACC,YAAY,CAACC,MAAM,IAAIF,UAAU,CAACG,IAAI,CAAC,IAAI,CAACP,MAAM,CAAC;IACpD,OAAOK,YAAY;EACvB;EACAH,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEM,QAAQ;MAAEC,WAAW;MAAET;IAAO,CAAC,GAAG,IAAI;IAC9C,IAAIQ,QAAQ,EAAE;MACV,MAAMC,WAAW;IACrB;IACA,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,OAAOV,MAAM;EACjB;EACAO,IAAIA,CAACN,KAAK,EAAE;IACR,KAAK,CAACM,IAAI,CAAE,IAAI,CAACP,MAAM,GAAGC,KAAM,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}