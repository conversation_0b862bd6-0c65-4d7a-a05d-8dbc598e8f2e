{"ast": null, "code": "import { retry } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class RetryInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    // Define the maximum number of retries\n    const maxRetries = 2;\n    return next.handle(request).pipe(retry(maxRetries));\n  }\n  static #_ = this.ɵfac = function RetryInterceptor_Factory(t) {\n    return new (t || RetryInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RetryInterceptor,\n    factory: RetryInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["retry", "RetryInterceptor", "constructor", "intercept", "request", "next", "maxRetries", "handle", "pipe", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\interceptors\\retry.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport {\n  HttpInterceptor,\n  HttpRequest,\n  HttpHandler,\n} from '@angular/common/http';\nimport { retry } from 'rxjs/operators';\n\n@Injectable()\nexport class RetryInterceptor implements HttpInterceptor {\n  constructor() {}\n\n  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {\n    // Define the maximum number of retries\n    const maxRetries = 2;\n    return next.handle(request).pipe(retry(maxRetries));\n  }\n}"], "mappings": "AAMA,SAASA,KAAK,QAAQ,gBAAgB;;AAGtC,OAAM,MAAOC,gBAAgB;EAC3BC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,MAAMC,UAAU,GAAG,CAAC;IACpB,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAACR,KAAK,CAACM,UAAU,CAAC,CAAC;EACrD;EAAC,QAAAG,CAAA,G;qBAPUR,gBAAgB;EAAA;EAAA,QAAAS,EAAA,G;WAAhBT,gBAAgB;IAAAU,OAAA,EAAhBV,gBAAgB,CAAAW;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}