{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nexport class AppFooterComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n  }\n  static #_ = this.ɵfac = function AppFooterComponent_Factory(t) {\n    return new (t || AppFooterComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppFooterComponent,\n    selectors: [[\"app-footer\"]],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"layout-footer\", \"pt-5\"], [1, \"font-medium\"]],\n    template: function AppFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"span\", 1);\n        i0.ɵɵtext(2, \"Dashboard Footer\");\n        i0.ɵɵelementEnd()();\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppFooterComponent", "constructor", "layoutService", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "AppFooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\layout\\app.footer.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { LayoutService } from './app.layout.service';\n\n@Component({\n    selector: 'app-footer',\n    template: `<div class=\"layout-footer pt-5\">\n        <span class=\"font-medium\">Dashboard Footer</span>\n    </div> `,\n})\nexport class AppFooterComponent {\n    constructor(public layoutService: LayoutService) {}\n}\n"], "mappings": ";;AASA,OAAM,MAAOA,kBAAkB;EAC3BC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;EAAkB;EAAC,QAAAC,CAAA,G;qBAD1CH,kBAAkB,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBR,kBAAkB;IAAAS,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAJhBX,EAAA,CAAAa,cAAA,aAAgC;QACbb,EAAA,CAAAc,MAAA,uBAAgB;QAAAd,EAAA,CAAAe,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}