/* You can add global styles to this file, and also import other style files */

$gutter: 1rem; //for primeflex grid system
@import 'assets/layout/styles/layout/layout.scss';

/* PrimeNG */
@import '../node_modules/primeng/resources/primeng.min.css';
@import '../node_modules/primeflex/primeflex.scss';
@import '../node_modules/primeicons/primeicons.css';

@media (max-width: 739px) {
}
@media (min-width: 740px) and (max-width: 1023px) {
}
@media (min-width: 1024px) {
}

/*  CUSTOM GROUP BUTTON EDIT IN TABLE */
.custom-group-button-edit {
    .p-button.p-button-sm {
        padding: 3px 7px;
    }
}
/*  CUSTOM AVATAR */
.custom-avatar {
    .p-avatar {
        background-color: #ece9fc;
        color: #2a1261;
    }
}
