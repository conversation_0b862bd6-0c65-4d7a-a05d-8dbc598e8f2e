/* You can add global styles to this file, and also import other style files */

$gutter: 1rem; //for primeflex grid system
@import 'assets/layout/styles/layout/layout.scss';

/* PrimeNG */
@import '../node_modules/primeng/resources/primeng.min.css';
@import '../node_modules/primeflex/primeflex.scss';
@import '../node_modules/primeicons/primeicons.css';

/* CSS VARIABLES FOR CONSISTENT THEMING */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #4facfe;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #6b7280;

    --border-color: #e2e8f0;
    --border-hover: #d1d5db;

    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 15px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 10px 40px rgba(0, 0, 0, 0.1);

    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* GLOBAL IMPROVEMENTS */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: var(--bg-gradient) !important;
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* IMPROVED SCROLLBAR */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--border-radius-md);

    &:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-color) 100%);
    }
}

/* ENHANCED CARD STYLING */
.card {
    background: var(--bg-primary) !important;
    border-radius: var(--border-radius-xl) !important;
    box-shadow: var(--shadow-xl) !important;
    border: none !important;
    overflow: hidden;
    transition: var(--transition-normal);

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
        color: white !important;
        padding: 1.5rem !important;
        border: none !important;

        h1, h2, h3, h4, h5, h6 {
            color: white !important;
            margin: 0;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    }

    .card-body {
        padding: 2rem !important;
    }

    .card-footer {
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-color) !important;
        padding: 1.5rem !important;
    }
}

/* RESPONSIVE DESIGN IMPROVEMENTS - Optimized */
@media (max-width: 739px) {
    .p-toolbar {
        .p-toolbar-group-left,
        .p-toolbar-group-right {
            flex-direction: column !important;
            gap: 0.75rem !important;
            width: 100% !important;
        }

        .p-button {
            width: 100% !important;
            justify-content: center !important;
            padding: 0.5rem 1rem !important;
            font-size: 0.8rem !important;
        }
    }

    .p-datatable {
        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: 0.5rem 0.25rem !important;
            font-size: 0.75rem !important;
        }
    }

    .customer-container {
        margin: 0.25rem !important;

        .p-toolbar,
        exex-table {
            margin: 0 0.75rem !important;
        }
    }

    :host {
        padding: 0.25rem !important;
    }

    .status-badge {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.65rem !important;
        gap: 0.25rem !important;

        i {
            font-size: 0.65rem !important;
        }

        .status-text {
            font-size: 0.6rem !important;
        }
    }
}

@media (min-width: 740px) and (max-width: 1023px) {
    .p-toolbar {
        .p-toolbar-group-right {
            flex-wrap: wrap !important;
            gap: 0.5rem !important;
        }
    }

    .p-datatable {
        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: 0.75rem !important;
        }
    }
}

@media (min-width: 1024px) {
    .p-datatable {
        .p-datatable-tbody > tr:hover {
            transform: translateY(-3px) !important;
        }
    }
}

/* LOADING ANIMATIONS */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* ENHANCED ANIMATIONS */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-slide-in-top {
    animation: slideInFromTop 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* STATUS BADGE STYLING - Compact */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);

    .status-text {
        font-size: 0.7rem;
    }

    i {
        font-size: 0.75rem;
    }

    &.status-badge-active {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
    }

    &.status-badge-inactive {
        background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }
    }

    &.status-badge-pending {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }
    }

    &.status-badge-suspended {
        background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
        }
    }

    &.status-badge-vip {
        background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        position: relative;
        overflow: hidden;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
        }
    }

    &.status-badge-default {
        background: linear-gradient(135deg, var(--info-color) 0%, #60a5fa 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }
    }
}

/*  CUSTOM GROUP BUTTON EDIT IN TABLE */
.custom-group-button-edit {
    .p-button.p-button-sm {
        padding: 3px 7px;
    }
}
/*  CUSTOM AVATAR */
.custom-avatar {
    .p-avatar {
        background-color: #ece9fc;
        color: #2a1261;
    }
}

/* ENHANCED TABLE STYLING - Compact */
.p-datatable {
    background: white !important;
    border-radius: var(--border-radius-md) !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-md) !important;
    border: none !important;

    .p-datatable-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        padding: 1.5rem !important;
        border: none !important;

        h2 {
            color: white;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    }

    .p-datatable-thead > tr > th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
        color: #1e293b !important;
        font-weight: 700 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.3px !important;
        padding: 1rem 0.75rem !important;
        border: none !important;
        border-bottom: 2px solid #667eea !important;
        font-size: 0.8rem;

        &:first-child {
            border-radius: 0 !important;
        }

        &:last-child {
            border-radius: 0 !important;
        }

        .p-sortable-column-icon {
            color: #667eea !important;
        }

        .p-column-header-content {
            justify-content: center;
        }
    }

    .p-datatable-tbody > tr {
        transition: all 0.3s ease !important;
        border: none !important;

        &:nth-child(even) {
            background: #f8fafc !important;
        }

        &:nth-child(odd) {
            background: white !important;
        }

        &:hover {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;

            td {
                color: #1e293b !important;
            }
        }

        td {
            padding: 0.75rem 0.5rem !important;
            border: none !important;
            border-bottom: 1px solid #e2e8f0 !important;
            font-weight: 500;
            color: #475569;
            text-align: center;
            font-size: 0.875rem;

            &:first-child {
                border-radius: 0 !important;
            }

            &:last-child {
                border-radius: 0 !important;
            }
        }

        &.p-highlight {
            background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%) !important;
            color: #1e293b !important;

            td {
                color: #1e293b !important;
            }
        }
    }

    .p-datatable-footer {
        background: #f8fafc !important;
        border: none !important;
        padding: 1rem !important;
        border-top: 1px solid #e2e8f0 !important;
    }

    .p-checkbox {
        .p-checkbox-box {
            border: 2px solid #d1d5db !important;
            border-radius: 6px !important;
            background: white !important;
            transition: all 0.3s ease;

            &:hover {
                border-color: #667eea !important;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            }

            &.p-highlight {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                border-color: #667eea !important;

                .p-checkbox-icon {
                    color: white !important;
                }
            }
        }
    }
}

/* ENHANCED BUTTON STYLING IN TABLE */
.custom-group-button-edit {
    display: flex;
    gap: 0.5rem;
    justify-content: center;

    .p-button {
        border-radius: 8px !important;
        padding: 0.5rem 0.75rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

        &:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25) !important;
        }

        &.p-button-success {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
            }
        }

        &.p-button-danger {
            background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
            }
        }

        .p-button-icon {
            font-size: 0.875rem;
        }
    }
}

/* PAGINATOR STYLING */
.p-paginator {
    background: #f8fafc !important;
    border: none !important;
    border-top: 1px solid #e2e8f0 !important;
    padding: 1rem !important;
    border-radius: 0 0 12px 12px !important;

    .p-paginator-pages {
        .p-paginator-page {
            background: white !important;
            border: 2px solid #e2e8f0 !important;
            color: #475569 !important;
            border-radius: 8px !important;
            margin: 0 0.25rem !important;
            transition: all 0.3s ease !important;

            &:hover {
                background: #667eea !important;
                color: white !important;
                border-color: #667eea !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            }

            &.p-highlight {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                border-color: #667eea !important;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            }
        }
    }

    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-first,
    .p-paginator-last {
        background: white !important;
        border: 2px solid #e2e8f0 !important;
        color: #475569 !important;
        border-radius: 8px !important;
        margin: 0 0.25rem !important;
        transition: all 0.3s ease !important;

        &:hover {
            background: #667eea !important;
            color: white !important;
            border-color: #667eea !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        }

        &:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;

            &:hover {
                background: white !important;
                color: #475569 !important;
                border-color: #e2e8f0 !important;
                transform: none !important;
                box-shadow: none !important;
            }
        }
    }

    .p-dropdown {
        border: 2px solid #e2e8f0 !important;
        border-radius: 8px !important;
        background: white !important;

        &:hover {
            border-color: #667eea !important;
        }

        &:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        }
    }
}
