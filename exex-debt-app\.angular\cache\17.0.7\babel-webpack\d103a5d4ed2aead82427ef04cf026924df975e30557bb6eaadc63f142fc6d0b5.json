{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n  return operate((source, subscriber) => {\n    let element;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      ({\n        duration,\n        element,\n        connector\n      } = elementOrOptions);\n    }\n    const groups = new Map();\n    const notify = cb => {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n    const handleError = err => notify(consumer => consumer.error(err));\n    let activeGroups = 0;\n    let teardownAttempted = false;\n    const groupBySourceSubscriber = new OperatorSubscriber(subscriber, value => {\n      try {\n        const key = keySelector(value);\n        let group = groups.get(key);\n        if (!group) {\n          groups.set(key, group = connector ? connector() : new Subject());\n          const grouped = createGroupedObservable(key, group);\n          subscriber.next(grouped);\n          if (duration) {\n            const durationSubscriber = createOperatorSubscriber(group, () => {\n              group.complete();\n              durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            }, undefined, undefined, () => groups.delete(key));\n            groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n          }\n        }\n        group.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, () => notify(consumer => consumer.complete()), handleError, () => groups.clear(), () => {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n    function createGroupedObservable(key, groupSubject) {\n      const result = new Observable(groupSubscriber => {\n        activeGroups++;\n        const innerSub = groupSubject.subscribe(groupSubscriber);\n        return () => {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "Subject", "operate", "createOperatorSubscriber", "OperatorSubscriber", "groupBy", "keySelector", "elementOrOptions", "duration", "connector", "source", "subscriber", "element", "groups", "Map", "notify", "cb", "for<PERSON>ach", "handleError", "err", "consumer", "error", "activeGroups", "teardownAttempted", "groupBySourceSubscriber", "value", "key", "group", "get", "set", "grouped", "createGroupedObservable", "next", "durationSubscriber", "complete", "unsubscribe", "undefined", "delete", "add", "subscribe", "clear", "groupSubject", "result", "groupSubscriber", "innerSub"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/rxjs/dist/esm/internal/operators/groupBy.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n    return operate((source, subscriber) => {\n        let element;\n        if (!elementOrOptions || typeof elementOrOptions === 'function') {\n            element = elementOrOptions;\n        }\n        else {\n            ({ duration, element, connector } = elementOrOptions);\n        }\n        const groups = new Map();\n        const notify = (cb) => {\n            groups.forEach(cb);\n            cb(subscriber);\n        };\n        const handleError = (err) => notify((consumer) => consumer.error(err));\n        let activeGroups = 0;\n        let teardownAttempted = false;\n        const groupBySourceSubscriber = new OperatorSubscriber(subscriber, (value) => {\n            try {\n                const key = keySelector(value);\n                let group = groups.get(key);\n                if (!group) {\n                    groups.set(key, (group = connector ? connector() : new Subject()));\n                    const grouped = createGroupedObservable(key, group);\n                    subscriber.next(grouped);\n                    if (duration) {\n                        const durationSubscriber = createOperatorSubscriber(group, () => {\n                            group.complete();\n                            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n                        }, undefined, undefined, () => groups.delete(key));\n                        groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n                    }\n                }\n                group.next(element ? element(value) : value);\n            }\n            catch (err) {\n                handleError(err);\n            }\n        }, () => notify((consumer) => consumer.complete()), handleError, () => groups.clear(), () => {\n            teardownAttempted = true;\n            return activeGroups === 0;\n        });\n        source.subscribe(groupBySourceSubscriber);\n        function createGroupedObservable(key, groupSubject) {\n            const result = new Observable((groupSubscriber) => {\n                activeGroups++;\n                const innerSub = groupSubject.subscribe(groupSubscriber);\n                return () => {\n                    innerSub.unsubscribe();\n                    --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n                };\n            });\n            result.key = key;\n            return result;\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,EAAEC,kBAAkB,QAAQ,sBAAsB;AACnF,OAAO,SAASC,OAAOA,CAACC,WAAW,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EACxE,OAAOP,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,OAAO;IACX,IAAI,CAACL,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC7DK,OAAO,GAAGL,gBAAgB;IAC9B,CAAC,MACI;MACD,CAAC;QAAEC,QAAQ;QAAEI,OAAO;QAAEH;MAAU,CAAC,GAAGF,gBAAgB;IACxD;IACA,MAAMM,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAIC,EAAE,IAAK;MACnBH,MAAM,CAACI,OAAO,CAACD,EAAE,CAAC;MAClBA,EAAE,CAACL,UAAU,CAAC;IAClB,CAAC;IACD,MAAMO,WAAW,GAAIC,GAAG,IAAKJ,MAAM,CAAEK,QAAQ,IAAKA,QAAQ,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC;IACtE,IAAIG,YAAY,GAAG,CAAC;IACpB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,MAAMC,uBAAuB,GAAG,IAAIpB,kBAAkB,CAACO,UAAU,EAAGc,KAAK,IAAK;MAC1E,IAAI;QACA,MAAMC,GAAG,GAAGpB,WAAW,CAACmB,KAAK,CAAC;QAC9B,IAAIE,KAAK,GAAGd,MAAM,CAACe,GAAG,CAACF,GAAG,CAAC;QAC3B,IAAI,CAACC,KAAK,EAAE;UACRd,MAAM,CAACgB,GAAG,CAACH,GAAG,EAAGC,KAAK,GAAGlB,SAAS,GAAGA,SAAS,CAAC,CAAC,GAAG,IAAIR,OAAO,CAAC,CAAE,CAAC;UAClE,MAAM6B,OAAO,GAAGC,uBAAuB,CAACL,GAAG,EAAEC,KAAK,CAAC;UACnDhB,UAAU,CAACqB,IAAI,CAACF,OAAO,CAAC;UACxB,IAAItB,QAAQ,EAAE;YACV,MAAMyB,kBAAkB,GAAG9B,wBAAwB,CAACwB,KAAK,EAAE,MAAM;cAC7DA,KAAK,CAACO,QAAQ,CAAC,CAAC;cAChBD,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,WAAW,CAAC,CAAC;YAC5G,CAAC,EAAEC,SAAS,EAAEA,SAAS,EAAE,MAAMvB,MAAM,CAACwB,MAAM,CAACX,GAAG,CAAC,CAAC;YAClDF,uBAAuB,CAACc,GAAG,CAACtC,SAAS,CAACQ,QAAQ,CAACsB,OAAO,CAAC,CAAC,CAACS,SAAS,CAACN,kBAAkB,CAAC,CAAC;UAC3F;QACJ;QACAN,KAAK,CAACK,IAAI,CAACpB,OAAO,GAAGA,OAAO,CAACa,KAAK,CAAC,GAAGA,KAAK,CAAC;MAChD,CAAC,CACD,OAAON,GAAG,EAAE;QACRD,WAAW,CAACC,GAAG,CAAC;MACpB;IACJ,CAAC,EAAE,MAAMJ,MAAM,CAAEK,QAAQ,IAAKA,QAAQ,CAACc,QAAQ,CAAC,CAAC,CAAC,EAAEhB,WAAW,EAAE,MAAML,MAAM,CAAC2B,KAAK,CAAC,CAAC,EAAE,MAAM;MACzFjB,iBAAiB,GAAG,IAAI;MACxB,OAAOD,YAAY,KAAK,CAAC;IAC7B,CAAC,CAAC;IACFZ,MAAM,CAAC6B,SAAS,CAACf,uBAAuB,CAAC;IACzC,SAASO,uBAAuBA,CAACL,GAAG,EAAEe,YAAY,EAAE;MAChD,MAAMC,MAAM,GAAG,IAAI3C,UAAU,CAAE4C,eAAe,IAAK;QAC/CrB,YAAY,EAAE;QACd,MAAMsB,QAAQ,GAAGH,YAAY,CAACF,SAAS,CAACI,eAAe,CAAC;QACxD,OAAO,MAAM;UACTC,QAAQ,CAACT,WAAW,CAAC,CAAC;UACtB,EAAEb,YAAY,KAAK,CAAC,IAAIC,iBAAiB,IAAIC,uBAAuB,CAACW,WAAW,CAAC,CAAC;QACtF,CAAC;MACL,CAAC,CAAC;MACFO,MAAM,CAAChB,GAAG,GAAGA,GAAG;MAChB,OAAOgB,MAAM;IACjB;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}