import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';
import { retry } from 'rxjs/operators';

@Injectable()
export class RetryInterceptor implements HttpInterceptor {
    constructor() {}

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {
        // Define the maximum number of retries
        const maxRetries = 2;
        return next.handle(request).pipe(retry(maxRetries));
    }
}
