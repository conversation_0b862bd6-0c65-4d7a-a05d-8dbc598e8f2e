{"ast": null, "code": "import { provideTransloco, TranslocoModule } from '@jsverse/transloco';\nimport { TranslocoHttpLoader } from './transloco-loader';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class TranslocoRootModule {\n  static #_ = this.ɵfac = function TranslocoRootModule_Factory(t) {\n    return new (t || TranslocoRootModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TranslocoRootModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [provideTransloco({\n      config: {\n        availableLangs: ['en', 'vi'],\n        defaultLang: 'en',\n        // Remove this option if your application doesn't support changing language in runtime.\n        reRenderOnLangChange: true,\n        prodMode: environment.production\n      },\n      loader: TranslocoHttpLoader\n    })],\n    imports: [TranslocoModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TranslocoRootModule, {\n    exports: [TranslocoModule]\n  });\n})();", "map": {"version": 3, "names": ["provideTransloco", "TranslocoModule", "TranslocoHttpLoader", "environment", "TranslocoRootModule", "_", "_2", "_3", "config", "availableLangs", "defaultLang", "reRenderOnLangChange", "prodMode", "production", "loader", "imports", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\transloco-root.module.ts"], "sourcesContent": ["import { provideTransloco, TranslocoModule } from '@jsverse/transloco';\r\nimport { NgModule } from '@angular/core';\r\nimport { TranslocoHttpLoader } from './transloco-loader';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@NgModule({\r\n    exports: [TranslocoModule],\r\n    providers: [\r\n        provideTransloco({\r\n            config: {\r\n                availableLangs: ['en', 'vi'],\r\n                defaultLang: 'en',\r\n                // Remove this option if your application doesn't support changing language in runtime.\r\n                reRenderOnLangChange: true,\r\n                prodMode: environment.production,\r\n            },\r\n            loader: TranslocoHttpLoader,\r\n        }),\r\n    ],\r\n})\r\nexport class TranslocoRootModule {}\r\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,eAAe,QAAQ,oBAAoB;AAEtE,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,WAAW,QAAQ,8BAA8B;;AAiB1D,OAAM,MAAOC,mBAAmB;EAAA,QAAAC,CAAA,G;qBAAnBD,mBAAmB;EAAA;EAAA,QAAAE,EAAA,G;UAAnBF;EAAmB;EAAA,QAAAG,EAAA,G;eAbjB,CACPP,gBAAgB,CAAC;MACbQ,MAAM,EAAE;QACJC,cAAc,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAC5BC,WAAW,EAAE,IAAI;QACjB;QACAC,oBAAoB,EAAE,IAAI;QAC1BC,QAAQ,EAAET,WAAW,CAACU;OACzB;MACDC,MAAM,EAAEZ;KACX,CAAC,CACL;IAAAa,OAAA,GAZSd,eAAe;EAAA;;;2EAchBG,mBAAmB;IAAAY,OAAA,GAdlBf,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}