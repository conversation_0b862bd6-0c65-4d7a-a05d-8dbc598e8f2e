import { Component } from '@angular/core';
import { Path } from '../../enums/path.enum';

@Component({
    selector: 'app-notfound',
    template: `<div
        class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
        <div class="flex flex-column align-items-center justify-content-center">
            <div style="border-radius: 56px; padding: 0.3rem">
                <div
                    class="w-full surface-card py-8 px-5 sm:px-8 flex flex-column align-items-center"
                    style="border-radius: 53px">
                    <h1 class="font-bold mb-0">404</h1>
                    <h1 class="text-900 font-bold text-3xl lg:text-5xl mb-2">Page Not Found</h1>
                    <h5>
                        <a [routerLink]="[dashboardUser]" class="text-center"> Go to dashboard </a>
                    </h5>
                </div>
            </div>
        </div>
    </div> `,
})
export class NotfoundComponent {
    dashboardUser = Path.DASHBOARD_USER;
}
