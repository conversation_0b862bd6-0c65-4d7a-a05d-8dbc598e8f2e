// Customer Component Styles
:host {
    display: block;
    padding: 1.5rem;
    background: var(--bg-gradient);
    min-height: 100vh;
}

// Toolbar Styling
.p-toolbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border: none !important;
    border-radius: var(--border-radius-lg) !important;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3) !important;
    padding: 1rem 1.5rem !important;
    margin-bottom: 2rem !important;

    .p-toolbar-group-left,
    .p-toolbar-group-right {
        align-items: center;
        gap: 1rem;
    }

    // Search Input Styling
    .p-input-icon-left {
        position: relative;

        .pi-search {
            color: #667eea;
            font-size: 1.1rem;
        }

        input {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 2px solid transparent !important;
            border-radius: 25px !important;
            padding: 0.75rem 1rem 0.75rem 2.5rem !important;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

            &:focus {
                border-color: #667eea !important;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
                transform: translateY(-2px);
            }

            &::placeholder {
                color: #9ca3af;
                font-style: italic;
            }
        }
    }

    // Button Styling
    .p-button {
        border-radius: 8px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;

        &:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
        }

        &:active {
            transform: translateY(0) !important;
        }

        &.p-button-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #4a9428 0%, #96d4b8 100%) !important;
            }
        }

        &.p-button-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #e6395f 0%, #e64327 100%) !important;
            }
        }

        &.p-button-help {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #3d8bfe 0%, #00d9fe 100%) !important;
            }
        }
    }

    // File Upload Styling
    .p-fileupload {
        .p-button {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
            border: none !important;

            &:hover {
                background: linear-gradient(135deg, #e85d87 0%, #e5c82d 100%) !important;
            }
        }
    }
}

// Dialog Styling
.p-dialog {
    .p-dialog-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border-radius: 12px 12px 0 0 !important;
        padding: 1.5rem !important;

        .p-dialog-title {
            font-weight: 700;
            font-size: 1.25rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .p-dialog-header-icon {
            color: white !important;

            &:hover {
                background: rgba(255, 255, 255, 0.1) !important;
            }
        }
    }

    .p-dialog-content {
        background: #ffffff !important;
        padding: 2rem !important;

        .field {
            margin-bottom: 1.5rem;

            label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #374151;
                font-size: 0.95rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            input {
                width: 100% !important;
                padding: 0.75rem 1rem !important;
                border: 2px solid #e5e7eb !important;
                border-radius: 8px !important;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: #f9fafb !important;

                &:focus {
                    border-color: #667eea !important;
                    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
                    background: white !important;
                    transform: translateY(-1px);
                }

                &:hover {
                    border-color: #d1d5db !important;
                }
            }

            .p-error {
                color: #ef4444;
                font-size: 0.875rem;
                margin-top: 0.25rem;
                font-weight: 500;
            }
        }
    }

    .p-dialog-footer {
        background: #f9fafb !important;
        border-radius: 0 0 12px 12px !important;
        padding: 1.5rem !important;
        border-top: 1px solid #e5e7eb !important;

        .p-button {
            margin: 0 0.5rem;
            padding: 0.75rem 2rem !important;
            font-weight: 600 !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-radius: 8px !important;

            &.p-button-text {
                background: transparent !important;
                color: #6b7280 !important;
                border: 2px solid #e5e7eb !important;

                &:hover {
                    background: #f3f4f6 !important;
                    color: #374151 !important;
                    border-color: #d1d5db !important;
                }
            }

            &.p-button-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                border: none !important;

                &:hover {
                    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
                }
            }
        }
    }
}

// Card Container for the whole component
.customer-container {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    margin: 1rem;
    position: relative;

    // Toolbar and table content area
    .p-toolbar,
    exex-table {
        margin: 0 2rem;
    }

    .p-toolbar {
        margin-top: 2rem;
        border-radius: var(--border-radius-lg) !important;
    }

    exex-table {
        margin-bottom: 2rem;
    }
}

// Animation for page load
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

:host {
    animation: fadeInUp 0.6s ease-out;
}

// Page Header Styling
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;

    .page-title {
        flex: 1;
        min-width: 300px;

        h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;

            i {
                font-size: 2rem;
                background: rgba(255, 255, 255, 0.2);
                padding: 0.75rem;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
        }

        p {
            margin: 0;
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 0.5px;
        }
    }

    .page-stats {
        display: flex;
        gap: 1.5rem;
        flex-wrap: wrap;

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            text-align: center;
            min-width: 120px;
            transition: var(--transition-normal);

            &:hover {
                background: rgba(255, 255, 255, 0.25);
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: 700;
                line-height: 1;
                margin-bottom: 0.5rem;
                background: linear-gradient(45deg, #ffffff, #f0f0f0);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .stat-label {
                font-size: 0.9rem;
                text-transform: uppercase;
                letter-spacing: 1px;
                opacity: 0.9;
                font-weight: 500;
            }
        }
    }

    @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;

        .page-title {
            min-width: auto;

            h1 {
                font-size: 2rem;
                justify-content: center;
            }
        }

        .page-stats {
            justify-content: center;
            width: 100%;

            .stat-card {
                flex: 1;
                min-width: 100px;
            }
        }
    }
}
