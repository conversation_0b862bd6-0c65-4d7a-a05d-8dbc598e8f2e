{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class BaseUrlInterceptor {\n  intercept(request, next) {\n    const apiRequest = request.clone({\n      url: this.prepareUrl(request.url)\n    });\n    return next.handle(apiRequest);\n  }\n  isAbsoluteUrl(url) {\n    const absolutePattern = /^https?:\\/\\//i;\n    return absolutePattern.test(url);\n  }\n  prepareUrl(url) {\n    const conditions = ['assets'];\n    url = conditions.some(val => url.includes(val)) || this.isAbsoluteUrl(url) ? url : environment.urlApi + url;\n    return url;\n  }\n  static #_ = this.ɵfac = function BaseUrlInterceptor_Factory(t) {\n    return new (t || BaseUrlInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: BaseUrlInterceptor,\n    factory: BaseUrlInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["environment", "BaseUrlInterceptor", "intercept", "request", "next", "apiRequest", "clone", "url", "prepareUrl", "handle", "isAbsoluteUrl", "absolutePattern", "test", "conditions", "some", "val", "includes", "urlApi", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\base-url.interceptor.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable()\r\nexport class BaseUrlInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler) {\r\n        const apiRequest = request.clone({\r\n            url: this.prepareUrl(request.url),\r\n        });\r\n        return next.handle(apiRequest);\r\n    }\r\n\r\n    private isAbsoluteUrl(url: string): boolean {\r\n        const absolutePattern = /^https?:\\/\\//i;\r\n        return absolutePattern.test(url);\r\n    }\r\n\r\n    private prepareUrl(url: string): string {\r\n        const conditions = ['assets'];\r\n        url = conditions.some((val) => url.includes(val)) || this.isAbsoluteUrl(url) ? url : environment.urlApi + url;\r\n        return url;\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,8BAA8B;;AAG1D,OAAM,MAAOC,kBAAkB;EAC3BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,MAAMC,UAAU,GAAGF,OAAO,CAACG,KAAK,CAAC;MAC7BC,GAAG,EAAE,IAAI,CAACC,UAAU,CAACL,OAAO,CAACI,GAAG;KACnC,CAAC;IACF,OAAOH,IAAI,CAACK,MAAM,CAACJ,UAAU,CAAC;EAClC;EAEQK,aAAaA,CAACH,GAAW;IAC7B,MAAMI,eAAe,GAAG,eAAe;IACvC,OAAOA,eAAe,CAACC,IAAI,CAACL,GAAG,CAAC;EACpC;EAEQC,UAAUA,CAACD,GAAW;IAC1B,MAAMM,UAAU,GAAG,CAAC,QAAQ,CAAC;IAC7BN,GAAG,GAAGM,UAAU,CAACC,IAAI,CAAEC,GAAG,IAAKR,GAAG,CAACS,QAAQ,CAACD,GAAG,CAAC,CAAC,IAAI,IAAI,CAACL,aAAa,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAGP,WAAW,CAACiB,MAAM,GAAGV,GAAG;IAC7G,OAAOA,GAAG;EACd;EAAC,QAAAW,CAAA,G;qBAjBQjB,kBAAkB;EAAA;EAAA,QAAAkB,EAAA,G;WAAlBlB,kBAAkB;IAAAmB,OAAA,EAAlBnB,kBAAkB,CAAAoB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}