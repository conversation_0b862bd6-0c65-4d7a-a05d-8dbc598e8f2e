{"ast": null, "code": "import { Path } from '../core/enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./app.menuitem.component\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const item_r1 = ctx_r4.$implicit;\n    const i_r2 = ctx_r4.index;\n    i0.ɵɵproperty(\"item\", item_r1)(\"index\", i_r2)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r1.separator);\n  }\n}\nexport class AppMenuComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n    this.model = [];\n  }\n  ngOnInit() {\n    this.model = [{\n      label: 'Home',\n      items: [{\n        label: 'User',\n        icon: 'pi pi-fw pi-user',\n        routerLink: [Path.DASHBOARD_USER]\n      }, {\n        label: 'Submenu 1',\n        icon: 'pi pi-fw pi-bookmark',\n        items: [{\n          label: 'Submenu 1.1',\n          icon: 'pi pi-fw pi-bookmark',\n          items: [{\n            label: 'Submenu 1.1.1',\n            icon: 'pi pi-fw pi-bookmark',\n            routerLink: ['/uikit/misc']\n          }, {\n            label: 'Submenu 1.1.2',\n            icon: 'pi pi-fw pi-bookmark'\n          }, {\n            label: 'Submenu 1.1.3',\n            icon: 'pi pi-fw pi-bookmark'\n          }]\n        }, {\n          label: 'Submenu 1.2',\n          icon: 'pi pi-fw pi-bookmark',\n          items: [{\n            label: 'Submenu 1.2.1',\n            icon: 'pi pi-fw pi-bookmark'\n          }]\n        }]\n      }]\n    }];\n  }\n  static #_ = this.ɵfac = function AppMenuComponent_Factory(t) {\n    return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppMenuComponent,\n    selectors: [[\"app-menu\"]],\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"]],\n    template: function AppMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ul\", 0);\n        i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.AppMenuitemComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Path", "i0", "ɵɵelement", "ɵɵproperty", "item_r1", "i_r2", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_ng_container_1_li_1_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "separator", "AppMenuComponent", "constructor", "layoutService", "model", "ngOnInit", "label", "items", "icon", "routerLink", "DASHBOARD_USER", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "AppMenuComponent_ng_container_1_Template", "ɵɵelementEnd"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\layout\\app.menu.component.ts"], "sourcesContent": ["import { OnInit } from '@angular/core';\nimport { Component } from '@angular/core';\nimport { LayoutService } from './app.layout.service';\nimport { Path } from '../core/enums/path.enum';\n\n@Component({\n    selector: 'app-menu',\n    template: `<ul class=\"layout-menu\">\n        <ng-container *ngFor=\"let item of model; let i = index\">\n            <li\n                app-menuitem\n                *ngIf=\"!item.separator\"\n                [item]=\"item\"\n                [index]=\"i\"\n                [root]=\"true\"\n            ></li>\n        </ng-container>\n    </ul>`,\n})\nexport class AppMenuComponent implements OnInit {\n    model: any[] = [];\n\n    constructor(public layoutService: LayoutService) {}\n\n    ngOnInit() {\n        this.model = [\n            {\n                label: 'Home',\n                items: [\n                    {\n                        label: 'User',\n                        icon: 'pi pi-fw pi-user',\n                        routerLink: [Path.DASHBOARD_USER],\n                    },\n                    {\n                        label: 'Submenu 1',\n                        icon: 'pi pi-fw pi-bookmark',\n                        items: [\n                            {\n                                label: 'Submenu 1.1',\n                                icon: 'pi pi-fw pi-bookmark',\n                                items: [\n                                    {\n                                        label: 'Submenu 1.1.1',\n                                        icon: 'pi pi-fw pi-bookmark',\n                                        routerLink: ['/uikit/misc'],\n                                    },\n                                    {\n                                        label: 'Submenu 1.1.2',\n                                        icon: 'pi pi-fw pi-bookmark',\n                                    },\n                                    {\n                                        label: 'Submenu 1.1.3',\n                                        icon: 'pi pi-fw pi-bookmark',\n                                    },\n                                ],\n                            },\n                            {\n                                label: 'Submenu 1.2',\n                                icon: 'pi pi-fw pi-bookmark',\n                                items: [\n                                    {\n                                        label: 'Submenu 1.2.1',\n                                        icon: 'pi pi-fw pi-bookmark',\n                                    },\n                                ],\n                            },\n                        ],\n                    },\n                ],\n            },\n        ];\n    }\n}\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,yBAAyB;;;;;;;IAMlCC,EAAA,CAAAC,SAAA,YAMM;;;;;;IAHFD,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA;;;;;IAJrBJ,EAAA,CAAAK,uBAAA,GAAwD;IACpDL,EAAA,CAAAM,UAAA,IAAAC,6CAAA,gBAMM;IACVP,EAAA,CAAAQ,qBAAA,EAAe;;;;IALNR,EAAA,CAAAS,SAAA,GAAqB;IAArBT,EAAA,CAAAE,UAAA,UAAAC,OAAA,CAAAO,SAAA,CAAqB;;;AAQtC,OAAM,MAAOC,gBAAgB;EAGzBC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAFhC,KAAAC,KAAK,GAAU,EAAE;EAEiC;EAElDC,QAAQA,CAAA;IACJ,IAAI,CAACD,KAAK,GAAG,CACT;MACIE,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,CACH;QACID,KAAK,EAAE,MAAM;QACbE,IAAI,EAAE,kBAAkB;QACxBC,UAAU,EAAE,CAACpB,IAAI,CAACqB,cAAc;OACnC,EACD;QACIJ,KAAK,EAAE,WAAW;QAClBE,IAAI,EAAE,sBAAsB;QAC5BD,KAAK,EAAE,CACH;UACID,KAAK,EAAE,aAAa;UACpBE,IAAI,EAAE,sBAAsB;UAC5BD,KAAK,EAAE,CACH;YACID,KAAK,EAAE,eAAe;YACtBE,IAAI,EAAE,sBAAsB;YAC5BC,UAAU,EAAE,CAAC,aAAa;WAC7B,EACD;YACIH,KAAK,EAAE,eAAe;YACtBE,IAAI,EAAE;WACT,EACD;YACIF,KAAK,EAAE,eAAe;YACtBE,IAAI,EAAE;WACT;SAER,EACD;UACIF,KAAK,EAAE,aAAa;UACpBE,IAAI,EAAE,sBAAsB;UAC5BD,KAAK,EAAE,CACH;YACID,KAAK,EAAE,eAAe;YACtBE,IAAI,EAAE;WACT;SAER;OAER;KAER,CACJ;EACL;EAAC,QAAAG,CAAA,G;qBArDQV,gBAAgB,EAAAX,EAAA,CAAAsB,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBd,gBAAgB;IAAAe,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAZdhC,EAAA,CAAAkC,cAAA,YAAwB;QAC/BlC,EAAA,CAAAM,UAAA,IAAA6B,wCAAA,0BAQe;QACnBnC,EAAA,CAAAoC,YAAA,EAAK;;;QAT8BpC,EAAA,CAAAS,SAAA,GAAU;QAAVT,EAAA,CAAAE,UAAA,YAAA+B,GAAA,CAAAnB,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}