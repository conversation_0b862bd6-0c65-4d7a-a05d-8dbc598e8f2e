{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { AppConfigComponent } from './app.config.component';\nimport * as i0 from \"@angular/core\";\nexport class AppConfigModule {\n  static #_ = this.ɵfac = function AppConfigModule_Factory(t) {\n    return new (t || AppConfigModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppConfigModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppConfigModule, {\n    declarations: [AppConfigComponent],\n    imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule],\n    exports: [AppConfigComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "SidebarModule", "RadioButtonModule", "ButtonModule", "InputSwitchModule", "AppConfigComponent", "AppConfigModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\layout\\config\\config.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { AppConfigComponent } from './app.config.component';\n\n@NgModule({\n    imports: [\n        CommonModule,\n        FormsModule,\n        SidebarModule,\n        RadioButtonModule,\n        ButtonModule,\n        InputSwitchModule\n    ],\n    declarations: [\n        AppConfigComponent\n    ],\n    exports: [\n        AppConfigComponent\n    ]\n})\nexport class AppConfigModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,kBAAkB,QAAQ,wBAAwB;;AAkB3D,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAdpBV,YAAY,EACZC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB;EAAA;;;2EASZE,eAAe;IAAAI,YAAA,GANpBL,kBAAkB;IAAAM,OAAA,GARlBZ,YAAY,EACZC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB;IAAAQ,OAAA,GAMjBP,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}