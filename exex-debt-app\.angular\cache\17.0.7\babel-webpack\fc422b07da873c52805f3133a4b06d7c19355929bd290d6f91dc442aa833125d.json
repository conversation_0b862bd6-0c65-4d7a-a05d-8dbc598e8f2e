{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { NgModule } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { LoginComponent } from './login/login.component';\nlet AuthModule = class AuthModule {};\nAuthModule = __decorate([NgModule({\n  declarations: [LoginComponent],\n  imports: [CommonModule, AuthRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, PasswordModule]\n})], AuthModule);\nexport { AuthModule };", "map": {"version": 3, "names": ["CommonModule", "NgModule", "FormsModule", "ButtonModule", "CheckboxModule", "InputTextModule", "PasswordModule", "AuthRoutingModule", "LoginComponent", "AuthModule", "__decorate", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\auth.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\nimport { LoginComponent } from './login/login.component';\r\n@NgModule({\r\n    declarations: [LoginComponent],\r\n    imports: [\r\n        CommonModule,\r\n        AuthRoutingModule,\r\n        ButtonModule,\r\n        CheckboxModule,\r\n        InputTextModule,\r\n        FormsModule,\r\n        PasswordModule,\r\n    ],\r\n})\r\nexport class AuthModule {}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,yBAAyB;AAajD,IAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAG;AAAbA,UAAU,GAAAC,UAAA,EAZtBT,QAAQ,CAAC;EACNU,YAAY,EAAE,CAACH,cAAc,CAAC;EAC9BI,OAAO,EAAE,CACLZ,YAAY,EACZO,iBAAiB,EACjBJ,YAAY,EACZC,cAAc,EACdC,eAAe,EACfH,WAAW,EACXI,cAAc;CAErB,CAAC,C,EACWG,UAAU,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}