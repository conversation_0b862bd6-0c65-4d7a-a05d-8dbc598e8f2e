{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { animate, state, style, transition, trigger } from '@angular/animations';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./app.menu.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/ripple\";\nconst _c0 = [\"app-menuitem\", \"\"];\nfunction AppMenuitemComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.item.label);\n  }\n}\nfunction AppMenuitemComponent_a_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 9);\n  }\n}\nfunction AppMenuitemComponent_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.itemClick($event));\n    });\n    i0.ɵɵelement(1, \"i\", 6);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AppMenuitemComponent_a_2_i_4_Template, 1, 0, \"i\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.class);\n    i0.ɵɵattribute(\"href\", ctx_r1.item.url, i0.ɵɵsanitizeUrl)(\"target\", ctx_r1.item.target);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.item.label);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.items);\n  }\n}\nfunction AppMenuitemComponent_a_3_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 9);\n  }\n}\nconst _c1 = () => ({\n  paths: \"exact\",\n  queryParams: \"ignored\",\n  matrixParams: \"ignored\",\n  fragment: \"ignored\"\n});\nfunction AppMenuitemComponent_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function AppMenuitemComponent_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.itemClick($event));\n    });\n    i0.ɵɵelement(1, \"i\", 6);\n    i0.ɵɵelementStart(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AppMenuitemComponent_a_3_i_4_Template, 1, 0, \"i\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.item.class)(\"routerLink\", ctx_r2.item.routerLink)(\"routerLinkActiveOptions\", ctx_r2.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(14, _c1))(\"fragment\", ctx_r2.item.fragment)(\"queryParamsHandling\", ctx_r2.item.queryParamsHandling)(\"preserveFragment\", ctx_r2.item.preserveFragment)(\"skipLocationChange\", ctx_r2.item.skipLocationChange)(\"replaceUrl\", ctx_r2.item.replaceUrl)(\"state\", ctx_r2.item.state)(\"queryParams\", ctx_r2.item.queryParams);\n    i0.ɵɵattribute(\"target\", ctx_r2.item.target);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.item.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.item.label);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.item.items);\n  }\n}\nfunction AppMenuitemComponent_ul_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 12);\n  }\n  if (rf & 2) {\n    const child_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(child_r11.badgeClass);\n    i0.ɵɵproperty(\"item\", child_r11)(\"index\", i_r12)(\"parentKey\", ctx_r10.key);\n  }\n}\nfunction AppMenuitemComponent_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, AppMenuitemComponent_ul_4_ng_template_1_Template, 1, 5, \"ng-template\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@children\", ctx_r3.submenuAnimation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.item.items);\n  }\n}\nexport class AppMenuitemComponent {\n  constructor(layoutService, cd, router, menuService) {\n    this.layoutService = layoutService;\n    this.cd = cd;\n    this.router = router;\n    this.menuService = menuService;\n    this.active = false;\n    this.key = '';\n    this.menuSourceSubscription = this.menuService.menuSource$.subscribe(value => {\n      Promise.resolve(null).then(() => {\n        if (value.routeEvent) {\n          this.active = value.key === this.key || value.key.startsWith(this.key + '-') ? true : false;\n        } else {\n          if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {\n            this.active = false;\n          }\n        }\n      });\n    });\n    this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {\n      this.active = false;\n    });\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(params => {\n      if (this.item.routerLink) {\n        this.updateActiveStateFromRoute();\n      }\n    });\n  }\n  ngOnInit() {\n    this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);\n    if (this.item.routerLink) {\n      this.updateActiveStateFromRoute();\n    }\n  }\n  updateActiveStateFromRoute() {\n    let activeRoute = this.router.isActive(this.item.routerLink[0], {\n      paths: 'exact',\n      queryParams: 'ignored',\n      matrixParams: 'ignored',\n      fragment: 'ignored'\n    });\n    if (activeRoute) {\n      this.menuService.onMenuStateChange({\n        key: this.key,\n        routeEvent: true\n      });\n    }\n  }\n  itemClick(event) {\n    // avoid processing disabled items\n    if (this.item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    // execute command\n    if (this.item.command) {\n      this.item.command({\n        originalEvent: event,\n        item: this.item\n      });\n    }\n    // toggle active state\n    if (this.item.items) {\n      this.active = !this.active;\n    }\n    this.menuService.onMenuStateChange({\n      key: this.key\n    });\n  }\n  get submenuAnimation() {\n    return this.root ? 'expanded' : this.active ? 'expanded' : 'collapsed';\n  }\n  get activeClass() {\n    return this.active && !this.root;\n  }\n  ngOnDestroy() {\n    if (this.menuSourceSubscription) {\n      this.menuSourceSubscription.unsubscribe();\n    }\n    if (this.menuResetSubscription) {\n      this.menuResetSubscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function AppMenuitemComponent_Factory(t) {\n    return new (t || AppMenuitemComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MenuService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppMenuitemComponent,\n    selectors: [[\"\", \"app-menuitem\", \"\"]],\n    hostVars: 4,\n    hostBindings: function AppMenuitemComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"layout-root-menuitem\", ctx.root)(\"active-menuitem\", ctx.activeClass);\n      }\n    },\n    inputs: {\n      item: \"item\",\n      index: \"index\",\n      root: \"root\",\n      parentKey: \"parentKey\"\n    },\n    attrs: _c0,\n    decls: 5,\n    vars: 4,\n    consts: [[\"class\", \"layout-menuitem-root-text\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"layout-menuitem-root-text\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"click\"], [1, \"layout-menuitem-icon\", 3, \"ngClass\"], [1, \"layout-menuitem-text\"], [\"class\", \"pi pi-fw pi-angle-down layout-submenu-toggler\", 4, \"ngIf\"], [1, \"pi\", \"pi-fw\", \"pi-angle-down\", \"layout-submenu-toggler\"], [\"routerLinkActive\", \"active-route\", \"tabindex\", \"0\", \"pRipple\", \"\", 3, \"ngClass\", \"routerLink\", \"routerLinkActiveOptions\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"queryParams\", \"click\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"parentKey\"]],\n    template: function AppMenuitemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0);\n        i0.ɵɵtemplate(1, AppMenuitemComponent_div_1_Template, 2, 1, \"div\", 0)(2, AppMenuitemComponent_a_2_Template, 5, 6, \"a\", 1)(3, AppMenuitemComponent_a_3_Template, 5, 15, \"a\", 2)(4, AppMenuitemComponent_ul_4_Template, 2, 2, \"ul\", 3);\n        i0.ɵɵelementContainerEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.root && ctx.item.visible !== false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (!ctx.item.routerLink || ctx.item.items) && ctx.item.visible !== false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.item.routerLink && !ctx.item.items && ctx.item.visible !== false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.item.items && ctx.item.visible !== false);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.Ripple, i2.RouterLink, i2.RouterLinkActive, AppMenuitemComponent],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('children', [state('collapsed', style({\n        height: '0'\n      })), state('expanded', style({\n        height: '*'\n      })), transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)'))])]\n    }\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "animate", "state", "style", "transition", "trigger", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "item", "label", "ɵɵelement", "ɵɵlistener", "AppMenuitemComponent_a_2_Template_a_click_0_listener", "$event", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "itemClick", "ɵɵtemplate", "AppMenuitemComponent_a_2_i_4_Template", "ɵɵproperty", "ctx_r1", "class", "ɵɵattribute", "url", "ɵɵsanitizeUrl", "target", "icon", "items", "AppMenuitemComponent_a_3_Template_a_click_0_listener", "_r9", "ctx_r8", "AppMenuitemComponent_a_3_i_4_Template", "ctx_r2", "routerLink", "routerLinkActiveOptions", "ɵɵpureFunction0", "_c1", "fragment", "queryParamsHandling", "preserveFragment", "skipLocationChange", "replaceUrl", "queryParams", "ɵɵclassMap", "child_r11", "badgeClass", "i_r12", "ctx_r10", "key", "AppMenuitemComponent_ul_4_ng_template_1_Template", "ctx_r3", "submenuAnimation", "AppMenuitemComponent", "constructor", "layoutService", "cd", "router", "menuService", "active", "menuSourceSubscription", "menuSource$", "subscribe", "value", "Promise", "resolve", "then", "routeEvent", "startsWith", "menuResetSubscription", "resetSource$", "events", "pipe", "event", "params", "updateActiveStateFromRoute", "ngOnInit", "parent<PERSON><PERSON>", "index", "String", "activeRoute", "isActive", "paths", "matrixParams", "onMenuStateChange", "disabled", "preventDefault", "command", "originalEvent", "root", "activeClass", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "ChangeDetectorRef", "i2", "Router", "i3", "MenuService", "_2", "selectors", "hostVars", "hostBindings", "AppMenuitemComponent_HostBindings", "rf", "ctx", "ɵɵelementContainerStart", "AppMenuitemComponent_div_1_Template", "AppMenuitemComponent_a_2_Template", "AppMenuitemComponent_a_3_Template", "AppMenuitemComponent_ul_4_Template", "ɵɵelementContainerEnd", "visible", "encapsulation", "data", "animation", "height"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.menuitem.component.ts"], "sourcesContent": ["import { ChangeDetector<PERSON><PERSON>, Component, Host, HostBinding, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { animate, state, style, transition, trigger } from '@angular/animations';\r\nimport { Subscription } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\nimport { MenuService } from './app.menu.service';\r\nimport { LayoutService } from './app.layout.service';\r\n\r\n@Component({\r\n    // eslint-disable-next-line @angular-eslint/component-selector\r\n    selector: '[app-menuitem]',\r\n    template: `\r\n        <ng-container>\r\n            <div *ngIf=\"root && item.visible !== false\" class=\"layout-menuitem-root-text\">{{ item.label }}</div>\r\n            <a\r\n                *ngIf=\"(!item.routerLink || item.items) && item.visible !== false\"\r\n                [attr.href]=\"item.url\"\r\n                (click)=\"itemClick($event)\"\r\n                [ngClass]=\"item.class\"\r\n                [attr.target]=\"item.target\"\r\n                tabindex=\"0\"\r\n                pRipple>\r\n                <i [ngClass]=\"item.icon\" class=\"layout-menuitem-icon\"></i>\r\n                <span class=\"layout-menuitem-text\">{{ item.label }}</span>\r\n                <i class=\"pi pi-fw pi-angle-down layout-submenu-toggler\" *ngIf=\"item.items\"></i>\r\n            </a>\r\n            <a\r\n                *ngIf=\"item.routerLink && !item.items && item.visible !== false\"\r\n                (click)=\"itemClick($event)\"\r\n                [ngClass]=\"item.class\"\r\n                [routerLink]=\"item.routerLink\"\r\n                routerLinkActive=\"active-route\"\r\n                [routerLinkActiveOptions]=\"\r\n                    item.routerLinkActiveOptions || {\r\n                        paths: 'exact',\r\n                        queryParams: 'ignored',\r\n                        matrixParams: 'ignored',\r\n                        fragment: 'ignored',\r\n                    }\r\n                \"\r\n                [fragment]=\"item.fragment\"\r\n                [queryParamsHandling]=\"item.queryParamsHandling\"\r\n                [preserveFragment]=\"item.preserveFragment\"\r\n                [skipLocationChange]=\"item.skipLocationChange\"\r\n                [replaceUrl]=\"item.replaceUrl\"\r\n                [state]=\"item.state\"\r\n                [queryParams]=\"item.queryParams\"\r\n                [attr.target]=\"item.target\"\r\n                tabindex=\"0\"\r\n                pRipple>\r\n                <i [ngClass]=\"item.icon\" class=\"layout-menuitem-icon\"></i>\r\n                <span class=\"layout-menuitem-text\">{{ item.label }}</span>\r\n                <i class=\"pi pi-fw pi-angle-down layout-submenu-toggler\" *ngIf=\"item.items\"></i>\r\n            </a>\r\n\r\n            <ul *ngIf=\"item.items && item.visible !== false\" [@children]=\"submenuAnimation\">\r\n                <ng-template ngFor let-child let-i=\"index\" [ngForOf]=\"item.items\">\r\n                    <li app-menuitem [item]=\"child\" [index]=\"i\" [parentKey]=\"key\" [class]=\"child.badgeClass\"></li>\r\n                </ng-template>\r\n            </ul>\r\n        </ng-container>\r\n    `,\r\n    animations: [\r\n        trigger('children', [\r\n            state(\r\n                'collapsed',\r\n                style({\r\n                    height: '0',\r\n                }),\r\n            ),\r\n            state(\r\n                'expanded',\r\n                style({\r\n                    height: '*',\r\n                }),\r\n            ),\r\n            transition('collapsed <=> expanded', animate('400ms cubic-bezier(0.86, 0, 0.07, 1)')),\r\n        ]),\r\n    ],\r\n})\r\nexport class AppMenuitemComponent implements OnInit, OnDestroy {\r\n    @Input() item: any;\r\n\r\n    @Input() index!: number;\r\n\r\n    @Input() @HostBinding('class.layout-root-menuitem') root!: boolean;\r\n\r\n    @Input() parentKey!: string;\r\n\r\n    active = false;\r\n\r\n    menuSourceSubscription: Subscription;\r\n\r\n    menuResetSubscription: Subscription;\r\n\r\n    key: string = '';\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private cd: ChangeDetectorRef,\r\n        public router: Router,\r\n        private menuService: MenuService,\r\n    ) {\r\n        this.menuSourceSubscription = this.menuService.menuSource$.subscribe((value) => {\r\n            Promise.resolve(null).then(() => {\r\n                if (value.routeEvent) {\r\n                    this.active = value.key === this.key || value.key.startsWith(this.key + '-') ? true : false;\r\n                } else {\r\n                    if (value.key !== this.key && !value.key.startsWith(this.key + '-')) {\r\n                        this.active = false;\r\n                    }\r\n                }\r\n            });\r\n        });\r\n\r\n        this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {\r\n            this.active = false;\r\n        });\r\n\r\n        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((params) => {\r\n            if (this.item.routerLink) {\r\n                this.updateActiveStateFromRoute();\r\n            }\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.key = this.parentKey ? this.parentKey + '-' + this.index : String(this.index);\r\n\r\n        if (this.item.routerLink) {\r\n            this.updateActiveStateFromRoute();\r\n        }\r\n    }\r\n\r\n    updateActiveStateFromRoute() {\r\n        let activeRoute = this.router.isActive(this.item.routerLink[0], {\r\n            paths: 'exact',\r\n            queryParams: 'ignored',\r\n            matrixParams: 'ignored',\r\n            fragment: 'ignored',\r\n        });\r\n\r\n        if (activeRoute) {\r\n            this.menuService.onMenuStateChange({ key: this.key, routeEvent: true });\r\n        }\r\n    }\r\n\r\n    itemClick(event: Event) {\r\n        // avoid processing disabled items\r\n        if (this.item.disabled) {\r\n            event.preventDefault();\r\n            return;\r\n        }\r\n\r\n        // execute command\r\n        if (this.item.command) {\r\n            this.item.command({ originalEvent: event, item: this.item });\r\n        }\r\n\r\n        // toggle active state\r\n        if (this.item.items) {\r\n            this.active = !this.active;\r\n        }\r\n\r\n        this.menuService.onMenuStateChange({ key: this.key });\r\n    }\r\n\r\n    get submenuAnimation() {\r\n        return this.root ? 'expanded' : this.active ? 'expanded' : 'collapsed';\r\n    }\r\n\r\n    @HostBinding('class.active-menuitem')\r\n    get activeClass() {\r\n        return this.active && !this.root;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        if (this.menuSourceSubscription) {\r\n            this.menuSourceSubscription.unsubscribe();\r\n        }\r\n\r\n        if (this.menuResetSubscription) {\r\n            this.menuResetSubscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAgB,iBAAiB;AACvD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAEhF,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;IAS3BC,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAgB;;;;;IAW1FR,EAAA,CAAAS,SAAA,WAAgF;;;;;;IAVpFT,EAAA,CAAAC,cAAA,WAOY;IAJRD,EAAA,CAAAU,UAAA,mBAAAC,qDAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAN,MAAA,CAAiB;IAAA,EAAC;IAK3BZ,EAAA,CAAAS,SAAA,WAA0D;IAC1DT,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAmB,UAAA,IAAAC,qCAAA,eAAgF;IACpFpB,EAAA,CAAAG,YAAA,EAAI;;;;IAPAH,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAf,IAAA,CAAAgB,KAAA,CAAsB;IAFtBvB,EAAA,CAAAwB,WAAA,SAAAF,MAAA,CAAAf,IAAA,CAAAkB,GAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAAsB,WAAAJ,MAAA,CAAAf,IAAA,CAAAoB,MAAA;IAMnB3B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAf,IAAA,CAAAqB,IAAA,CAAqB;IACW5B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAiB,MAAA,CAAAf,IAAA,CAAAC,KAAA,CAAgB;IACOR,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAqB,UAAA,SAAAC,MAAA,CAAAf,IAAA,CAAAsB,KAAA,CAAgB;;;;;IA4B1E7B,EAAA,CAAAS,SAAA,WAAgF;;;;;;;;;;;;IA1BpFT,EAAA,CAAAC,cAAA,YAuBY;IArBRD,EAAA,CAAAU,UAAA,mBAAAoB,qDAAAlB,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAC,MAAA,GAAAhC,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAe,MAAA,CAAAd,SAAA,CAAAN,MAAA,CAAiB;IAAA,EAAC;IAsB3BZ,EAAA,CAAAS,SAAA,WAA0D;IAC1DT,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAmB,UAAA,IAAAc,qCAAA,eAAgF;IACpFjC,EAAA,CAAAG,YAAA,EAAI;;;;IAxBAH,EAAA,CAAAqB,UAAA,YAAAa,MAAA,CAAA3B,IAAA,CAAAgB,KAAA,CAAsB,eAAAW,MAAA,CAAA3B,IAAA,CAAA4B,UAAA,6BAAAD,MAAA,CAAA3B,IAAA,CAAA6B,uBAAA,IAAApC,EAAA,CAAAqC,eAAA,KAAAC,GAAA,eAAAJ,MAAA,CAAA3B,IAAA,CAAAgC,QAAA,yBAAAL,MAAA,CAAA3B,IAAA,CAAAiC,mBAAA,sBAAAN,MAAA,CAAA3B,IAAA,CAAAkC,gBAAA,wBAAAP,MAAA,CAAA3B,IAAA,CAAAmC,kBAAA,gBAAAR,MAAA,CAAA3B,IAAA,CAAAoC,UAAA,WAAAT,MAAA,CAAA3B,IAAA,CAAAZ,KAAA,iBAAAuC,MAAA,CAAA3B,IAAA,CAAAqC,WAAA;IAkBtB5C,EAAA,CAAAwB,WAAA,WAAAU,MAAA,CAAA3B,IAAA,CAAAoB,MAAA,CAA2B;IAGxB3B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,YAAAa,MAAA,CAAA3B,IAAA,CAAAqB,IAAA,CAAqB;IACW5B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA6B,MAAA,CAAA3B,IAAA,CAAAC,KAAA,CAAgB;IACOR,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAqB,UAAA,SAAAa,MAAA,CAAA3B,IAAA,CAAAsB,KAAA,CAAgB;;;;;IAKtE7B,EAAA,CAAAS,SAAA,aAA8F;;;;;;IAAhCT,EAAA,CAAA6C,UAAA,CAAAC,SAAA,CAAAC,UAAA,CAA0B;IAAvE/C,EAAA,CAAAqB,UAAA,SAAAyB,SAAA,CAAc,UAAAE,KAAA,eAAAC,OAAA,CAAAC,GAAA;;;;;IAFvClD,EAAA,CAAAC,cAAA,SAAgF;IAC5ED,EAAA,CAAAmB,UAAA,IAAAgC,gDAAA,0BAEc;IAClBnD,EAAA,CAAAG,YAAA,EAAK;;;;IAJ4CH,EAAA,CAAAqB,UAAA,cAAA+B,MAAA,CAAAC,gBAAA,CAA8B;IAChCrD,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,UAAA,YAAA+B,MAAA,CAAA7C,IAAA,CAAAsB,KAAA,CAAsB;;;AAwBjF,OAAM,MAAOyB,oBAAoB;EAiB7BC,YACWC,aAA4B,EAC3BC,EAAqB,EACtBC,MAAc,EACbC,WAAwB;IAHzB,KAAAH,aAAa,GAAbA,aAAa;IACZ,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,WAAW,GAAXA,WAAW;IAZvB,KAAAC,MAAM,GAAG,KAAK;IAMd,KAAAV,GAAG,GAAW,EAAE;IAQZ,IAAI,CAACW,sBAAsB,GAAG,IAAI,CAACF,WAAW,CAACG,WAAW,CAACC,SAAS,CAAEC,KAAK,IAAI;MAC3EC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAK;QAC5B,IAAIH,KAAK,CAACI,UAAU,EAAE;UAClB,IAAI,CAACR,MAAM,GAAGI,KAAK,CAACd,GAAG,KAAK,IAAI,CAACA,GAAG,IAAIc,KAAK,CAACd,GAAG,CAACmB,UAAU,CAAC,IAAI,CAACnB,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;SAC9F,MAAM;UACH,IAAIc,KAAK,CAACd,GAAG,KAAK,IAAI,CAACA,GAAG,IAAI,CAACc,KAAK,CAACd,GAAG,CAACmB,UAAU,CAAC,IAAI,CAACnB,GAAG,GAAG,GAAG,CAAC,EAAE;YACjE,IAAI,CAACU,MAAM,GAAG,KAAK;;;MAG/B,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,IAAI,CAACU,qBAAqB,GAAG,IAAI,CAACX,WAAW,CAACY,YAAY,CAACR,SAAS,CAAC,MAAK;MACtE,IAAI,CAACH,MAAM,GAAG,KAAK;IACvB,CAAC,CAAC;IAEF,IAAI,CAACF,MAAM,CAACc,MAAM,CAACC,IAAI,CAAC1E,MAAM,CAAE2E,KAAK,IAAKA,KAAK,YAAYjF,aAAa,CAAC,CAAC,CAACsE,SAAS,CAAEY,MAAM,IAAI;MAC5F,IAAI,IAAI,CAACpE,IAAI,CAAC4B,UAAU,EAAE;QACtB,IAAI,CAACyC,0BAA0B,EAAE;;IAEzC,CAAC,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAAC3B,GAAG,GAAG,IAAI,CAAC4B,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,GAAG,GAAG,IAAI,CAACC,KAAK,GAAGC,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC;IAElF,IAAI,IAAI,CAACxE,IAAI,CAAC4B,UAAU,EAAE;MACtB,IAAI,CAACyC,0BAA0B,EAAE;;EAEzC;EAEAA,0BAA0BA,CAAA;IACtB,IAAIK,WAAW,GAAG,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,IAAI,CAAC3E,IAAI,CAAC4B,UAAU,CAAC,CAAC,CAAC,EAAE;MAC5DgD,KAAK,EAAE,OAAO;MACdvC,WAAW,EAAE,SAAS;MACtBwC,YAAY,EAAE,SAAS;MACvB7C,QAAQ,EAAE;KACb,CAAC;IAEF,IAAI0C,WAAW,EAAE;MACb,IAAI,CAACtB,WAAW,CAAC0B,iBAAiB,CAAC;QAAEnC,GAAG,EAAE,IAAI,CAACA,GAAG;QAAEkB,UAAU,EAAE;MAAI,CAAE,CAAC;;EAE/E;EAEAlD,SAASA,CAACwD,KAAY;IAClB;IACA,IAAI,IAAI,CAACnE,IAAI,CAAC+E,QAAQ,EAAE;MACpBZ,KAAK,CAACa,cAAc,EAAE;MACtB;;IAGJ;IACA,IAAI,IAAI,CAAChF,IAAI,CAACiF,OAAO,EAAE;MACnB,IAAI,CAACjF,IAAI,CAACiF,OAAO,CAAC;QAAEC,aAAa,EAAEf,KAAK;QAAEnE,IAAI,EAAE,IAAI,CAACA;MAAI,CAAE,CAAC;;IAGhE;IACA,IAAI,IAAI,CAACA,IAAI,CAACsB,KAAK,EAAE;MACjB,IAAI,CAAC+B,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;;IAG9B,IAAI,CAACD,WAAW,CAAC0B,iBAAiB,CAAC;MAAEnC,GAAG,EAAE,IAAI,CAACA;IAAG,CAAE,CAAC;EACzD;EAEA,IAAIG,gBAAgBA,CAAA;IAChB,OAAO,IAAI,CAACqC,IAAI,GAAG,UAAU,GAAG,IAAI,CAAC9B,MAAM,GAAG,UAAU,GAAG,WAAW;EAC1E;EAEA,IACI+B,WAAWA,CAAA;IACX,OAAO,IAAI,CAAC/B,MAAM,IAAI,CAAC,IAAI,CAAC8B,IAAI;EACpC;EAEAE,WAAWA,CAAA;IACP,IAAI,IAAI,CAAC/B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACgC,WAAW,EAAE;;IAG7C,IAAI,IAAI,CAACvB,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAACuB,WAAW,EAAE;;EAEhD;EAAC,QAAAC,CAAA,G;qBAxGQxC,oBAAoB,EAAAtD,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAjG,EAAA,CAAA+F,iBAAA,CAAA/F,EAAA,CAAAkG,iBAAA,GAAAlG,EAAA,CAAA+F,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAApG,EAAA,CAAA+F,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBjD,oBAAoB;IAAAkD,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QApEzB5G,EAAA,CAAA8G,uBAAA,GAAc;QACV9G,EAAA,CAAAmB,UAAA,IAAA4F,mCAAA,iBAAoG,IAAAC,iCAAA,mBAAAC,iCAAA,oBAAAC,kCAAA;QA+CxGlH,EAAA,CAAAmH,qBAAA,EAAe;;;QA/CLnH,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAAnB,IAAA,IAAAmB,GAAA,CAAAtG,IAAA,CAAA6G,OAAA,WAAoC;QAErCpH,EAAA,CAAAI,SAAA,GAAgE;QAAhEJ,EAAA,CAAAqB,UAAA,WAAAwF,GAAA,CAAAtG,IAAA,CAAA4B,UAAA,IAAA0E,GAAA,CAAAtG,IAAA,CAAAsB,KAAA,KAAAgF,GAAA,CAAAtG,IAAA,CAAA6G,OAAA,WAAgE;QAYhEpH,EAAA,CAAAI,SAAA,GAA8D;QAA9DJ,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAAtG,IAAA,CAAA4B,UAAA,KAAA0E,GAAA,CAAAtG,IAAA,CAAAsB,KAAA,IAAAgF,GAAA,CAAAtG,IAAA,CAAA6G,OAAA,WAA8D;QA4B9DpH,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAAtG,IAAA,CAAAsB,KAAA,IAAAgF,GAAA,CAAAtG,IAAA,CAAA6G,OAAA,WAA0C;;;mGAyB9C9D,oBAAoB;IAAA+D,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAlBjB,CACRzH,OAAO,CAAC,UAAU,EAAE,CAChBH,KAAK,CACD,WAAW,EACXC,KAAK,CAAC;QACF4H,MAAM,EAAE;OACX,CAAC,CACL,EACD7H,KAAK,CACD,UAAU,EACVC,KAAK,CAAC;QACF4H,MAAM,EAAE;OACX,CAAC,CACL,EACD3H,UAAU,CAAC,wBAAwB,EAAEH,OAAO,CAAC,sCAAsC,CAAC,CAAC,CACxF,CAAC;IACL;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}