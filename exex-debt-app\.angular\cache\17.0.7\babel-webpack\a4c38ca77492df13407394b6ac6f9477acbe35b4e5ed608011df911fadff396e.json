{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * Skeleton is a placeholder to display instead of the actual content.\n * @group Components\n */\nclass Skeleton {\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Shape of the element.\n   * @group Props\n   */\n  shape = 'rectangle';\n  /**\n   * Type of the animation.\n   * @gruop Props\n   */\n  animation = 'wave';\n  /**\n   * Border radius of the element, defaults to value from theme.\n   * @group Props\n   */\n  borderRadius;\n  /**\n   * Size of the Circle or Square.\n   * @group Props\n   */\n  size;\n  /**\n   * Width of the element.\n   * @group Props\n   */\n  width = '100%';\n  /**\n   * Height of the element.\n   * @group Props\n   */\n  height = '1rem';\n  containerClass() {\n    return {\n      'p-skeleton p-component': true,\n      'p-skeleton-circle': this.shape === 'circle',\n      'p-skeleton-none': this.animation === 'none'\n    };\n  }\n  containerStyle() {\n    if (this.size) return {\n      ...this.style,\n      width: this.size,\n      height: this.size,\n      borderRadius: this.borderRadius\n    };else return {\n      ...this.style,\n      width: this.width,\n      height: this.height,\n      borderRadius: this.borderRadius\n    };\n  }\n  static ɵfac = function Skeleton_Factory(t) {\n    return new (t || Skeleton)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Skeleton,\n    selectors: [[\"p-skeleton\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      styleClass: \"styleClass\",\n      style: \"style\",\n      shape: \"shape\",\n      animation: \"animation\",\n      borderRadius: \"borderRadius\",\n      size: \"size\",\n      width: \"width\",\n      height: \"height\"\n    },\n    decls: 1,\n    vars: 7,\n    consts: [[3, \"ngClass\", \"ngStyle\"]],\n    template: function Skeleton_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.containerStyle());\n        i0.ɵɵattribute(\"data-pc-name\", \"skeleton\")(\"aria-hidden\", true)(\"data-pc-section\", \"root\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Skeleton, [{\n    type: Component,\n    args: [{\n      selector: 'p-skeleton',\n      template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    borderRadius: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }]\n  });\n})();\nclass SkeletonModule {\n  static ɵfac = function SkeletonModule_Factory(t) {\n    return new (t || SkeletonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SkeletonModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Skeleton],\n      declarations: [Skeleton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Skeleton, SkeletonModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "Skeleton", "styleClass", "style", "shape", "animation", "borderRadius", "size", "width", "height", "containerClass", "containerStyle", "ɵfac", "Skeleton_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "decls", "vars", "consts", "template", "Skeleton_Template", "rf", "ctx", "ɵɵelement", "ɵɵclassMap", "ɵɵproperty", "ɵɵattribute", "dependencies", "Ng<PERSON><PERSON>", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "SkeletonModule", "SkeletonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/primeng/fesm2022/primeng-skeleton.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * Skeleton is a placeholder to display instead of the actual content.\n * @group Components\n */\nclass Skeleton {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Shape of the element.\n     * @group Props\n     */\n    shape = 'rectangle';\n    /**\n     * Type of the animation.\n     * @gruop Props\n     */\n    animation = 'wave';\n    /**\n     * Border radius of the element, defaults to value from theme.\n     * @group Props\n     */\n    borderRadius;\n    /**\n     * Size of the Circle or Square.\n     * @group Props\n     */\n    size;\n    /**\n     * Width of the element.\n     * @group Props\n     */\n    width = '100%';\n    /**\n     * Height of the element.\n     * @group Props\n     */\n    height = '1rem';\n    containerClass() {\n        return {\n            'p-skeleton p-component': true,\n            'p-skeleton-circle': this.shape === 'circle',\n            'p-skeleton-none': this.animation === 'none'\n        };\n    }\n    containerStyle() {\n        if (this.size)\n            return { ...this.style, width: this.size, height: this.size, borderRadius: this.borderRadius };\n        else\n            return { ...this.style, width: this.width, height: this.height, borderRadius: this.borderRadius };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Skeleton, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Skeleton, selector: \"p-skeleton\", inputs: { styleClass: \"styleClass\", style: \"style\", shape: \"shape\", animation: \"animation\", borderRadius: \"borderRadius\", size: \"size\", width: \"width\", height: \"height\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `, isInline: true, styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Skeleton, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-skeleton', template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"] }]\n        }], propDecorators: { styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], shape: [{\n                type: Input\n            }], animation: [{\n                type: Input\n            }], borderRadius: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }] } });\nclass SkeletonModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SkeletonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: SkeletonModule, declarations: [Skeleton], imports: [CommonModule], exports: [Skeleton] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SkeletonModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SkeletonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Skeleton],\n                    declarations: [Skeleton]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Skeleton, SkeletonModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;;AAEtG;AACA;AACA;AACA;AACA,MAAMC,QAAQ,CAAC;EACX;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,KAAK,GAAG,WAAW;EACnB;AACJ;AACA;AACA;EACIC,SAAS,GAAG,MAAM;EAClB;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,KAAK,GAAG,MAAM;EACd;AACJ;AACA;AACA;EACIC,MAAM,GAAG,MAAM;EACfC,cAAcA,CAAA,EAAG;IACb,OAAO;MACH,wBAAwB,EAAE,IAAI;MAC9B,mBAAmB,EAAE,IAAI,CAACN,KAAK,KAAK,QAAQ;MAC5C,iBAAiB,EAAE,IAAI,CAACC,SAAS,KAAK;IAC1C,CAAC;EACL;EACAM,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACJ,IAAI,EACT,OAAO;MAAE,GAAG,IAAI,CAACJ,KAAK;MAAEK,KAAK,EAAE,IAAI,CAACD,IAAI;MAAEE,MAAM,EAAE,IAAI,CAACF,IAAI;MAAED,YAAY,EAAE,IAAI,CAACA;IAAa,CAAC,CAAC,KAE/F,OAAO;MAAE,GAAG,IAAI,CAACH,KAAK;MAAEK,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEH,YAAY,EAAE,IAAI,CAACA;IAAa,CAAC;EACzG;EACA,OAAOM,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFb,QAAQ;EAAA;EAC3G,OAAOc,IAAI,kBAD8EpB,EAAE,CAAAqB,iBAAA;IAAAC,IAAA,EACJhB,QAAQ;IAAAiB,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAlB,UAAA;MAAAC,KAAA;MAAAC,KAAA;MAAAC,SAAA;MAAAC,YAAA;MAAAC,IAAA;MAAAC,KAAA;MAAAC,MAAA;IAAA;IAAAY,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADN/B,EAAE,CAAAiC,SAAA,YAC+b,CAAC;MAAA;MAAA,IAAAF,EAAA;QADlc/B,EAAE,CAAAkC,UAAA,CAAAF,GAAA,CAAAzB,UACgU,CAAC;QADnUP,EAAE,CAAAmC,UAAA,YAAAH,GAAA,CAAAjB,cAAA,EAC2S,CAAC,YAAAiB,GAAA,CAAAhB,cAAA,EAAD,CAAC;QAD9ShB,EAAE,CAAAoC,WAAA,2BAC8X,CAAC,oBAAD,CAAC,0BAAD,CAAC;MAAA;IAAA;IAAAC,YAAA,GAAmiBvC,EAAE,CAACwC,OAAO,EAAoFxC,EAAE,CAACyC,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACzmC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F3C,EAAE,CAAA4C,iBAAA,CAGJtC,QAAQ,EAAc,CAAC;IACtGgB,IAAI,EAAErB,SAAS;IACf4C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEjB,QAAQ,EAAG,yLAAwL;MAAEa,eAAe,EAAExC,uBAAuB,CAAC6C,MAAM;MAAEN,aAAa,EAAEtC,iBAAiB,CAAC6C,IAAI;MAAEC,IAAI,EAAE;QACxTC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,wZAAwZ;IAAE,CAAC;EACnb,CAAC,CAAC,QAAkB;IAAEjC,UAAU,EAAE,CAAC;MAC3Be,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEI,KAAK,EAAE,CAAC;MACRc,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEK,KAAK,EAAE,CAAC;MACRa,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEM,SAAS,EAAE,CAAC;MACZY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEO,YAAY,EAAE,CAAC;MACfW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEQ,IAAI,EAAE,CAAC;MACPU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAES,KAAK,EAAE,CAAC;MACRS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEU,MAAM,EAAE,CAAC;MACTQ,IAAI,EAAElB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM+C,cAAc,CAAC;EACjB,OAAOlC,IAAI,YAAAmC,uBAAAjC,CAAA;IAAA,YAAAA,CAAA,IAAwFgC,cAAc;EAAA;EACjH,OAAOE,IAAI,kBA3B8ErD,EAAE,CAAAsD,gBAAA;IAAAhC,IAAA,EA2BS6B;EAAc;EAClH,OAAOI,IAAI,kBA5B8EvD,EAAE,CAAAwD,gBAAA;IAAAC,OAAA,GA4BmC1D,YAAY;EAAA;AAC9I;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KA9B6F3C,EAAE,CAAA4C,iBAAA,CA8BJO,cAAc,EAAc,CAAC;IAC5G7B,IAAI,EAAEjB,QAAQ;IACdwC,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAAC1D,YAAY,CAAC;MACvB2D,OAAO,EAAE,CAACpD,QAAQ,CAAC;MACnBqD,YAAY,EAAE,CAACrD,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAE6C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}