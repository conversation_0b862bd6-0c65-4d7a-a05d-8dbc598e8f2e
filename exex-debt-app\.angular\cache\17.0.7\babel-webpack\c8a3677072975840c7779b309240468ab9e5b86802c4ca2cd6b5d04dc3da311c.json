{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MenuService {\n  constructor() {\n    this.menuSource = new Subject();\n    this.resetSource = new Subject();\n    this.menuSource$ = this.menuSource.asObservable();\n    this.resetSource$ = this.resetSource.asObservable();\n  }\n  onMenuStateChange(event) {\n    this.menuSource.next(event);\n  }\n  reset() {\n    this.resetSource.next(true);\n  }\n  static #_ = this.ɵfac = function MenuService_Factory(t) {\n    return new (t || MenuService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: MenuService,\n    factory: MenuService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Subject", "MenuService", "constructor", "menuSource", "resetSource", "menuSource$", "asObservable", "resetSource$", "onMenuStateChange", "event", "next", "reset", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\layout\\app.menu.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\n\ninterface MenuChangeEvent {\n    key: string;\n    routeEvent?: boolean;\n}\n\n@Injectable({\n    providedIn: 'root'\n})\nexport class MenuService {\n\n    private menuSource = new Subject<MenuChangeEvent>();\n    private resetSource = new Subject();\n\n    menuSource$ = this.menuSource.asObservable();\n    resetSource$ = this.resetSource.asObservable();\n\n    onMenuStateChange(event: MenuChangeEvent) {\n        this.menuSource.next(event);\n    }\n\n    reset() {\n        this.resetSource.next(true);\n    }\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;;AAU9B,OAAM,MAAOC,WAAW;EAHxBC,YAAA;IAKY,KAAAC,UAAU,GAAG,IAAIH,OAAO,EAAmB;IAC3C,KAAAI,WAAW,GAAG,IAAIJ,OAAO,EAAE;IAEnC,KAAAK,WAAW,GAAG,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;IAC5C,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;;EAE9CE,iBAAiBA,CAACC,KAAsB;IACpC,IAAI,CAACN,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;EAC/B;EAEAE,KAAKA,CAAA;IACD,IAAI,CAACP,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC;EAC/B;EAAC,QAAAE,CAAA,G;qBAdQX,WAAW;EAAA;EAAA,QAAAY,EAAA,G;WAAXZ,WAAW;IAAAa,OAAA,EAAXb,WAAW,CAAAc,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}