{"ast": null, "code": "/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n  static zindex = 1000;\n  static calculatedScrollbarWidth = null;\n  static calculatedScrollbarHeight = null;\n  static browser;\n  static addClass(element, className) {\n    if (element && className) {\n      if (element.classList) element.classList.add(className);else element.className += ' ' + className;\n    }\n  }\n  static addMultipleClasses(element, className) {\n    if (element && className) {\n      if (element.classList) {\n        let styles = className.trim().split(' ');\n        for (let i = 0; i < styles.length; i++) {\n          element.classList.add(styles[i]);\n        }\n      } else {\n        let styles = className.split(' ');\n        for (let i = 0; i < styles.length; i++) {\n          element.className += ' ' + styles[i];\n        }\n      }\n    }\n  }\n  static removeClass(element, className) {\n    if (element && className) {\n      if (element.classList) element.classList.remove(className);else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n    }\n  }\n  static removeMultipleClasses(element, classNames) {\n    if (element && classNames) {\n      [classNames].flat().filter(Boolean).forEach(cNames => cNames.split(' ').forEach(className => this.removeClass(element, className)));\n    }\n  }\n  static hasClass(element, className) {\n    if (element && className) {\n      if (element.classList) return element.classList.contains(className);else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n    }\n    return false;\n  }\n  static siblings(element) {\n    return Array.prototype.filter.call(element.parentNode.children, function (child) {\n      return child !== element;\n    });\n  }\n  static find(element, selector) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  static findSingle(element, selector) {\n    return this.isElement(element) ? element.querySelector(selector) : null;\n  }\n  static index(element) {\n    let children = element.parentNode.childNodes;\n    let num = 0;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].nodeType == 1) num++;\n    }\n    return -1;\n  }\n  static indexWithinGroup(element, attributeName) {\n    let children = element.parentNode ? element.parentNode.childNodes : [];\n    let num = 0;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n    }\n    return -1;\n  }\n  static appendOverlay(overlay, target, appendTo = 'self') {\n    if (appendTo !== 'self' && overlay && target) {\n      this.appendChild(overlay, target);\n    }\n  }\n  static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n    if (overlay && target) {\n      if (calculateMinWidth) {\n        overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n      }\n      if (appendTo === 'self') {\n        this.relativePosition(overlay, target);\n      } else {\n        this.absolutePosition(overlay, target);\n      }\n    }\n  }\n  static relativePosition(element, target) {\n    const getClosestRelativeElement = el => {\n      if (!el) return;\n      return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n    };\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    const targetHeight = target.offsetHeight;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = this.getWindowScrollTop();\n    const windowScrollLeft = this.getWindowScrollLeft();\n    const viewport = this.getViewport();\n    const relativeElement = getClosestRelativeElement(element);\n    const relativeElementOffset = relativeElement?.getBoundingClientRect() || {\n      top: -1 * windowScrollTop,\n      left: -1 * windowScrollLeft\n    };\n    let top, left;\n    if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n      top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n      element.style.transformOrigin = 'bottom';\n      if (targetOffset.top + top < 0) {\n        top = -1 * targetOffset.top;\n      }\n    } else {\n      top = targetHeight + targetOffset.top - relativeElementOffset.top;\n      element.style.transformOrigin = 'top';\n    }\n    const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n    const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n    if (elementDimensions.width > viewport.width) {\n      // element wider then viewport and cannot fit on screen (align at left side of viewport)\n      left = (targetOffset.left - relativeElementOffset.left) * -1;\n    } else if (horizontalOverflow > 0) {\n      // element wider then viewport but can be fit on screen (align at right side of viewport)\n      left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n    } else {\n      // element fits on screen (align with target)\n      left = targetOffset.left - relativeElementOffset.left;\n    }\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n  }\n  static absolutePosition(element, target) {\n    const elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    const elementOuterHeight = elementDimensions.height;\n    const elementOuterWidth = elementDimensions.width;\n    const targetOuterHeight = target.offsetHeight;\n    const targetOuterWidth = target.offsetWidth;\n    const targetOffset = target.getBoundingClientRect();\n    const windowScrollTop = this.getWindowScrollTop();\n    const windowScrollLeft = this.getWindowScrollLeft();\n    const viewport = this.getViewport();\n    let top, left;\n    if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n      top = targetOffset.top + windowScrollTop - elementOuterHeight;\n      element.style.transformOrigin = 'bottom';\n      if (top < 0) {\n        top = windowScrollTop;\n      }\n    } else {\n      top = targetOuterHeight + targetOffset.top + windowScrollTop;\n      element.style.transformOrigin = 'top';\n    }\n    if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n  }\n  static getParents(element, parents = []) {\n    return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n  }\n  static getScrollableParents(element) {\n    let scrollableParents = [];\n    if (element) {\n      let parents = this.getParents(element);\n      const overflowRegex = /(auto|scroll)/;\n      const overflowCheck = node => {\n        let styleDeclaration = window['getComputedStyle'](node, null);\n        return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n      };\n      for (let parent of parents) {\n        let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n        if (scrollSelectors) {\n          let selectors = scrollSelectors.split(',');\n          for (let selector of selectors) {\n            let el = this.findSingle(parent, selector);\n            if (el && overflowCheck(el)) {\n              scrollableParents.push(el);\n            }\n          }\n        }\n        if (parent.nodeType !== 9 && overflowCheck(parent)) {\n          scrollableParents.push(parent);\n        }\n      }\n    }\n    return scrollableParents;\n  }\n  static getHiddenElementOuterHeight(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementHeight = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementHeight;\n  }\n  static getHiddenElementOuterWidth(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementWidth = element.offsetWidth;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementWidth;\n  }\n  static getHiddenElementDimensions(element) {\n    let dimensions = {};\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    dimensions.width = element.offsetWidth;\n    dimensions.height = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return dimensions;\n  }\n  static scrollInView(container, item) {\n    let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n    let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n    let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n    let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n    let containerRect = container.getBoundingClientRect();\n    let itemRect = item.getBoundingClientRect();\n    let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n    let scroll = container.scrollTop;\n    let elementHeight = container.clientHeight;\n    let itemHeight = this.getOuterHeight(item);\n    if (offset < 0) {\n      container.scrollTop = scroll + offset;\n    } else if (offset + itemHeight > elementHeight) {\n      container.scrollTop = scroll + offset - elementHeight + itemHeight;\n    }\n  }\n  static fadeIn(element, duration) {\n    element.style.opacity = 0;\n    let last = +new Date();\n    let opacity = 0;\n    let tick = function () {\n      opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n      element.style.opacity = opacity;\n      last = +new Date();\n      if (+opacity < 1) {\n        window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n      }\n    };\n    tick();\n  }\n  static fadeOut(element, ms) {\n    var opacity = 1,\n      interval = 50,\n      duration = ms,\n      gap = interval / duration;\n    let fading = setInterval(() => {\n      opacity = opacity - gap;\n      if (opacity <= 0) {\n        opacity = 0;\n        clearInterval(fading);\n      }\n      element.style.opacity = opacity;\n    }, interval);\n  }\n  static getWindowScrollTop() {\n    let doc = document.documentElement;\n    return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n  }\n  static getWindowScrollLeft() {\n    let doc = document.documentElement;\n    return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n  }\n  static matches(element, selector) {\n    var p = Element.prototype;\n    var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n      return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n    };\n    return f.call(element, selector);\n  }\n  static getOuterWidth(el, margin) {\n    let width = el.offsetWidth;\n    if (margin) {\n      let style = getComputedStyle(el);\n      width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    return width;\n  }\n  static getHorizontalPadding(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n  }\n  static getHorizontalMargin(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n  }\n  static innerWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n  static width(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n  static getInnerHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n    return height;\n  }\n  static getOuterHeight(el, margin) {\n    let height = el.offsetHeight;\n    if (margin) {\n      let style = getComputedStyle(el);\n      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    }\n    return height;\n  }\n  static getHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n    return height;\n  }\n  static getWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n    return width;\n  }\n  static getViewport() {\n    let win = window,\n      d = document,\n      e = d.documentElement,\n      g = d.getElementsByTagName('body')[0],\n      w = win.innerWidth || e.clientWidth || g.clientWidth,\n      h = win.innerHeight || e.clientHeight || g.clientHeight;\n    return {\n      width: w,\n      height: h\n    };\n  }\n  static getOffset(el) {\n    var rect = el.getBoundingClientRect();\n    return {\n      top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n      left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n    };\n  }\n  static replaceElementWith(element, replacementElement) {\n    let parentNode = element.parentNode;\n    if (!parentNode) throw `Can't replace element`;\n    return parentNode.replaceChild(replacementElement, element);\n  }\n  static getUserAgent() {\n    if (navigator && this.isClient()) {\n      return navigator.userAgent;\n    }\n  }\n  static isIE() {\n    var ua = window.navigator.userAgent;\n    var msie = ua.indexOf('MSIE ');\n    if (msie > 0) {\n      // IE 10 or older => return version number\n      return true;\n    }\n    var trident = ua.indexOf('Trident/');\n    if (trident > 0) {\n      // IE 11 => return version number\n      var rv = ua.indexOf('rv:');\n      return true;\n    }\n    var edge = ua.indexOf('Edge/');\n    if (edge > 0) {\n      // Edge (IE 12+) => return version number\n      return true;\n    }\n    // other browser\n    return false;\n  }\n  static isIOS() {\n    return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n  }\n  static isAndroid() {\n    return /(android)/i.test(navigator.userAgent);\n  }\n  static isTouchDevice() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n  static appendChild(element, target) {\n    if (this.isElement(target)) target.appendChild(element);else if (target && target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);else throw 'Cannot append ' + target + ' to ' + element;\n  }\n  static removeChild(element, target) {\n    if (this.isElement(target)) target.removeChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);else throw 'Cannot remove ' + element + ' from ' + target;\n  }\n  static removeElement(element) {\n    if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);else element.remove();\n  }\n  static isElement(obj) {\n    return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n  }\n  static calculateScrollbarWidth(el) {\n    if (el) {\n      let style = getComputedStyle(el);\n      return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n    } else {\n      if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n      let scrollDiv = document.createElement('div');\n      scrollDiv.className = 'p-scrollbar-measure';\n      document.body.appendChild(scrollDiv);\n      let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n      this.calculatedScrollbarWidth = scrollbarWidth;\n      return scrollbarWidth;\n    }\n  }\n  static calculateScrollbarHeight() {\n    if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n    let scrollDiv = document.createElement('div');\n    scrollDiv.className = 'p-scrollbar-measure';\n    document.body.appendChild(scrollDiv);\n    let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    document.body.removeChild(scrollDiv);\n    this.calculatedScrollbarWidth = scrollbarHeight;\n    return scrollbarHeight;\n  }\n  static invokeElementMethod(element, methodName, args) {\n    element[methodName].apply(element, args);\n  }\n  static clearSelection() {\n    if (window.getSelection) {\n      if (window.getSelection().empty) {\n        window.getSelection().empty();\n      } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n        window.getSelection().removeAllRanges();\n      }\n    } else if (document['selection'] && document['selection'].empty) {\n      try {\n        document['selection'].empty();\n      } catch (error) {\n        //ignore IE bug\n      }\n    }\n  }\n  static getBrowser() {\n    if (!this.browser) {\n      let matched = this.resolveUserAgent();\n      this.browser = {};\n      if (matched.browser) {\n        this.browser[matched.browser] = true;\n        this.browser['version'] = matched.version;\n      }\n      if (this.browser['chrome']) {\n        this.browser['webkit'] = true;\n      } else if (this.browser['webkit']) {\n        this.browser['safari'] = true;\n      }\n    }\n    return this.browser;\n  }\n  static resolveUserAgent() {\n    let ua = navigator.userAgent.toLowerCase();\n    let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n    return {\n      browser: match[1] || '',\n      version: match[2] || '0'\n    };\n  }\n  static isInteger(value) {\n    if (Number.isInteger) {\n      return Number.isInteger(value);\n    } else {\n      return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n    }\n  }\n  static isHidden(element) {\n    return !element || element.offsetParent === null;\n  }\n  static isVisible(element) {\n    return element && element.offsetParent != null;\n  }\n  static isExist(element) {\n    return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n  }\n  static focus(element, options) {\n    element && document.activeElement !== element && element.focus(options);\n  }\n  static getFocusableElements(element, selector = '') {\n    let focusableElements = this.find(element, `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`);\n    let visibleFocusableElements = [];\n    for (let focusableElement of focusableElements) {\n      if (getComputedStyle(focusableElement).display != 'none' && getComputedStyle(focusableElement).visibility != 'hidden') visibleFocusableElements.push(focusableElement);\n    }\n    return visibleFocusableElements;\n  }\n  static getFirstFocusableElement(element, selector) {\n    const focusableElements = this.getFocusableElements(element, selector);\n    return focusableElements.length > 0 ? focusableElements[0] : null;\n  }\n  static getLastFocusableElement(element, selector) {\n    const focusableElements = this.getFocusableElements(element, selector);\n    return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n  }\n  static getNextFocusableElement(element, reverse = false) {\n    const focusableElements = DomHandler.getFocusableElements(element);\n    let index = 0;\n    if (focusableElements && focusableElements.length > 0) {\n      const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n      if (reverse) {\n        if (focusedIndex == -1 || focusedIndex === 0) {\n          index = focusableElements.length - 1;\n        } else {\n          index = focusedIndex - 1;\n        }\n      } else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n        index = focusedIndex + 1;\n      }\n    }\n    return focusableElements[index];\n  }\n  static generateZIndex() {\n    this.zindex = this.zindex || 999;\n    return ++this.zindex;\n  }\n  static getSelection() {\n    if (window.getSelection) return window.getSelection().toString();else if (document.getSelection) return document.getSelection().toString();else if (document['selection']) return document['selection'].createRange().text;\n    return null;\n  }\n  static getTargetElement(target, el) {\n    if (!target) return null;\n    switch (target) {\n      case 'document':\n        return document;\n      case 'window':\n        return window;\n      case '@next':\n        return el?.nextElementSibling;\n      case '@prev':\n        return el?.previousElementSibling;\n      case '@parent':\n        return el?.parentElement;\n      case '@grandparent':\n        return el?.parentElement.parentElement;\n      default:\n        const type = typeof target;\n        if (type === 'string') {\n          return document.querySelector(target);\n        } else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n          return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n        }\n        const isFunction = obj => !!(obj && obj.constructor && obj.call && obj.apply);\n        const element = isFunction(target) ? target() : target;\n        return element && element.nodeType === 9 || this.isExist(element) ? element : null;\n    }\n  }\n  static isClient() {\n    return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n  }\n  static getAttribute(element, name) {\n    if (element) {\n      const value = element.getAttribute(name);\n      if (!isNaN(value)) {\n        return +value;\n      }\n      if (value === 'true' || value === 'false') {\n        return value === 'true';\n      }\n      return value;\n    }\n    return undefined;\n  }\n  static calculateBodyScrollbarWidth() {\n    return window.innerWidth - document.documentElement.offsetWidth;\n  }\n  static blockBodyScroll(className = 'p-overflow-hidden') {\n    document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n    this.addClass(document.body, className);\n  }\n  static unblockBodyScroll(className = 'p-overflow-hidden') {\n    document.body.style.removeProperty('--scrollbar-width');\n    this.removeClass(document.body, className);\n  }\n}\nclass ConnectedOverlayScrollHandler {\n  element;\n  listener;\n  scrollableParents;\n  constructor(element, listener = () => {}) {\n    this.element = element;\n    this.listener = listener;\n  }\n  bindScrollListener() {\n    this.scrollableParents = DomHandler.getScrollableParents(this.element);\n    for (let i = 0; i < this.scrollableParents.length; i++) {\n      this.scrollableParents[i].addEventListener('scroll', this.listener);\n    }\n  }\n  unbindScrollListener() {\n    if (this.scrollableParents) {\n      for (let i = 0; i < this.scrollableParents.length; i++) {\n        this.scrollableParents[i].removeEventListener('scroll', this.listener);\n      }\n    }\n  }\n  destroy() {\n    this.unbindScrollListener();\n    this.element = null;\n    this.listener = null;\n    this.scrollableParents = null;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "zindex", "calculatedScrollbarWidth", "calculatedScrollbarHeight", "browser", "addClass", "element", "className", "classList", "add", "addMultipleClasses", "styles", "trim", "split", "i", "length", "removeClass", "remove", "replace", "RegExp", "join", "removeMultipleClasses", "classNames", "flat", "filter", "Boolean", "for<PERSON>ach", "cNames", "hasClass", "contains", "test", "siblings", "Array", "prototype", "call", "parentNode", "children", "child", "find", "selector", "from", "querySelectorAll", "findSingle", "isElement", "querySelector", "index", "childNodes", "num", "nodeType", "indexWithinGroup", "attributeName", "attributes", "appendOverlay", "overlay", "target", "appendTo", "append<PERSON><PERSON><PERSON>", "alignOverlay", "calculateMinWidth", "style", "min<PERSON><PERSON><PERSON>", "getOuterWidth", "relativePosition", "absolutePosition", "getClosestRelativeElement", "el", "getComputedStyle", "getPropertyValue", "parentElement", "elementDimensions", "offsetParent", "width", "offsetWidth", "height", "offsetHeight", "getHiddenElementDimensions", "targetHeight", "targetOffset", "getBoundingClientRect", "windowScrollTop", "getWindowScrollTop", "windowScrollLeft", "getWindowScrollLeft", "viewport", "getViewport", "relativeElement", "relativeElementOffset", "top", "left", "transform<PERSON><PERSON>in", "horizontalOverflow", "targetLeftOffsetInSpaceOfRelativeElement", "elementOuterHeight", "elementOuterWidth", "targetOuterHeight", "targetOuterWidth", "Math", "max", "getParents", "parents", "concat", "getScrollableParents", "scrollableParents", "overflowRegex", "overflowCheck", "node", "styleDeclaration", "window", "parent", "scrollSelectors", "dataset", "selectors", "push", "getHiddenElementOuterHeight", "visibility", "display", "elementHeight", "getHiddenElementOuterWidth", "elementWidth", "dimensions", "scrollInView", "container", "item", "borderTopValue", "borderTop", "parseFloat", "paddingTopValue", "paddingTop", "containerRect", "itemRect", "offset", "document", "body", "scrollTop", "scroll", "clientHeight", "itemHeight", "getOuterHeight", "fadeIn", "duration", "opacity", "last", "Date", "tick", "getTime", "requestAnimationFrame", "setTimeout", "fadeOut", "ms", "interval", "gap", "fading", "setInterval", "clearInterval", "doc", "documentElement", "pageYOffset", "clientTop", "pageXOffset", "scrollLeft", "clientLeft", "matches", "p", "Element", "f", "webkitMatchesSelector", "s", "indexOf", "margin", "marginLeft", "marginRight", "getHorizontalPadding", "paddingLeft", "paddingRight", "getHorizontalMargin", "innerWidth", "getInnerHeight", "paddingBottom", "marginTop", "marginBottom", "getHeight", "borderTopWidth", "borderBottomWidth", "getWidth", "borderLeftWidth", "borderRightWidth", "win", "d", "e", "g", "getElementsByTagName", "w", "clientWidth", "h", "innerHeight", "getOffset", "rect", "replaceElementWith", "replacementElement", "<PERSON><PERSON><PERSON><PERSON>", "getUserAgent", "navigator", "isClient", "userAgent", "isIE", "ua", "msie", "trident", "rv", "edge", "isIOS", "isAndroid", "isTouchDevice", "maxTouchPoints", "nativeElement", "<PERSON><PERSON><PERSON><PERSON>", "removeElement", "obj", "HTMLElement", "nodeName", "calculateScrollbarWidth", "scrollDiv", "createElement", "scrollbarWidth", "calculateScrollbarHeight", "scrollbarHeight", "invokeElementMethod", "methodName", "args", "apply", "clearSelection", "getSelection", "empty", "removeAllRanges", "rangeCount", "getRangeAt", "getClientRects", "error", "<PERSON><PERSON><PERSON><PERSON>", "matched", "resolveUserAgent", "version", "toLowerCase", "match", "exec", "isInteger", "value", "Number", "isFinite", "floor", "isHidden", "isVisible", "isExist", "focus", "options", "activeElement", "getFocusableElements", "focusableElements", "visibleFocusableElements", "focusableElement", "getFirstFocusableElement", "getLastFocusableElement", "getNextFocusableElement", "reverse", "focusedIndex", "ownerDocument", "generateZIndex", "toString", "createRange", "text", "getTargetElement", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "type", "hasOwnProperty", "undefined", "isFunction", "constructor", "getAttribute", "name", "isNaN", "calculateBodyScrollbarWidth", "blockBodyScroll", "setProperty", "unblockBodyScroll", "removeProperty", "ConnectedOverlayScrollHandler", "listener", "bindScrollListener", "addEventListener", "unbindScrollListener", "removeEventListener", "destroy"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/primeng/fesm2022/primeng-dom.mjs"], "sourcesContent": ["/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n    static zindex = 1000;\n    static calculatedScrollbarWidth = null;\n    static calculatedScrollbarHeight = null;\n    static browser;\n    static addClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.add(className);\n            else\n                element.className += ' ' + className;\n        }\n    }\n    static addMultipleClasses(element, className) {\n        if (element && className) {\n            if (element.classList) {\n                let styles = className.trim().split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.classList.add(styles[i]);\n                }\n            }\n            else {\n                let styles = className.split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.className += ' ' + styles[i];\n                }\n            }\n        }\n    }\n    static removeClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.remove(className);\n            else\n                element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n        }\n    }\n    static removeMultipleClasses(element, classNames) {\n        if (element && classNames) {\n            [classNames]\n                .flat()\n                .filter(Boolean)\n                .forEach((cNames) => cNames.split(' ').forEach((className) => this.removeClass(element, className)));\n        }\n    }\n    static hasClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                return element.classList.contains(className);\n            else\n                return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n        }\n        return false;\n    }\n    static siblings(element) {\n        return Array.prototype.filter.call(element.parentNode.children, function (child) {\n            return child !== element;\n        });\n    }\n    static find(element, selector) {\n        return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n        return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n    static index(element) {\n        let children = element.parentNode.childNodes;\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n        let children = element.parentNode ? element.parentNode.childNodes : [];\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static appendOverlay(overlay, target, appendTo = 'self') {\n        if (appendTo !== 'self' && overlay && target) {\n            this.appendChild(overlay, target);\n        }\n    }\n    static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n        if (overlay && target) {\n            if (calculateMinWidth) {\n                overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n            }\n            if (appendTo === 'self') {\n                this.relativePosition(overlay, target);\n            }\n            else {\n                this.absolutePosition(overlay, target);\n            }\n        }\n    }\n    static relativePosition(element, target) {\n        const getClosestRelativeElement = (el) => {\n            if (!el)\n                return;\n            return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n        };\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const targetHeight = target.offsetHeight;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        const relativeElement = getClosestRelativeElement(element);\n        const relativeElementOffset = relativeElement?.getBoundingClientRect() || { top: -1 * windowScrollTop, left: -1 * windowScrollLeft };\n        let top, left;\n        if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n            top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n            element.style.transformOrigin = 'bottom';\n            if (targetOffset.top + top < 0) {\n                top = -1 * targetOffset.top;\n            }\n        }\n        else {\n            top = targetHeight + targetOffset.top - relativeElementOffset.top;\n            element.style.transformOrigin = 'top';\n        }\n        const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n        const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n        if (elementDimensions.width > viewport.width) {\n            // element wider then viewport and cannot fit on screen (align at left side of viewport)\n            left = (targetOffset.left - relativeElementOffset.left) * -1;\n        }\n        else if (horizontalOverflow > 0) {\n            // element wider then viewport but can be fit on screen (align at right side of viewport)\n            left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n        }\n        else {\n            // element fits on screen (align with target)\n            left = targetOffset.left - relativeElementOffset.left;\n        }\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n    }\n    static absolutePosition(element, target) {\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const elementOuterHeight = elementDimensions.height;\n        const elementOuterWidth = elementDimensions.width;\n        const targetOuterHeight = target.offsetHeight;\n        const targetOuterWidth = target.offsetWidth;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        let top, left;\n        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n            top = targetOffset.top + windowScrollTop - elementOuterHeight;\n            element.style.transformOrigin = 'bottom';\n            if (top < 0) {\n                top = windowScrollTop;\n            }\n        }\n        else {\n            top = targetOuterHeight + targetOffset.top + windowScrollTop;\n            element.style.transformOrigin = 'top';\n        }\n        if (targetOffset.left + elementOuterWidth > viewport.width)\n            left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n        else\n            left = targetOffset.left + windowScrollLeft;\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n    }\n    static getParents(element, parents = []) {\n        return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n        let scrollableParents = [];\n        if (element) {\n            let parents = this.getParents(element);\n            const overflowRegex = /(auto|scroll)/;\n            const overflowCheck = (node) => {\n                let styleDeclaration = window['getComputedStyle'](node, null);\n                return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n            };\n            for (let parent of parents) {\n                let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n                if (scrollSelectors) {\n                    let selectors = scrollSelectors.split(',');\n                    for (let selector of selectors) {\n                        let el = this.findSingle(parent, selector);\n                        if (el && overflowCheck(el)) {\n                            scrollableParents.push(el);\n                        }\n                    }\n                }\n                if (parent.nodeType !== 9 && overflowCheck(parent)) {\n                    scrollableParents.push(parent);\n                }\n            }\n        }\n        return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementHeight = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementWidth = element.offsetWidth;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n        let dimensions = {};\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        dimensions.width = element.offsetWidth;\n        dimensions.height = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return dimensions;\n    }\n    static scrollInView(container, item) {\n        let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n        let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n        let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n        let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n        let scroll = container.scrollTop;\n        let elementHeight = container.clientHeight;\n        let itemHeight = this.getOuterHeight(item);\n        if (offset < 0) {\n            container.scrollTop = scroll + offset;\n        }\n        else if (offset + itemHeight > elementHeight) {\n            container.scrollTop = scroll + offset - elementHeight + itemHeight;\n        }\n    }\n    static fadeIn(element, duration) {\n        element.style.opacity = 0;\n        let last = +new Date();\n        let opacity = 0;\n        let tick = function () {\n            opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n            element.style.opacity = opacity;\n            last = +new Date();\n            if (+opacity < 1) {\n                (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16);\n            }\n        };\n        tick();\n    }\n    static fadeOut(element, ms) {\n        var opacity = 1, interval = 50, duration = ms, gap = interval / duration;\n        let fading = setInterval(() => {\n            opacity = opacity - gap;\n            if (opacity <= 0) {\n                opacity = 0;\n                clearInterval(fading);\n            }\n            element.style.opacity = opacity;\n        }, interval);\n    }\n    static getWindowScrollTop() {\n        let doc = document.documentElement;\n        return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n        let doc = document.documentElement;\n        return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n        var p = Element.prototype;\n        var f = p['matches'] ||\n            p.webkitMatchesSelector ||\n            p['mozMatchesSelector'] ||\n            p['msMatchesSelector'] ||\n            function (s) {\n                return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n            };\n        return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n        let width = el.offsetWidth;\n        if (margin) {\n            let style = getComputedStyle(el);\n            width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n        }\n        return width;\n    }\n    static getHorizontalPadding(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static width(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static getInnerHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n        return height;\n    }\n    static getOuterHeight(el, margin) {\n        let height = el.offsetHeight;\n        if (margin) {\n            let style = getComputedStyle(el);\n            height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n        }\n        return height;\n    }\n    static getHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n        return height;\n    }\n    static getWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n        return width;\n    }\n    static getViewport() {\n        let win = window, d = document, e = d.documentElement, g = d.getElementsByTagName('body')[0], w = win.innerWidth || e.clientWidth || g.clientWidth, h = win.innerHeight || e.clientHeight || g.clientHeight;\n        return { width: w, height: h };\n    }\n    static getOffset(el) {\n        var rect = el.getBoundingClientRect();\n        return {\n            top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n            left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n        };\n    }\n    static replaceElementWith(element, replacementElement) {\n        let parentNode = element.parentNode;\n        if (!parentNode)\n            throw `Can't replace element`;\n        return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n        if (navigator && this.isClient()) {\n            return navigator.userAgent;\n        }\n    }\n    static isIE() {\n        var ua = window.navigator.userAgent;\n        var msie = ua.indexOf('MSIE ');\n        if (msie > 0) {\n            // IE 10 or older => return version number\n            return true;\n        }\n        var trident = ua.indexOf('Trident/');\n        if (trident > 0) {\n            // IE 11 => return version number\n            var rv = ua.indexOf('rv:');\n            return true;\n        }\n        var edge = ua.indexOf('Edge/');\n        if (edge > 0) {\n            // Edge (IE 12+) => return version number\n            return true;\n        }\n        // other browser\n        return false;\n    }\n    static isIOS() {\n        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n        return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    static appendChild(element, target) {\n        if (this.isElement(target))\n            target.appendChild(element);\n        else if (target && target.el && target.el.nativeElement)\n            target.el.nativeElement.appendChild(element);\n        else\n            throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n        if (this.isElement(target))\n            target.removeChild(element);\n        else if (target.el && target.el.nativeElement)\n            target.el.nativeElement.removeChild(element);\n        else\n            throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n        if (!('remove' in Element.prototype))\n            element.parentNode.removeChild(element);\n        else\n            element.remove();\n    }\n    static isElement(obj) {\n        return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n    static calculateScrollbarWidth(el) {\n        if (el) {\n            let style = getComputedStyle(el);\n            return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n        }\n        else {\n            if (this.calculatedScrollbarWidth !== null)\n                return this.calculatedScrollbarWidth;\n            let scrollDiv = document.createElement('div');\n            scrollDiv.className = 'p-scrollbar-measure';\n            document.body.appendChild(scrollDiv);\n            let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n            document.body.removeChild(scrollDiv);\n            this.calculatedScrollbarWidth = scrollbarWidth;\n            return scrollbarWidth;\n        }\n    }\n    static calculateScrollbarHeight() {\n        if (this.calculatedScrollbarHeight !== null)\n            return this.calculatedScrollbarHeight;\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n        let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarHeight;\n        return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n        element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n        if (window.getSelection) {\n            if (window.getSelection().empty) {\n                window.getSelection().empty();\n            }\n            else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n                window.getSelection().removeAllRanges();\n            }\n        }\n        else if (document['selection'] && document['selection'].empty) {\n            try {\n                document['selection'].empty();\n            }\n            catch (error) {\n                //ignore IE bug\n            }\n        }\n    }\n    static getBrowser() {\n        if (!this.browser) {\n            let matched = this.resolveUserAgent();\n            this.browser = {};\n            if (matched.browser) {\n                this.browser[matched.browser] = true;\n                this.browser['version'] = matched.version;\n            }\n            if (this.browser['chrome']) {\n                this.browser['webkit'] = true;\n            }\n            else if (this.browser['webkit']) {\n                this.browser['safari'] = true;\n            }\n        }\n        return this.browser;\n    }\n    static resolveUserAgent() {\n        let ua = navigator.userAgent.toLowerCase();\n        let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || (ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua)) || [];\n        return {\n            browser: match[1] || '',\n            version: match[2] || '0'\n        };\n    }\n    static isInteger(value) {\n        if (Number.isInteger) {\n            return Number.isInteger(value);\n        }\n        else {\n            return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n        }\n    }\n    static isHidden(element) {\n        return !element || element.offsetParent === null;\n    }\n    static isVisible(element) {\n        return element && element.offsetParent != null;\n    }\n    static isExist(element) {\n        return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n    static focus(element, options) {\n        element && document.activeElement !== element && element.focus(options);\n    }\n    static getFocusableElements(element, selector = '') {\n        let focusableElements = this.find(element, `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`);\n        let visibleFocusableElements = [];\n        for (let focusableElement of focusableElements) {\n            if (getComputedStyle(focusableElement).display != 'none' && getComputedStyle(focusableElement).visibility != 'hidden')\n                visibleFocusableElements.push(focusableElement);\n        }\n        return visibleFocusableElements;\n    }\n    static getFirstFocusableElement(element, selector) {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n    static getLastFocusableElement(element, selector) {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n    static getNextFocusableElement(element, reverse = false) {\n        const focusableElements = DomHandler.getFocusableElements(element);\n        let index = 0;\n        if (focusableElements && focusableElements.length > 0) {\n            const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n            if (reverse) {\n                if (focusedIndex == -1 || focusedIndex === 0) {\n                    index = focusableElements.length - 1;\n                }\n                else {\n                    index = focusedIndex - 1;\n                }\n            }\n            else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n                index = focusedIndex + 1;\n            }\n        }\n        return focusableElements[index];\n    }\n    static generateZIndex() {\n        this.zindex = this.zindex || 999;\n        return ++this.zindex;\n    }\n    static getSelection() {\n        if (window.getSelection)\n            return window.getSelection().toString();\n        else if (document.getSelection)\n            return document.getSelection().toString();\n        else if (document['selection'])\n            return document['selection'].createRange().text;\n        return null;\n    }\n    static getTargetElement(target, el) {\n        if (!target)\n            return null;\n        switch (target) {\n            case 'document':\n                return document;\n            case 'window':\n                return window;\n            case '@next':\n                return el?.nextElementSibling;\n            case '@prev':\n                return el?.previousElementSibling;\n            case '@parent':\n                return el?.parentElement;\n            case '@grandparent':\n                return el?.parentElement.parentElement;\n            default:\n                const type = typeof target;\n                if (type === 'string') {\n                    return document.querySelector(target);\n                }\n                else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n                    return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n                }\n                const isFunction = (obj) => !!(obj && obj.constructor && obj.call && obj.apply);\n                const element = isFunction(target) ? target() : target;\n                return (element && element.nodeType === 9) || this.isExist(element) ? element : null;\n        }\n    }\n    static isClient() {\n        return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n    static getAttribute(element, name) {\n        if (element) {\n            const value = element.getAttribute(name);\n            if (!isNaN(value)) {\n                return +value;\n            }\n            if (value === 'true' || value === 'false') {\n                return value === 'true';\n            }\n            return value;\n        }\n        return undefined;\n    }\n    static calculateBodyScrollbarWidth() {\n        return window.innerWidth - document.documentElement.offsetWidth;\n    }\n    static blockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n        this.addClass(document.body, className);\n    }\n    static unblockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.removeProperty('--scrollbar-width');\n        this.removeClass(document.body, className);\n    }\n}\n\nclass ConnectedOverlayScrollHandler {\n    element;\n    listener;\n    scrollableParents;\n    constructor(element, listener = () => { }) {\n        this.element = element;\n        this.listener = listener;\n    }\n    bindScrollListener() {\n        this.scrollableParents = DomHandler.getScrollableParents(this.element);\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,UAAU,CAAC;EACb,OAAOC,MAAM,GAAG,IAAI;EACpB,OAAOC,wBAAwB,GAAG,IAAI;EACtC,OAAOC,yBAAyB,GAAG,IAAI;EACvC,OAAOC,OAAO;EACd,OAAOC,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;IAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC,KAEjCD,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGA,SAAS;IAC5C;EACJ;EACA,OAAOG,kBAAkBA,CAACJ,OAAO,EAAEC,SAAS,EAAE;IAC1C,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EAAE;QACnB,IAAIG,MAAM,GAAGJ,SAAS,CAACK,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;QACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACpCR,OAAO,CAACE,SAAS,CAACC,GAAG,CAACE,MAAM,CAACG,CAAC,CAAC,CAAC;QACpC;MACJ,CAAC,MACI;QACD,IAAIH,MAAM,GAAGJ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC;QACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UACpCR,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGI,MAAM,CAACG,CAAC,CAAC;QACxC;MACJ;IACJ;EACJ;EACA,OAAOE,WAAWA,CAACV,OAAO,EAAEC,SAAS,EAAE;IACnC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACS,MAAM,CAACV,SAAS,CAAC,CAAC,KAEpCD,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,CAACW,OAAO,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGZ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IACpI;EACJ;EACA,OAAOC,qBAAqBA,CAACf,OAAO,EAAEgB,UAAU,EAAE;IAC9C,IAAIhB,OAAO,IAAIgB,UAAU,EAAE;MACvB,CAACA,UAAU,CAAC,CACPC,IAAI,CAAC,CAAC,CACNC,MAAM,CAACC,OAAO,CAAC,CACfC,OAAO,CAAEC,MAAM,IAAKA,MAAM,CAACd,KAAK,CAAC,GAAG,CAAC,CAACa,OAAO,CAAEnB,SAAS,IAAK,IAAI,CAACS,WAAW,CAACV,OAAO,EAAEC,SAAS,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA,OAAOqB,QAAQA,CAACtB,OAAO,EAAEC,SAAS,EAAE;IAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,SAAS,EACjB,OAAOF,OAAO,CAACE,SAAS,CAACqB,QAAQ,CAACtB,SAAS,CAAC,CAAC,KAE7C,OAAO,IAAIY,MAAM,CAAC,OAAO,GAAGZ,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,CAACuB,IAAI,CAACxB,OAAO,CAACC,SAAS,CAAC;IACtF;IACA,OAAO,KAAK;EAChB;EACA,OAAOwB,QAAQA,CAACzB,OAAO,EAAE;IACrB,OAAO0B,KAAK,CAACC,SAAS,CAACT,MAAM,CAACU,IAAI,CAAC5B,OAAO,CAAC6B,UAAU,CAACC,QAAQ,EAAE,UAAUC,KAAK,EAAE;MAC7E,OAAOA,KAAK,KAAK/B,OAAO;IAC5B,CAAC,CAAC;EACN;EACA,OAAOgC,IAAIA,CAAChC,OAAO,EAAEiC,QAAQ,EAAE;IAC3B,OAAOP,KAAK,CAACQ,IAAI,CAAClC,OAAO,CAACmC,gBAAgB,CAACF,QAAQ,CAAC,CAAC;EACzD;EACA,OAAOG,UAAUA,CAACpC,OAAO,EAAEiC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAACI,SAAS,CAACrC,OAAO,CAAC,GAAGA,OAAO,CAACsC,aAAa,CAACL,QAAQ,CAAC,GAAG,IAAI;EAC3E;EACA,OAAOM,KAAKA,CAACvC,OAAO,EAAE;IAClB,IAAI8B,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,CAACW,UAAU;IAC5C,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;MACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EACzBD,GAAG,EAAE;IACb;IACA,OAAO,CAAC,CAAC;EACb;EACA,OAAOE,gBAAgBA,CAAC3C,OAAO,EAAE4C,aAAa,EAAE;IAC5C,IAAId,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,GAAG7B,OAAO,CAAC6B,UAAU,CAACW,UAAU,GAAG,EAAE;IACtE,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;MACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,IAAIf,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,CAACD,aAAa,CAAC,IAAId,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EAC5FD,GAAG,EAAE;IACb;IACA,OAAO,CAAC,CAAC;EACb;EACA,OAAOK,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACrD,IAAIA,QAAQ,KAAK,MAAM,IAAIF,OAAO,IAAIC,MAAM,EAAE;MAC1C,IAAI,CAACE,WAAW,CAACH,OAAO,EAAEC,MAAM,CAAC;IACrC;EACJ;EACA,OAAOG,YAAYA,CAACJ,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAEG,iBAAiB,GAAG,IAAI,EAAE;IAC9E,IAAIL,OAAO,IAAIC,MAAM,EAAE;MACnB,IAAII,iBAAiB,EAAE;QACnBL,OAAO,CAACM,KAAK,CAACC,QAAQ,GAAI,GAAE5D,UAAU,CAAC6D,aAAa,CAACP,MAAM,CAAE,IAAG;MACpE;MACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;QACrB,IAAI,CAACO,gBAAgB,CAACT,OAAO,EAAEC,MAAM,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACS,gBAAgB,CAACV,OAAO,EAAEC,MAAM,CAAC;MAC1C;IACJ;EACJ;EACA,OAAOQ,gBAAgBA,CAACxD,OAAO,EAAEgD,MAAM,EAAE;IACrC,MAAMU,yBAAyB,GAAIC,EAAE,IAAK;MACtC,IAAI,CAACA,EAAE,EACH;MACJ,OAAOC,gBAAgB,CAACD,EAAE,CAAC,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,GAAGF,EAAE,GAAGD,yBAAyB,CAACC,EAAE,CAACG,aAAa,CAAC;IAC9H,CAAC;IACD,MAAMC,iBAAiB,GAAG/D,OAAO,CAACgE,YAAY,GAAG;MAAEC,KAAK,EAAEjE,OAAO,CAACkE,WAAW;MAAEC,MAAM,EAAEnE,OAAO,CAACoE;IAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACrE,OAAO,CAAC;IACxJ,MAAMsE,YAAY,GAAGtB,MAAM,CAACoB,YAAY;IACxC,MAAMG,YAAY,GAAGvB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,MAAMC,eAAe,GAAGrB,yBAAyB,CAAC1D,OAAO,CAAC;IAC1D,MAAMgF,qBAAqB,GAAGD,eAAe,EAAEP,qBAAqB,CAAC,CAAC,IAAI;MAAES,GAAG,EAAE,CAAC,CAAC,GAAGR,eAAe;MAAES,IAAI,EAAE,CAAC,CAAC,GAAGP;IAAiB,CAAC;IACpI,IAAIM,GAAG,EAAEC,IAAI;IACb,IAAIX,YAAY,CAACU,GAAG,GAAGX,YAAY,GAAGP,iBAAiB,CAACI,MAAM,GAAGU,QAAQ,CAACV,MAAM,EAAE;MAC9Ec,GAAG,GAAGV,YAAY,CAACU,GAAG,GAAGD,qBAAqB,CAACC,GAAG,GAAGlB,iBAAiB,CAACI,MAAM;MAC7EnE,OAAO,CAACqD,KAAK,CAAC8B,eAAe,GAAG,QAAQ;MACxC,IAAIZ,YAAY,CAACU,GAAG,GAAGA,GAAG,GAAG,CAAC,EAAE;QAC5BA,GAAG,GAAG,CAAC,CAAC,GAAGV,YAAY,CAACU,GAAG;MAC/B;IACJ,CAAC,MACI;MACDA,GAAG,GAAGX,YAAY,GAAGC,YAAY,CAACU,GAAG,GAAGD,qBAAqB,CAACC,GAAG;MACjEjF,OAAO,CAACqD,KAAK,CAAC8B,eAAe,GAAG,KAAK;IACzC;IACA,MAAMC,kBAAkB,GAAGb,YAAY,CAACW,IAAI,GAAGnB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK;IACvF,MAAMoB,wCAAwC,GAAGd,YAAY,CAACW,IAAI,GAAGF,qBAAqB,CAACE,IAAI;IAC/F,IAAInB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK,EAAE;MAC1C;MACAiB,IAAI,GAAG,CAACX,YAAY,CAACW,IAAI,GAAGF,qBAAqB,CAACE,IAAI,IAAI,CAAC,CAAC;IAChE,CAAC,MACI,IAAIE,kBAAkB,GAAG,CAAC,EAAE;MAC7B;MACAF,IAAI,GAAGG,wCAAwC,GAAGD,kBAAkB;IACxE,CAAC,MACI;MACD;MACAF,IAAI,GAAGX,YAAY,CAACW,IAAI,GAAGF,qBAAqB,CAACE,IAAI;IACzD;IACAlF,OAAO,CAACqD,KAAK,CAAC4B,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BjF,OAAO,CAACqD,KAAK,CAAC6B,IAAI,GAAGA,IAAI,GAAG,IAAI;EACpC;EACA,OAAOzB,gBAAgBA,CAACzD,OAAO,EAAEgD,MAAM,EAAE;IACrC,MAAMe,iBAAiB,GAAG/D,OAAO,CAACgE,YAAY,GAAG;MAAEC,KAAK,EAAEjE,OAAO,CAACkE,WAAW;MAAEC,MAAM,EAAEnE,OAAO,CAACoE;IAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACrE,OAAO,CAAC;IACxJ,MAAMsF,kBAAkB,GAAGvB,iBAAiB,CAACI,MAAM;IACnD,MAAMoB,iBAAiB,GAAGxB,iBAAiB,CAACE,KAAK;IACjD,MAAMuB,iBAAiB,GAAGxC,MAAM,CAACoB,YAAY;IAC7C,MAAMqB,gBAAgB,GAAGzC,MAAM,CAACkB,WAAW;IAC3C,MAAMK,YAAY,GAAGvB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;IACnD,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC,IAAIG,GAAG,EAAEC,IAAI;IACb,IAAIX,YAAY,CAACU,GAAG,GAAGO,iBAAiB,GAAGF,kBAAkB,GAAGT,QAAQ,CAACV,MAAM,EAAE;MAC7Ec,GAAG,GAAGV,YAAY,CAACU,GAAG,GAAGR,eAAe,GAAGa,kBAAkB;MAC7DtF,OAAO,CAACqD,KAAK,CAAC8B,eAAe,GAAG,QAAQ;MACxC,IAAIF,GAAG,GAAG,CAAC,EAAE;QACTA,GAAG,GAAGR,eAAe;MACzB;IACJ,CAAC,MACI;MACDQ,GAAG,GAAGO,iBAAiB,GAAGjB,YAAY,CAACU,GAAG,GAAGR,eAAe;MAC5DzE,OAAO,CAACqD,KAAK,CAAC8B,eAAe,GAAG,KAAK;IACzC;IACA,IAAIZ,YAAY,CAACW,IAAI,GAAGK,iBAAiB,GAAGV,QAAQ,CAACZ,KAAK,EACtDiB,IAAI,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,YAAY,CAACW,IAAI,GAAGP,gBAAgB,GAAGc,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,KAEhGL,IAAI,GAAGX,YAAY,CAACW,IAAI,GAAGP,gBAAgB;IAC/C3E,OAAO,CAACqD,KAAK,CAAC4B,GAAG,GAAGA,GAAG,GAAG,IAAI;IAC9BjF,OAAO,CAACqD,KAAK,CAAC6B,IAAI,GAAGA,IAAI,GAAG,IAAI;EACpC;EACA,OAAOU,UAAUA,CAAC5F,OAAO,EAAE6F,OAAO,GAAG,EAAE,EAAE;IACrC,OAAO7F,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG6F,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC5F,OAAO,CAAC6B,UAAU,EAAEgE,OAAO,CAACC,MAAM,CAAC,CAAC9F,OAAO,CAAC6B,UAAU,CAAC,CAAC,CAAC;EAC/H;EACA,OAAOkE,oBAAoBA,CAAC/F,OAAO,EAAE;IACjC,IAAIgG,iBAAiB,GAAG,EAAE;IAC1B,IAAIhG,OAAO,EAAE;MACT,IAAI6F,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC5F,OAAO,CAAC;MACtC,MAAMiG,aAAa,GAAG,eAAe;MACrC,MAAMC,aAAa,GAAIC,IAAI,IAAK;QAC5B,IAAIC,gBAAgB,GAAGC,MAAM,CAAC,kBAAkB,CAAC,CAACF,IAAI,EAAE,IAAI,CAAC;QAC7D,OAAOF,aAAa,CAACzE,IAAI,CAAC4E,gBAAgB,CAACvC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAIoC,aAAa,CAACzE,IAAI,CAAC4E,gBAAgB,CAACvC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAIoC,aAAa,CAACzE,IAAI,CAAC4E,gBAAgB,CAACvC,gBAAgB,CAAC,WAAW,CAAC,CAAC;MACxN,CAAC;MACD,KAAK,IAAIyC,MAAM,IAAIT,OAAO,EAAE;QACxB,IAAIU,eAAe,GAAGD,MAAM,CAAC5D,QAAQ,KAAK,CAAC,IAAI4D,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;QAChF,IAAID,eAAe,EAAE;UACjB,IAAIE,SAAS,GAAGF,eAAe,CAAChG,KAAK,CAAC,GAAG,CAAC;UAC1C,KAAK,IAAI0B,QAAQ,IAAIwE,SAAS,EAAE;YAC5B,IAAI9C,EAAE,GAAG,IAAI,CAACvB,UAAU,CAACkE,MAAM,EAAErE,QAAQ,CAAC;YAC1C,IAAI0B,EAAE,IAAIuC,aAAa,CAACvC,EAAE,CAAC,EAAE;cACzBqC,iBAAiB,CAACU,IAAI,CAAC/C,EAAE,CAAC;YAC9B;UACJ;QACJ;QACA,IAAI2C,MAAM,CAAC5D,QAAQ,KAAK,CAAC,IAAIwD,aAAa,CAACI,MAAM,CAAC,EAAE;UAChDN,iBAAiB,CAACU,IAAI,CAACJ,MAAM,CAAC;QAClC;MACJ;IACJ;IACA,OAAON,iBAAiB;EAC5B;EACA,OAAOW,2BAA2BA,CAAC3G,OAAO,EAAE;IACxCA,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,QAAQ;IACnC5G,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,OAAO;IAC/B,IAAIC,aAAa,GAAG9G,OAAO,CAACoE,YAAY;IACxCpE,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,MAAM;IAC9B7G,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,SAAS;IACpC,OAAOE,aAAa;EACxB;EACA,OAAOC,0BAA0BA,CAAC/G,OAAO,EAAE;IACvCA,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,QAAQ;IACnC5G,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,OAAO;IAC/B,IAAIG,YAAY,GAAGhH,OAAO,CAACkE,WAAW;IACtClE,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,MAAM;IAC9B7G,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,SAAS;IACpC,OAAOI,YAAY;EACvB;EACA,OAAO3C,0BAA0BA,CAACrE,OAAO,EAAE;IACvC,IAAIiH,UAAU,GAAG,CAAC,CAAC;IACnBjH,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,QAAQ;IACnC5G,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,OAAO;IAC/BI,UAAU,CAAChD,KAAK,GAAGjE,OAAO,CAACkE,WAAW;IACtC+C,UAAU,CAAC9C,MAAM,GAAGnE,OAAO,CAACoE,YAAY;IACxCpE,OAAO,CAACqD,KAAK,CAACwD,OAAO,GAAG,MAAM;IAC9B7G,OAAO,CAACqD,KAAK,CAACuD,UAAU,GAAG,SAAS;IACpC,OAAOK,UAAU;EACrB;EACA,OAAOC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACjC,IAAIC,cAAc,GAAGzD,gBAAgB,CAACuD,SAAS,CAAC,CAACtD,gBAAgB,CAAC,gBAAgB,CAAC;IACnF,IAAIyD,SAAS,GAAGD,cAAc,GAAGE,UAAU,CAACF,cAAc,CAAC,GAAG,CAAC;IAC/D,IAAIG,eAAe,GAAG5D,gBAAgB,CAACuD,SAAS,CAAC,CAACtD,gBAAgB,CAAC,YAAY,CAAC;IAChF,IAAI4D,UAAU,GAAGD,eAAe,GAAGD,UAAU,CAACC,eAAe,CAAC,GAAG,CAAC;IAClE,IAAIE,aAAa,GAAGP,SAAS,CAAC3C,qBAAqB,CAAC,CAAC;IACrD,IAAImD,QAAQ,GAAGP,IAAI,CAAC5C,qBAAqB,CAAC,CAAC;IAC3C,IAAIoD,MAAM,GAAGD,QAAQ,CAAC1C,GAAG,GAAG4C,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAIL,aAAa,CAACzC,GAAG,GAAG4C,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC,GAAGT,SAAS,GAAGG,UAAU;IAC5H,IAAIO,MAAM,GAAGb,SAAS,CAACY,SAAS;IAChC,IAAIjB,aAAa,GAAGK,SAAS,CAACc,YAAY;IAC1C,IAAIC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACf,IAAI,CAAC;IAC1C,IAAIQ,MAAM,GAAG,CAAC,EAAE;MACZT,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM;IACzC,CAAC,MACI,IAAIA,MAAM,GAAGM,UAAU,GAAGpB,aAAa,EAAE;MAC1CK,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM,GAAGd,aAAa,GAAGoB,UAAU;IACtE;EACJ;EACA,OAAOE,MAAMA,CAACpI,OAAO,EAAEqI,QAAQ,EAAE;IAC7BrI,OAAO,CAACqD,KAAK,CAACiF,OAAO,GAAG,CAAC;IACzB,IAAIC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIF,OAAO,GAAG,CAAC;IACf,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnBH,OAAO,GAAG,CAACtI,OAAO,CAACqD,KAAK,CAACiF,OAAO,CAAC1H,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI4H,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,IAAI,IAAIF,QAAQ;MAC7FrI,OAAO,CAACqD,KAAK,CAACiF,OAAO,GAAGA,OAAO;MAC/BC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;MAClB,IAAI,CAACF,OAAO,GAAG,CAAC,EAAE;QACbjC,MAAM,CAACsC,qBAAqB,IAAIA,qBAAqB,CAACF,IAAI,CAAC,IAAKG,UAAU,CAACH,IAAI,EAAE,EAAE,CAAC;MACzF;IACJ,CAAC;IACDA,IAAI,CAAC,CAAC;EACV;EACA,OAAOI,OAAOA,CAAC7I,OAAO,EAAE8I,EAAE,EAAE;IACxB,IAAIR,OAAO,GAAG,CAAC;MAAES,QAAQ,GAAG,EAAE;MAAEV,QAAQ,GAAGS,EAAE;MAAEE,GAAG,GAAGD,QAAQ,GAAGV,QAAQ;IACxE,IAAIY,MAAM,GAAGC,WAAW,CAAC,MAAM;MAC3BZ,OAAO,GAAGA,OAAO,GAAGU,GAAG;MACvB,IAAIV,OAAO,IAAI,CAAC,EAAE;QACdA,OAAO,GAAG,CAAC;QACXa,aAAa,CAACF,MAAM,CAAC;MACzB;MACAjJ,OAAO,CAACqD,KAAK,CAACiF,OAAO,GAAGA,OAAO;IACnC,CAAC,EAAES,QAAQ,CAAC;EAChB;EACA,OAAOrE,kBAAkBA,CAAA,EAAG;IACxB,IAAI0E,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;IAClC,OAAO,CAAChD,MAAM,CAACiD,WAAW,IAAIF,GAAG,CAACrB,SAAS,KAAKqB,GAAG,CAACG,SAAS,IAAI,CAAC,CAAC;EACvE;EACA,OAAO3E,mBAAmBA,CAAA,EAAG;IACzB,IAAIwE,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;IAClC,OAAO,CAAChD,MAAM,CAACmD,WAAW,IAAIJ,GAAG,CAACK,UAAU,KAAKL,GAAG,CAACM,UAAU,IAAI,CAAC,CAAC;EACzE;EACA,OAAOC,OAAOA,CAAC3J,OAAO,EAAEiC,QAAQ,EAAE;IAC9B,IAAI2H,CAAC,GAAGC,OAAO,CAAClI,SAAS;IACzB,IAAImI,CAAC,GAAGF,CAAC,CAAC,SAAS,CAAC,IAChBA,CAAC,CAACG,qBAAqB,IACvBH,CAAC,CAAC,oBAAoB,CAAC,IACvBA,CAAC,CAAC,mBAAmB,CAAC,IACtB,UAAUI,CAAC,EAAE;MACT,OAAO,EAAE,CAACC,OAAO,CAACrI,IAAI,CAACiG,QAAQ,CAAC1F,gBAAgB,CAAC6H,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACrE,CAAC;IACL,OAAOF,CAAC,CAAClI,IAAI,CAAC5B,OAAO,EAAEiC,QAAQ,CAAC;EACpC;EACA,OAAOsB,aAAaA,CAACI,EAAE,EAAEuG,MAAM,EAAE;IAC7B,IAAIjG,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAIgG,MAAM,EAAE;MACR,IAAI7G,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;MAChCM,KAAK,IAAIsD,UAAU,CAAClE,KAAK,CAAC8G,UAAU,CAAC,GAAG5C,UAAU,CAAClE,KAAK,CAAC+G,WAAW,CAAC;IACzE;IACA,OAAOnG,KAAK;EAChB;EACA,OAAOoG,oBAAoBA,CAAC1G,EAAE,EAAE;IAC5B,IAAIN,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChC,OAAO4D,UAAU,CAAClE,KAAK,CAACiH,WAAW,CAAC,GAAG/C,UAAU,CAAClE,KAAK,CAACkH,YAAY,CAAC;EACzE;EACA,OAAOC,mBAAmBA,CAAC7G,EAAE,EAAE;IAC3B,IAAIN,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChC,OAAO4D,UAAU,CAAClE,KAAK,CAAC8G,UAAU,CAAC,GAAG5C,UAAU,CAAClE,KAAK,CAAC+G,WAAW,CAAC;EACvE;EACA,OAAOK,UAAUA,CAAC9G,EAAE,EAAE;IAClB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAIb,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIsD,UAAU,CAAClE,KAAK,CAACiH,WAAW,CAAC,GAAG/C,UAAU,CAAClE,KAAK,CAACkH,YAAY,CAAC;IACvE,OAAOtG,KAAK;EAChB;EACA,OAAOA,KAAKA,CAACN,EAAE,EAAE;IACb,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAIb,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIsD,UAAU,CAAClE,KAAK,CAACiH,WAAW,CAAC,GAAG/C,UAAU,CAAClE,KAAK,CAACkH,YAAY,CAAC;IACvE,OAAOtG,KAAK;EAChB;EACA,OAAOyG,cAAcA,CAAC/G,EAAE,EAAE;IACtB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAIf,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChCQ,MAAM,IAAIoD,UAAU,CAAClE,KAAK,CAACoE,UAAU,CAAC,GAAGF,UAAU,CAAClE,KAAK,CAACsH,aAAa,CAAC;IACxE,OAAOxG,MAAM;EACjB;EACA,OAAOgE,cAAcA,CAACxE,EAAE,EAAEuG,MAAM,EAAE;IAC9B,IAAI/F,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAI8F,MAAM,EAAE;MACR,IAAI7G,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;MAChCQ,MAAM,IAAIoD,UAAU,CAAClE,KAAK,CAACuH,SAAS,CAAC,GAAGrD,UAAU,CAAClE,KAAK,CAACwH,YAAY,CAAC;IAC1E;IACA,OAAO1G,MAAM;EACjB;EACA,OAAO2G,SAASA,CAACnH,EAAE,EAAE;IACjB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;IAC5B,IAAIf,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChCQ,MAAM,IAAIoD,UAAU,CAAClE,KAAK,CAACoE,UAAU,CAAC,GAAGF,UAAU,CAAClE,KAAK,CAACsH,aAAa,CAAC,GAAGpD,UAAU,CAAClE,KAAK,CAAC0H,cAAc,CAAC,GAAGxD,UAAU,CAAClE,KAAK,CAAC2H,iBAAiB,CAAC;IACjJ,OAAO7G,MAAM;EACjB;EACA,OAAO8G,QAAQA,CAACtH,EAAE,EAAE;IAChB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;IAC1B,IAAIb,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;IAChCM,KAAK,IAAIsD,UAAU,CAAClE,KAAK,CAACiH,WAAW,CAAC,GAAG/C,UAAU,CAAClE,KAAK,CAACkH,YAAY,CAAC,GAAGhD,UAAU,CAAClE,KAAK,CAAC6H,eAAe,CAAC,GAAG3D,UAAU,CAAClE,KAAK,CAAC8H,gBAAgB,CAAC;IAChJ,OAAOlH,KAAK;EAChB;EACA,OAAOa,WAAWA,CAAA,EAAG;IACjB,IAAIsG,GAAG,GAAG/E,MAAM;MAAEgF,CAAC,GAAGxD,QAAQ;MAAEyD,CAAC,GAAGD,CAAC,CAAChC,eAAe;MAAEkC,CAAC,GAAGF,CAAC,CAACG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;MAAEC,CAAC,GAAGL,GAAG,CAACX,UAAU,IAAIa,CAAC,CAACI,WAAW,IAAIH,CAAC,CAACG,WAAW;MAAEC,CAAC,GAAGP,GAAG,CAACQ,WAAW,IAAIN,CAAC,CAACrD,YAAY,IAAIsD,CAAC,CAACtD,YAAY;IAC3M,OAAO;MAAEhE,KAAK,EAAEwH,CAAC;MAAEtH,MAAM,EAAEwH;IAAE,CAAC;EAClC;EACA,OAAOE,SAASA,CAAClI,EAAE,EAAE;IACjB,IAAImI,IAAI,GAAGnI,EAAE,CAACa,qBAAqB,CAAC,CAAC;IACrC,OAAO;MACHS,GAAG,EAAE6G,IAAI,CAAC7G,GAAG,IAAIoB,MAAM,CAACiD,WAAW,IAAIzB,QAAQ,CAACwB,eAAe,CAACtB,SAAS,IAAIF,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAI,CAAC,CAAC;MAC1G7C,IAAI,EAAE4G,IAAI,CAAC5G,IAAI,IAAImB,MAAM,CAACmD,WAAW,IAAI3B,QAAQ,CAACwB,eAAe,CAACI,UAAU,IAAI5B,QAAQ,CAACC,IAAI,CAAC2B,UAAU,IAAI,CAAC;IACjH,CAAC;EACL;EACA,OAAOsC,kBAAkBA,CAAC/L,OAAO,EAAEgM,kBAAkB,EAAE;IACnD,IAAInK,UAAU,GAAG7B,OAAO,CAAC6B,UAAU;IACnC,IAAI,CAACA,UAAU,EACX,MAAO,uBAAsB;IACjC,OAAOA,UAAU,CAACoK,YAAY,CAACD,kBAAkB,EAAEhM,OAAO,CAAC;EAC/D;EACA,OAAOkM,YAAYA,CAAA,EAAG;IAClB,IAAIC,SAAS,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC9B,OAAOD,SAAS,CAACE,SAAS;IAC9B;EACJ;EACA,OAAOC,IAAIA,CAAA,EAAG;IACV,IAAIC,EAAE,GAAGlG,MAAM,CAAC8F,SAAS,CAACE,SAAS;IACnC,IAAIG,IAAI,GAAGD,EAAE,CAACtC,OAAO,CAAC,OAAO,CAAC;IAC9B,IAAIuC,IAAI,GAAG,CAAC,EAAE;MACV;MACA,OAAO,IAAI;IACf;IACA,IAAIC,OAAO,GAAGF,EAAE,CAACtC,OAAO,CAAC,UAAU,CAAC;IACpC,IAAIwC,OAAO,GAAG,CAAC,EAAE;MACb;MACA,IAAIC,EAAE,GAAGH,EAAE,CAACtC,OAAO,CAAC,KAAK,CAAC;MAC1B,OAAO,IAAI;IACf;IACA,IAAI0C,IAAI,GAAGJ,EAAE,CAACtC,OAAO,CAAC,OAAO,CAAC;IAC9B,IAAI0C,IAAI,GAAG,CAAC,EAAE;MACV;MACA,OAAO,IAAI;IACf;IACA;IACA,OAAO,KAAK;EAChB;EACA,OAAOC,KAAKA,CAAA,EAAG;IACX,OAAO,kBAAkB,CAACpL,IAAI,CAAC2K,SAAS,CAACE,SAAS,CAAC,IAAI,CAAChG,MAAM,CAAC,UAAU,CAAC;EAC9E;EACA,OAAOwG,SAASA,CAAA,EAAG;IACf,OAAO,YAAY,CAACrL,IAAI,CAAC2K,SAAS,CAACE,SAAS,CAAC;EACjD;EACA,OAAOS,aAAaA,CAAA,EAAG;IACnB,OAAO,cAAc,IAAIzG,MAAM,IAAI8F,SAAS,CAACY,cAAc,GAAG,CAAC;EACnE;EACA,OAAO7J,WAAWA,CAAClD,OAAO,EAAEgD,MAAM,EAAE;IAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACE,WAAW,CAAClD,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,IAAIA,MAAM,CAACW,EAAE,IAAIX,MAAM,CAACW,EAAE,CAACqJ,aAAa,EACnDhK,MAAM,CAACW,EAAE,CAACqJ,aAAa,CAAC9J,WAAW,CAAClD,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGgD,MAAM,GAAG,MAAM,GAAGhD,OAAO;EAC1D;EACA,OAAOiN,WAAWA,CAACjN,OAAO,EAAEgD,MAAM,EAAE;IAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACiK,WAAW,CAACjN,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,CAACW,EAAE,IAAIX,MAAM,CAACW,EAAE,CAACqJ,aAAa,EACzChK,MAAM,CAACW,EAAE,CAACqJ,aAAa,CAACC,WAAW,CAACjN,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGA,OAAO,GAAG,QAAQ,GAAGgD,MAAM;EAC5D;EACA,OAAOkK,aAAaA,CAAClN,OAAO,EAAE;IAC1B,IAAI,EAAE,QAAQ,IAAI6J,OAAO,CAAClI,SAAS,CAAC,EAChC3B,OAAO,CAAC6B,UAAU,CAACoL,WAAW,CAACjN,OAAO,CAAC,CAAC,KAExCA,OAAO,CAACW,MAAM,CAAC,CAAC;EACxB;EACA,OAAO0B,SAASA,CAAC8K,GAAG,EAAE;IAClB,OAAO,OAAOC,WAAW,KAAK,QAAQ,GAAGD,GAAG,YAAYC,WAAW,GAAGD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,CAACzK,QAAQ,KAAK,CAAC,IAAI,OAAOyK,GAAG,CAACE,QAAQ,KAAK,QAAQ;EAClL;EACA,OAAOC,uBAAuBA,CAAC3J,EAAE,EAAE;IAC/B,IAAIA,EAAE,EAAE;MACJ,IAAIN,KAAK,GAAGO,gBAAgB,CAACD,EAAE,CAAC;MAChC,OAAOA,EAAE,CAACO,WAAW,GAAGP,EAAE,CAAC+H,WAAW,GAAGnE,UAAU,CAAClE,KAAK,CAAC6H,eAAe,CAAC,GAAG3D,UAAU,CAAClE,KAAK,CAAC8H,gBAAgB,CAAC;IACnH,CAAC,MACI;MACD,IAAI,IAAI,CAACvL,wBAAwB,KAAK,IAAI,EACtC,OAAO,IAAI,CAACA,wBAAwB;MACxC,IAAI2N,SAAS,GAAG1F,QAAQ,CAAC2F,aAAa,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAACtN,SAAS,GAAG,qBAAqB;MAC3C4H,QAAQ,CAACC,IAAI,CAAC5E,WAAW,CAACqK,SAAS,CAAC;MACpC,IAAIE,cAAc,GAAGF,SAAS,CAACrJ,WAAW,GAAGqJ,SAAS,CAAC7B,WAAW;MAClE7D,QAAQ,CAACC,IAAI,CAACmF,WAAW,CAACM,SAAS,CAAC;MACpC,IAAI,CAAC3N,wBAAwB,GAAG6N,cAAc;MAC9C,OAAOA,cAAc;IACzB;EACJ;EACA,OAAOC,wBAAwBA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAAC7N,yBAAyB,KAAK,IAAI,EACvC,OAAO,IAAI,CAACA,yBAAyB;IACzC,IAAI0N,SAAS,GAAG1F,QAAQ,CAAC2F,aAAa,CAAC,KAAK,CAAC;IAC7CD,SAAS,CAACtN,SAAS,GAAG,qBAAqB;IAC3C4H,QAAQ,CAACC,IAAI,CAAC5E,WAAW,CAACqK,SAAS,CAAC;IACpC,IAAII,eAAe,GAAGJ,SAAS,CAACnJ,YAAY,GAAGmJ,SAAS,CAACtF,YAAY;IACrEJ,QAAQ,CAACC,IAAI,CAACmF,WAAW,CAACM,SAAS,CAAC;IACpC,IAAI,CAAC3N,wBAAwB,GAAG+N,eAAe;IAC/C,OAAOA,eAAe;EAC1B;EACA,OAAOC,mBAAmBA,CAAC5N,OAAO,EAAE6N,UAAU,EAAEC,IAAI,EAAE;IAClD9N,OAAO,CAAC6N,UAAU,CAAC,CAACE,KAAK,CAAC/N,OAAO,EAAE8N,IAAI,CAAC;EAC5C;EACA,OAAOE,cAAcA,CAAA,EAAG;IACpB,IAAI3H,MAAM,CAAC4H,YAAY,EAAE;MACrB,IAAI5H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACC,KAAK,EAAE;QAC7B7H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACjC,CAAC,MACI,IAAI7H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACE,eAAe,IAAI9H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACG,UAAU,GAAG,CAAC,IAAI/H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC7N,MAAM,GAAG,CAAC,EAAE;QACvJ4F,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACE,eAAe,CAAC,CAAC;MAC3C;IACJ,CAAC,MACI,IAAItG,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,WAAW,CAAC,CAACqG,KAAK,EAAE;MAC3D,IAAI;QACArG,QAAQ,CAAC,WAAW,CAAC,CAACqG,KAAK,CAAC,CAAC;MACjC,CAAC,CACD,OAAOK,KAAK,EAAE;QACV;MAAA;IAER;EACJ;EACA,OAAOC,UAAUA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC1O,OAAO,EAAE;MACf,IAAI2O,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACrC,IAAI,CAAC5O,OAAO,GAAG,CAAC,CAAC;MACjB,IAAI2O,OAAO,CAAC3O,OAAO,EAAE;QACjB,IAAI,CAACA,OAAO,CAAC2O,OAAO,CAAC3O,OAAO,CAAC,GAAG,IAAI;QACpC,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,GAAG2O,OAAO,CAACE,OAAO;MAC7C;MACA,IAAI,IAAI,CAAC7O,OAAO,CAAC,QAAQ,CAAC,EAAE;QACxB,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;MACjC,CAAC,MACI,IAAI,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC7B,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;MACjC;IACJ;IACA,OAAO,IAAI,CAACA,OAAO;EACvB;EACA,OAAO4O,gBAAgBA,CAAA,EAAG;IACtB,IAAInC,EAAE,GAAGJ,SAAS,CAACE,SAAS,CAACuC,WAAW,CAAC,CAAC;IAC1C,IAAIC,KAAK,GAAG,uBAAuB,CAACC,IAAI,CAACvC,EAAE,CAAC,IAAI,uBAAuB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,oCAAoC,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,iBAAiB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAKA,EAAE,CAACtC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAAC6E,IAAI,CAACvC,EAAE,CAAE,IAAI,EAAE;IACnP,OAAO;MACHzM,OAAO,EAAE+O,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MACvBF,OAAO,EAAEE,KAAK,CAAC,CAAC,CAAC,IAAI;IACzB,CAAC;EACL;EACA,OAAOE,SAASA,CAACC,KAAK,EAAE;IACpB,IAAIC,MAAM,CAACF,SAAS,EAAE;MAClB,OAAOE,MAAM,CAACF,SAAS,CAACC,KAAK,CAAC;IAClC,CAAC,MACI;MACD,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIE,QAAQ,CAACF,KAAK,CAAC,IAAItJ,IAAI,CAACyJ,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK;IACtF;EACJ;EACA,OAAOI,QAAQA,CAACpP,OAAO,EAAE;IACrB,OAAO,CAACA,OAAO,IAAIA,OAAO,CAACgE,YAAY,KAAK,IAAI;EACpD;EACA,OAAOqL,SAASA,CAACrP,OAAO,EAAE;IACtB,OAAOA,OAAO,IAAIA,OAAO,CAACgE,YAAY,IAAI,IAAI;EAClD;EACA,OAAOsL,OAAOA,CAACtP,OAAO,EAAE;IACpB,OAAOA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACqN,QAAQ,IAAIrN,OAAO,CAAC6B,UAAU;EACvG;EACA,OAAO0N,KAAKA,CAACvP,OAAO,EAAEwP,OAAO,EAAE;IAC3BxP,OAAO,IAAI6H,QAAQ,CAAC4H,aAAa,KAAKzP,OAAO,IAAIA,OAAO,CAACuP,KAAK,CAACC,OAAO,CAAC;EAC3E;EACA,OAAOE,oBAAoBA,CAAC1P,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;IAChD,IAAI0N,iBAAiB,GAAG,IAAI,CAAC3N,IAAI,CAAChC,OAAO,EAAG,2FAA0FiC,QAAS;AACvJ,qIAAqIA,QAAS;AAC9I,yGAAyGA,QAAS;AAClH,0GAA0GA,QAAS;AACnH,4GAA4GA,QAAS;AACrH,8GAA8GA,QAAS;AACvH,qHAAqHA,QAAS,EAAC,CAAC;IACxH,IAAI2N,wBAAwB,GAAG,EAAE;IACjC,KAAK,IAAIC,gBAAgB,IAAIF,iBAAiB,EAAE;MAC5C,IAAI/L,gBAAgB,CAACiM,gBAAgB,CAAC,CAAChJ,OAAO,IAAI,MAAM,IAAIjD,gBAAgB,CAACiM,gBAAgB,CAAC,CAACjJ,UAAU,IAAI,QAAQ,EACjHgJ,wBAAwB,CAAClJ,IAAI,CAACmJ,gBAAgB,CAAC;IACvD;IACA,OAAOD,wBAAwB;EACnC;EACA,OAAOE,wBAAwBA,CAAC9P,OAAO,EAAEiC,QAAQ,EAAE;IAC/C,MAAM0N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC1P,OAAO,EAAEiC,QAAQ,CAAC;IACtE,OAAO0N,iBAAiB,CAAClP,MAAM,GAAG,CAAC,GAAGkP,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;EACrE;EACA,OAAOI,uBAAuBA,CAAC/P,OAAO,EAAEiC,QAAQ,EAAE;IAC9C,MAAM0N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC1P,OAAO,EAAEiC,QAAQ,CAAC;IACtE,OAAO0N,iBAAiB,CAAClP,MAAM,GAAG,CAAC,GAAGkP,iBAAiB,CAACA,iBAAiB,CAAClP,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAChG;EACA,OAAOuP,uBAAuBA,CAAChQ,OAAO,EAAEiQ,OAAO,GAAG,KAAK,EAAE;IACrD,MAAMN,iBAAiB,GAAGjQ,UAAU,CAACgQ,oBAAoB,CAAC1P,OAAO,CAAC;IAClE,IAAIuC,KAAK,GAAG,CAAC;IACb,IAAIoN,iBAAiB,IAAIA,iBAAiB,CAAClP,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMyP,YAAY,GAAGP,iBAAiB,CAAC1F,OAAO,CAAC0F,iBAAiB,CAAC,CAAC,CAAC,CAACQ,aAAa,CAACV,aAAa,CAAC;MAChG,IAAIQ,OAAO,EAAE;QACT,IAAIC,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAK,CAAC,EAAE;UAC1C3N,KAAK,GAAGoN,iBAAiB,CAAClP,MAAM,GAAG,CAAC;QACxC,CAAC,MACI;UACD8B,KAAK,GAAG2N,YAAY,GAAG,CAAC;QAC5B;MACJ,CAAC,MACI,IAAIA,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAKP,iBAAiB,CAAClP,MAAM,GAAG,CAAC,EAAE;QAC1E8B,KAAK,GAAG2N,YAAY,GAAG,CAAC;MAC5B;IACJ;IACA,OAAOP,iBAAiB,CAACpN,KAAK,CAAC;EACnC;EACA,OAAO6N,cAAcA,CAAA,EAAG;IACpB,IAAI,CAACzQ,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,GAAG;IAChC,OAAO,EAAE,IAAI,CAACA,MAAM;EACxB;EACA,OAAOsO,YAAYA,CAAA,EAAG;IAClB,IAAI5H,MAAM,CAAC4H,YAAY,EACnB,OAAO5H,MAAM,CAAC4H,YAAY,CAAC,CAAC,CAACoC,QAAQ,CAAC,CAAC,CAAC,KACvC,IAAIxI,QAAQ,CAACoG,YAAY,EAC1B,OAAOpG,QAAQ,CAACoG,YAAY,CAAC,CAAC,CAACoC,QAAQ,CAAC,CAAC,CAAC,KACzC,IAAIxI,QAAQ,CAAC,WAAW,CAAC,EAC1B,OAAOA,QAAQ,CAAC,WAAW,CAAC,CAACyI,WAAW,CAAC,CAAC,CAACC,IAAI;IACnD,OAAO,IAAI;EACf;EACA,OAAOC,gBAAgBA,CAACxN,MAAM,EAAEW,EAAE,EAAE;IAChC,IAAI,CAACX,MAAM,EACP,OAAO,IAAI;IACf,QAAQA,MAAM;MACV,KAAK,UAAU;QACX,OAAO6E,QAAQ;MACnB,KAAK,QAAQ;QACT,OAAOxB,MAAM;MACjB,KAAK,OAAO;QACR,OAAO1C,EAAE,EAAE8M,kBAAkB;MACjC,KAAK,OAAO;QACR,OAAO9M,EAAE,EAAE+M,sBAAsB;MACrC,KAAK,SAAS;QACV,OAAO/M,EAAE,EAAEG,aAAa;MAC5B,KAAK,cAAc;QACf,OAAOH,EAAE,EAAEG,aAAa,CAACA,aAAa;MAC1C;QACI,MAAM6M,IAAI,GAAG,OAAO3N,MAAM;QAC1B,IAAI2N,IAAI,KAAK,QAAQ,EAAE;UACnB,OAAO9I,QAAQ,CAACvF,aAAa,CAACU,MAAM,CAAC;QACzC,CAAC,MACI,IAAI2N,IAAI,KAAK,QAAQ,IAAI3N,MAAM,CAAC4N,cAAc,CAAC,eAAe,CAAC,EAAE;UAClE,OAAO,IAAI,CAACtB,OAAO,CAACtM,MAAM,CAACgK,aAAa,CAAC,GAAGhK,MAAM,CAACgK,aAAa,GAAG6D,SAAS;QAChF;QACA,MAAMC,UAAU,GAAI3D,GAAG,IAAK,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAAC4D,WAAW,IAAI5D,GAAG,CAACvL,IAAI,IAAIuL,GAAG,CAACY,KAAK,CAAC;QAC/E,MAAM/N,OAAO,GAAG8Q,UAAU,CAAC9N,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM;QACtD,OAAQhD,OAAO,IAAIA,OAAO,CAAC0C,QAAQ,KAAK,CAAC,IAAK,IAAI,CAAC4M,OAAO,CAACtP,OAAO,CAAC,GAAGA,OAAO,GAAG,IAAI;IAC5F;EACJ;EACA,OAAOoM,QAAQA,CAAA,EAAG;IACd,OAAO,CAAC,EAAE,OAAO/F,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACwB,QAAQ,IAAIxB,MAAM,CAACwB,QAAQ,CAAC2F,aAAa,CAAC;EAChG;EACA,OAAOwD,YAAYA,CAAChR,OAAO,EAAEiR,IAAI,EAAE;IAC/B,IAAIjR,OAAO,EAAE;MACT,MAAMgP,KAAK,GAAGhP,OAAO,CAACgR,YAAY,CAACC,IAAI,CAAC;MACxC,IAAI,CAACC,KAAK,CAAClC,KAAK,CAAC,EAAE;QACf,OAAO,CAACA,KAAK;MACjB;MACA,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;QACvC,OAAOA,KAAK,KAAK,MAAM;MAC3B;MACA,OAAOA,KAAK;IAChB;IACA,OAAO6B,SAAS;EACpB;EACA,OAAOM,2BAA2BA,CAAA,EAAG;IACjC,OAAO9K,MAAM,CAACoE,UAAU,GAAG5C,QAAQ,CAACwB,eAAe,CAACnF,WAAW;EACnE;EACA,OAAOkN,eAAeA,CAACnR,SAAS,GAAG,mBAAmB,EAAE;IACpD4H,QAAQ,CAACC,IAAI,CAACzE,KAAK,CAACgO,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAACF,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/F,IAAI,CAACpR,QAAQ,CAAC8H,QAAQ,CAACC,IAAI,EAAE7H,SAAS,CAAC;EAC3C;EACA,OAAOqR,iBAAiBA,CAACrR,SAAS,GAAG,mBAAmB,EAAE;IACtD4H,QAAQ,CAACC,IAAI,CAACzE,KAAK,CAACkO,cAAc,CAAC,mBAAmB,CAAC;IACvD,IAAI,CAAC7Q,WAAW,CAACmH,QAAQ,CAACC,IAAI,EAAE7H,SAAS,CAAC;EAC9C;AACJ;AAEA,MAAMuR,6BAA6B,CAAC;EAChCxR,OAAO;EACPyR,QAAQ;EACRzL,iBAAiB;EACjB+K,WAAWA,CAAC/Q,OAAO,EAAEyR,QAAQ,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACvC,IAAI,CAACzR,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACyR,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC1L,iBAAiB,GAAGtG,UAAU,CAACqG,oBAAoB,CAAC,IAAI,CAAC/F,OAAO,CAAC;IACtE,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwF,iBAAiB,CAACvF,MAAM,EAAED,CAAC,EAAE,EAAE;MACpD,IAAI,CAACwF,iBAAiB,CAACxF,CAAC,CAAC,CAACmR,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,QAAQ,CAAC;IACvE;EACJ;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5L,iBAAiB,EAAE;MACxB,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwF,iBAAiB,CAACvF,MAAM,EAAED,CAAC,EAAE,EAAE;QACpD,IAAI,CAACwF,iBAAiB,CAACxF,CAAC,CAAC,CAACqR,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACJ,QAAQ,CAAC;MAC1E;IACJ;EACJ;EACAK,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC5R,OAAO,GAAG,IAAI;IACnB,IAAI,CAACyR,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACzL,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;;AAEA,SAASwL,6BAA6B,EAAE9R,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}