import { Component } from '@angular/core';
import { AuthService } from '@app/core/services/auth.service';
import { LayoutService } from 'src/app/layout/app.layout.service';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styles: [
        `
            :host ::ng-deep .pi-eye,
            :host ::ng-deep .pi-eye-slash {
                transform: scale(1.6);
                margin-right: 1rem;
                color: var(--primary-color) !important;
            }
        `,
    ],
})
export class LoginComponent {
    valCheck: string[] = ['remember'];

    password!: string;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
    ) {
        this.authService.redirectToDashboard();
    }

    login() {
        this.authService.login();
    }
}
