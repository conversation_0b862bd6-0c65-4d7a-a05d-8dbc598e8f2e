export class Utils {
    static isEmpty(val: any) {
        return (
            val === undefined ||
            val === null ||
            val === '' ||
            (Array.isArray(val) && val.length === 0) ||
            (typeof val === 'object' && Object.keys(val).length === 0)
        );
    }

    static isNull(val: any) {
        return val === undefined || val === null;
    }

    static isNumber(val: any) {
        return typeof val === 'number' && !isNaN(val);
    }

    static isString(val: any) {
        return typeof val === 'string';
    }

    static isBoolean(val: any) {
        return typeof val === 'boolean';
    }

    static isObject(val: any) {
        return val !== null && typeof val === 'object' && !Array.isArray(val);
    }

    static isArray(val: any) {
        return Array.isArray(val);
    }

    static deepClone<T>(obj: T): T {
        return JSON.parse(JSON.stringify(obj));
    }

    static randomInt(min: number, max: number) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    static debounce(func: Function, delay: number) {
        let timer: NodeJS.Timeout;
        return function (...args: any[]) {
            clearTimeout(timer);
            timer = setTimeout(() => func.apply(this, args), delay);
        };
    }
}
