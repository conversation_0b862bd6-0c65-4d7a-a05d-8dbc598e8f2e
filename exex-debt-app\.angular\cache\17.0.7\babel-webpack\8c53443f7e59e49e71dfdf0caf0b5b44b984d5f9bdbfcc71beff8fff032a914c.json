{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-spinner\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Dashboard Angular';\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 2,\n    vars: 1,\n    consts: [[\"bdColor\", \"rgba(0, 0, 0, 0.8)\", \"size\", \"medium\", \"color\", \"#fff\", \"type\", \"ball-scale-multiple\", 3, \"fullScreen\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"ngx-spinner\", 0)(1, \"router-outlet\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"fullScreen\", true);\n      }\n    },\n    dependencies: [i1.RouterOutlet, i2.NgxSpinnerComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "ɵɵproperty"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    template: ` <ngx-spinner\r\n            bdColor=\"rgba(0, 0, 0, 0.8)\"\r\n            size=\"medium\"\r\n            color=\"#fff\"\r\n            [fullScreen]=\"true\"\r\n            type=\"ball-scale-multiple\"\r\n        >\r\n            <!-- <p style=\"color: white\">Loading...</p> -->\r\n        </ngx-spinner>\r\n\r\n        <router-outlet></router-outlet>`,\r\n})\r\nexport class AppComponent {\r\n    title = 'Dashboard Angular';\r\n}\r\n"], "mappings": ";;;AAgBA,OAAM,MAAOA,YAAY;EAdzBC,YAAA;IAeI,KAAAC,KAAK,GAAG,mBAAmB;;EAC9B,QAAAC,CAAA,G;qBAFYH,YAAY;EAAA;EAAA,QAAAI,EAAA,G;UAAZJ,YAAY;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAZTE,EAAA,CAAAC,SAAA,qBAQM;;;QAJVD,EAAA,CAAAE,UAAA,oBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}