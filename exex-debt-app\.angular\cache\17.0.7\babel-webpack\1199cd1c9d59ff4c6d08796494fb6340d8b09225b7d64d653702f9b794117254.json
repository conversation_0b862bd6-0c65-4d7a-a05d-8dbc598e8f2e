{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardsRoutingModule {\n  static #_ = this.ɵfac = function DashboardsRoutingModule_Factory(t) {\n    return new (t || DashboardsRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardsRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'user',\n      loadChildren: () => import('./user/user.module').then(m => m.UserModule)\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DashboardsRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "loadChildren", "then", "m", "UserModule", "redirectTo", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\components\\dashboard\\dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@NgModule({\n    imports: [\n        RouterModule.forChild([\n            {\n                path: 'user',\n                loadChildren: () =>\n                    import('./user/user.module').then((m) => m.UserModule),\n            },\n            { path: '**', redirectTo: '/notfound' },\n        ]),\n    ],\n    exports: [RouterModule],\n})\nexport class DashboardsRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAe9C,OAAM,MAAOC,uBAAuB;EAAA,QAAAC,CAAA,G;qBAAvBD,uBAAuB;EAAA;EAAA,QAAAE,EAAA,G;UAAvBF;EAAuB;EAAA,QAAAG,EAAA,G;cAX5BJ,YAAY,CAACK,QAAQ,CAAC,CAClB;MACIC,IAAI,EAAE,MAAM;MACZC,YAAY,EAAEA,CAAA,KACV,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;KAC5D,EACD;MAAEJ,IAAI,EAAE,IAAI;MAAEK,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EAEIX,YAAY;EAAA;;;2EAEbC,uBAAuB;IAAAW,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFtBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}