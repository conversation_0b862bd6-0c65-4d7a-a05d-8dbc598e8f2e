{"ast": null, "code": "import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n  return joinAllInternals(zip, project);\n}", "map": {"version": 3, "names": ["zip", "joinAllInternals", "zipAll", "project"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/rxjs/dist/esm/internal/operators/zipAll.js"], "sourcesContent": ["import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n    return joinAllInternals(zip, project);\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,SAASC,MAAMA,CAACC,OAAO,EAAE;EAC5B,OAAOF,gBAAgB,CAACD,GAAG,EAAEG,OAAO,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}