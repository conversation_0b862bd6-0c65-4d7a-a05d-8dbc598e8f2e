{"ast": null, "code": "import { BehaviorSubject, fromEvent } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class WindowSizeService {\n  constructor() {\n    this.heightSubject = new BehaviorSubject(window.innerHeight);\n    this.height$ = this.heightSubject.asObservable();\n    this.scale = Number(localStorage.getItem('scale')) || 14;\n    this.heightOffsets = {\n      14: 346,\n      15: 366,\n      16: 391\n    };\n    this.calcHeight = this.heightOffsets[this.scale];\n    this.heightTableSubject = new BehaviorSubject(window.innerHeight - this.calcHeight);\n    fromEvent(window, 'resize').subscribe(() => {\n      this.heightSubject.next(window.innerHeight);\n    });\n  }\n  getHeight() {\n    return this.heightSubject.value;\n  }\n  getHeightTable(scale) {\n    this.heightTableSubject.next(window.innerHeight - this.heightOffsets[scale]);\n  }\n  static #_ = this.ɵfac = function WindowSizeService_Factory(t) {\n    return new (t || WindowSizeService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: WindowSizeService,\n    factory: WindowSizeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "fromEvent", "WindowSizeService", "constructor", "heightSubject", "window", "innerHeight", "height$", "asObservable", "scale", "Number", "localStorage", "getItem", "heightOffsets", "calcHeight", "heightTableSubject", "subscribe", "next", "getHeight", "value", "getHeightTable", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\window-size.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, fromEvent } from 'rxjs';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class WindowSizeService {\r\n    private heightSubject = new BehaviorSubject<number>(window.innerHeight);\r\n    height$ = this.heightSubject.asObservable();\r\n    scale = Number(localStorage.getItem('scale')) || 14;\r\n    heightOffsets = { 14: 346, 15: 366, 16: 391 };\r\n    calcHeight = this.heightOffsets[this.scale];\r\n    heightTableSubject = new BehaviorSubject<number>(window.innerHeight - this.calcHeight);\r\n    constructor() {\r\n        fromEvent(window, 'resize').subscribe(() => {\r\n            this.heightSubject.next(window.innerHeight);\r\n        });\r\n    }\r\n\r\n    getHeight(): number {\r\n        return this.heightSubject.value;\r\n    }\r\n\r\n    getHeightTable(scale) {\r\n        this.heightTableSubject.next(window.innerHeight - this.heightOffsets[scale]);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,EAAEC,SAAS,QAAQ,MAAM;;AAKjD,OAAM,MAAOC,iBAAiB;EAO1BC,YAAA;IANQ,KAAAC,aAAa,GAAG,IAAIJ,eAAe,CAASK,MAAM,CAACC,WAAW,CAAC;IACvE,KAAAC,OAAO,GAAG,IAAI,CAACH,aAAa,CAACI,YAAY,EAAE;IAC3C,KAAAC,KAAK,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE;IACnD,KAAAC,aAAa,GAAG;MAAE,EAAE,EAAE,GAAG;MAAE,EAAE,EAAE,GAAG;MAAE,EAAE,EAAE;IAAG,CAAE;IAC7C,KAAAC,UAAU,GAAG,IAAI,CAACD,aAAa,CAAC,IAAI,CAACJ,KAAK,CAAC;IAC3C,KAAAM,kBAAkB,GAAG,IAAIf,eAAe,CAASK,MAAM,CAACC,WAAW,GAAG,IAAI,CAACQ,UAAU,CAAC;IAElFb,SAAS,CAACI,MAAM,EAAE,QAAQ,CAAC,CAACW,SAAS,CAAC,MAAK;MACvC,IAAI,CAACZ,aAAa,CAACa,IAAI,CAACZ,MAAM,CAACC,WAAW,CAAC;IAC/C,CAAC,CAAC;EACN;EAEAY,SAASA,CAAA;IACL,OAAO,IAAI,CAACd,aAAa,CAACe,KAAK;EACnC;EAEAC,cAAcA,CAACX,KAAK;IAChB,IAAI,CAACM,kBAAkB,CAACE,IAAI,CAACZ,MAAM,CAACC,WAAW,GAAG,IAAI,CAACO,aAAa,CAACJ,KAAK,CAAC,CAAC;EAChF;EAAC,QAAAY,CAAA,G;qBAnBQnB,iBAAiB;EAAA;EAAA,QAAAoB,EAAA,G;WAAjBpB,iBAAiB;IAAAqB,OAAA,EAAjBrB,iBAAiB,CAAAsB,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}