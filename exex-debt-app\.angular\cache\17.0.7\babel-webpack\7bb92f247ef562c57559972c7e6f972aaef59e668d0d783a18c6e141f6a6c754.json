{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserRoutingModule } from './user-routing.module';\nimport { UserComponent } from './user.component';\nimport { AvatarModule } from 'primeng/avatar';\nimport * as i0 from \"@angular/core\";\nexport class UserModule {\n  static #_ = this.ɵfac = function UserModule_Factory(t) {\n    return new (t || UserModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UserModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, UserRoutingModule, AvatarModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserModule, {\n    declarations: [UserComponent],\n    imports: [CommonModule, UserRoutingModule, AvatarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UserRoutingModule", "UserComponent", "AvatarModule", "UserModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { UserRoutingModule } from './user-routing.module';\r\nimport { UserComponent } from './user.component';\r\nimport { AvatarModule } from 'primeng/avatar';\r\n@NgModule({\r\n    imports: [CommonModule, UserRoutingModule, AvatarModule],\r\n    declarations: [UserComponent],\r\n})\r\nexport class UserModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;;AAK7C,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAHTP,YAAY,EAAEC,iBAAiB,EAAEE,YAAY;EAAA;;;2EAG9CC,UAAU;IAAAI,YAAA,GAFJN,aAAa;IAAAO,OAAA,GADlBT,YAAY,EAAEC,iBAAiB,EAAEE,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}