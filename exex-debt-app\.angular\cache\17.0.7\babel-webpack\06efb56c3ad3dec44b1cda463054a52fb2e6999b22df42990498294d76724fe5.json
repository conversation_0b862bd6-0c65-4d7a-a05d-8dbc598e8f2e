{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { tap } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport { Path } from '../enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-spinner\";\nexport class AuthService {\n  constructor(http, spinner) {\n    this.http = http;\n    this.spinner = spinner;\n    this.redirectTo = inject(Router);\n    const token = localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (token) {\n      this.jwtToken = token;\n    }\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\n    return this.http.post(`/refresh`, {\n      refreshToken\n    }).pipe(tap(response => {\n      localStorage.setItem(Auth.ACCESS_TOKEN, response.access_token);\n    }));\n  }\n  isAuthenticated() {\n    return this.jwtToken != null;\n  }\n  redirectToDashboard() {\n    this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\n    this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_USER]);\n  }\n  login() {\n    this.spinner.show();\n    this.jwtToken = 'ponynguyen';\n    localStorage.setItem(Auth.ACCESS_TOKEN, this.jwtToken);\n    this.redirectTo.navigate([Path.DASHBOARD_USER]);\n    setTimeout(() => {\n      this.spinner.hide();\n    }, 1000);\n  }\n  logout() {\n    this.jwtToken = null;\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n    this.redirectTo.navigate([Path.AUTH_LOGIN]);\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "Router", "tap", "<PERSON><PERSON>", "Path", "AuthService", "constructor", "http", "spinner", "redirectTo", "token", "localStorage", "getItem", "ACCESS_TOKEN", "jwtToken", "refreshToken", "REFRESH_TOKEN", "post", "pipe", "response", "setItem", "access_token", "isAuthenticated", "redirectToDashboard", "navigate", "DASHBOARD_USER", "login", "show", "setTimeout", "hide", "logout", "removeItem", "AUTH_LOGIN", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "NgxSpinnerService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\service\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { inject, Injectable } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { NgxSpinnerService } from 'ngx-spinner';\nimport { Observable, tap } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport { Path } from '../enums/path.enum';\n\n@Injectable({\n    providedIn: 'root',\n})\nexport class AuthService {\n    private jwtToken;\n    redirectTo = inject(Router);\n\n    constructor(private http: HttpClient, private spinner: NgxSpinnerService) {\n        const token: any = localStorage.getItem(Auth.ACCESS_TOKEN);\n        if (token) {\n            this.jwtToken = token;\n        }\n    }\n\n    refreshToken(): Observable<any> {\n        const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\n        return this.http\n            .post<any>(`/refresh`, { refreshToken })\n            .pipe(\n                tap((response) => {\n                    localStorage.setItem(Auth.ACCESS_TOKEN, response.access_token);\n                })\n            );\n    }\n\n    public isAuthenticated(): boolean {\n        return this.jwtToken != null;\n    }\n\n    redirectToDashboard() {\n        this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\n        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_USER]);\n    }\n\n    login() {\n        this.spinner.show();\n        this.jwtToken = 'ponynguyen';\n        localStorage.setItem(Auth.ACCESS_TOKEN, this.jwtToken);\n        this.redirectTo.navigate([Path.DASHBOARD_USER]);\n        setTimeout(() => {\n            this.spinner.hide();\n        }, 1000);\n    }\n\n    logout() {\n        this.jwtToken = null;\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\n        this.redirectTo.navigate([Path.AUTH_LOGIN]);\n    }\n\n}\n"], "mappings": "AACA,SAASA,MAAM,QAAoB,eAAe;AAClD,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAAqBC,GAAG,QAAQ,MAAM;AACtC,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,oBAAoB;;;;AAKzC,OAAM,MAAOC,WAAW;EAIpBC,YAAoBC,IAAgB,EAAUC,OAA0B;IAApD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,OAAO,GAAPA,OAAO;IAFrD,KAAAC,UAAU,GAAGT,MAAM,CAACC,MAAM,CAAC;IAGvB,MAAMS,KAAK,GAAQC,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IAC1D,IAAIH,KAAK,EAAE;MACP,IAAI,CAACI,QAAQ,GAAGJ,KAAK;;EAE7B;EAEAK,YAAYA,CAAA;IACR,MAAMA,YAAY,GAAGJ,YAAY,CAACC,OAAO,CAACT,IAAI,CAACa,aAAa,CAAC;IAC7D,OAAO,IAAI,CAACT,IAAI,CACXU,IAAI,CAAM,UAAU,EAAE;MAAEF;IAAY,CAAE,CAAC,CACvCG,IAAI,CACDhB,GAAG,CAAEiB,QAAQ,IAAI;MACbR,YAAY,CAACS,OAAO,CAACjB,IAAI,CAACU,YAAY,EAAEM,QAAQ,CAACE,YAAY,CAAC;IAClE,CAAC,CAAC,CACL;EACT;EAEOC,eAAeA,CAAA;IAClB,OAAO,IAAI,CAACR,QAAQ,IAAI,IAAI;EAChC;EAEAS,mBAAmBA,CAAA;IACf,IAAI,CAACT,QAAQ,GAAGH,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IACvD,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACL,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAACqB,cAAc,CAAC,CAAC;EACpE;EAEAC,KAAKA,CAAA;IACD,IAAI,CAAClB,OAAO,CAACmB,IAAI,EAAE;IACnB,IAAI,CAACb,QAAQ,GAAG,YAAY;IAC5BH,YAAY,CAACS,OAAO,CAACjB,IAAI,CAACU,YAAY,EAAE,IAAI,CAACC,QAAQ,CAAC;IACtD,IAAI,CAACL,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAACqB,cAAc,CAAC,CAAC;IAC/CG,UAAU,CAAC,MAAK;MACZ,IAAI,CAACpB,OAAO,CAACqB,IAAI,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACZ;EAEAC,MAAMA,CAAA;IACF,IAAI,CAAChB,QAAQ,GAAG,IAAI;IACpBH,YAAY,CAACoB,UAAU,CAAC5B,IAAI,CAACU,YAAY,CAAC;IAC1CF,YAAY,CAACoB,UAAU,CAAC5B,IAAI,CAACa,aAAa,CAAC;IAC3C,IAAI,CAACP,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAAC4B,UAAU,CAAC,CAAC;EAC/C;EAAC,QAAAC,CAAA,G;qBA9CQ5B,WAAW,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXnC,WAAW;IAAAoC,OAAA,EAAXpC,WAAW,CAAAqC,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}