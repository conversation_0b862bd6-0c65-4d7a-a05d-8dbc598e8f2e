{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { BadgeModule } from 'primeng/badge';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { RippleModule } from 'primeng/ripple';\nimport { AppMenuComponent } from './app.menu.component';\nimport { AppMenuitemComponent } from './app.menuitem.component';\nimport { RouterModule } from '@angular/router';\nimport { AppTopBarComponent } from './app.topbar.component';\nimport { AppFooterComponent } from './app.footer.component';\nimport { AppConfigModule } from './config/config.module';\nimport { AppSidebarComponent } from \"./app.sidebar.component\";\nimport { AppLayoutComponent } from \"./app.layout.component\";\nimport { TranslocoModule } from '@jsverse/transloco';\nimport * as i0 from \"@angular/core\";\nexport class AppLayoutModule {\n  static #_ = this.ɵfac = function AppLayoutModule_Factory(t) {\n    return new (t || AppLayoutModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppLayoutModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, RippleModule, RouterModule, AppConfigModule, TranslocoModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppLayoutModule, {\n    declarations: [AppMenuitemComponent, AppTopBarComponent, AppFooterComponent, AppMenuComponent, AppSidebarComponent, AppLayoutComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, RippleModule, RouterModule, AppConfigModule, TranslocoModule],\n    exports: [AppLayoutComponent]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "HttpClientModule", "BrowserAnimationsModule", "InputTextModule", "SidebarModule", "BadgeModule", "RadioButtonModule", "InputSwitchModule", "RippleModule", "AppMenuComponent", "AppMenuitemComponent", "RouterModule", "AppTopBarComponent", "AppFooterComponent", "AppConfigModule", "AppSidebarComponent", "AppLayoutComponent", "TranslocoModule", "AppLayoutModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\layout\\app.layout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { BadgeModule } from 'primeng/badge';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { RippleModule } from 'primeng/ripple';\nimport { AppMenuComponent } from './app.menu.component';\nimport { AppMenuitemComponent } from './app.menuitem.component';\nimport { RouterModule } from '@angular/router';\nimport { AppTopBarComponent } from './app.topbar.component';\nimport { AppFooterComponent } from './app.footer.component';\nimport { AppConfigModule } from './config/config.module';\nimport { AppSidebarComponent } from \"./app.sidebar.component\";\nimport { AppLayoutComponent } from \"./app.layout.component\";\nimport { TranslocoModule } from '@jsverse/transloco';\n\n@NgModule({\n    declarations: [\n        AppMenuitemComponent,\n        AppTopBarComponent,\n        AppFooterComponent,\n        AppMenuComponent,\n        AppSidebarComponent,\n        AppLayoutComponent,\n    ],\n    imports: [\n        BrowserModule,\n        FormsModule,\n        HttpClientModule,\n        BrowserAnimationsModule,\n        InputTextModule,\n        SidebarModule,\n        BadgeModule,\n        RadioButtonModule,\n        InputSwitchModule,\n        RippleModule,\n        RouterModule,\n        AppConfigModule,\n        TranslocoModule\n    ],\n    exports: [AppLayoutComponent]\n})\nexport class AppLayoutModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,eAAe,QAAQ,oBAAoB;;AA4BpD,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAhBpBtB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,eAAe,EACfC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZG,YAAY,EACZG,eAAe,EACfG,eAAe;EAAA;;;2EAIVC,eAAe;IAAAI,YAAA,GAxBpBZ,oBAAoB,EACpBE,kBAAkB,EAClBC,kBAAkB,EAClBJ,gBAAgB,EAChBM,mBAAmB,EACnBC,kBAAkB;IAAAO,OAAA,GAGlBxB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,eAAe,EACfC,aAAa,EACbC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZG,YAAY,EACZG,eAAe,EACfG,eAAe;IAAAO,OAAA,GAETR,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}