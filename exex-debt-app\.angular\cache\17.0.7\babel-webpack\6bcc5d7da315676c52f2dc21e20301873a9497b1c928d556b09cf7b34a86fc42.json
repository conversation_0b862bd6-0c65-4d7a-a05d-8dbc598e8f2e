{"ast": null, "code": "//ng generate interceptor my-interceptor --skip-tests\nimport { inject } from '@angular/core';\nimport { AuthService } from '../service/auth.service';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    const authRequest = request.clone({\n      headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`)\n    });\n    return next.handle(authRequest);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["inject", "AuthService", "<PERSON><PERSON>", "AuthInterceptor", "constructor", "authService", "intercept", "request", "next", "authRequest", "clone", "headers", "set", "localStorage", "getItem", "ACCESS_TOKEN", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["//ng generate interceptor my-interceptor --skip-tests\r\n\r\nimport { inject, Injectable } from '@angular/core';\r\nimport {\r\n  HttpInterceptor,\r\n  HttpRequest,\r\n  HttpHandler,\r\n} from '@angular/common/http';\r\nimport { AuthService } from '../service/auth.service';\r\nimport { Auth } from '../enums/auth.enum';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n  authService = inject(AuthService);\r\n  intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n    const authRequest = request.clone({\r\n      headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`),\r\n    });\r\n    return next.handle(authRequest);\r\n  }\r\n}"], "mappings": "AAAA;AAEA,SAASA,MAAM,QAAoB,eAAe;AAMlD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,IAAI,QAAQ,oBAAoB;;AAGzC,OAAM,MAAOC,eAAe;EAD5BC,YAAA;IAEE,KAAAC,WAAW,GAAGL,MAAM,CAACC,WAAW,CAAC;;EACjCK,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD,MAAMC,WAAW,GAAGF,OAAO,CAACG,KAAK,CAAC;MAChCC,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACa,YAAY,CAAC,EAAE;KAClG,CAAC;IACF,OAAOP,IAAI,CAACQ,MAAM,CAACP,WAAW,CAAC;EACjC;EAAC,QAAAQ,CAAA,G;qBAPUd,eAAe;EAAA;EAAA,QAAAe,EAAA,G;WAAff,eAAe;IAAAgB,OAAA,EAAfhB,eAAe,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}