{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UserComponent } from './user.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: UserComponent\n}];\nexport class UserRoutingModule {\n  static #_ = this.ɵfac = function UserRoutingModule_Factory(t) {\n    return new (t || UserRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UserRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "UserComponent", "routes", "path", "component", "UserRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { UserComponent } from './user.component';\n\nconst routes: Routes = [{ path: '', component: UserComponent }];\n\n@NgModule({\n    imports: [RouterModule.forChild(routes)],\n    exports: [RouterModule],\n})\nexport class UserRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;;;AAEhD,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAa,CAAE,CAAC;AAM/D,OAAM,MAAOI,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAHhBR,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEbK,iBAAiB;IAAAK,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFhBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}