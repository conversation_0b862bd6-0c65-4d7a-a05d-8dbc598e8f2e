{"ast": null, "code": "import { Path } from '../core/enums/path.enum';\nexport const MENU_LIST = [{\n  label: 'Dashboard',\n  items: [{\n    label: 'Customer',\n    icon: 'pi pi-users',\n    routerLink: [Path.DASHBOARD_CUSTOMER]\n  }, {\n    label: 'Invoice',\n    icon: 'pi pi-file',\n    routerLink: [Path.DASHBOARD_INVOICE]\n  }, {\n    label: 'User',\n    icon: 'pi pi-user',\n    routerLink: [Path.DASHBOARD_USER]\n  }\n  // {\n  //     label: 'Submenu 1',\n  //     icon: 'pi pi-fw pi-bookmark',\n  //     items: [\n  //         {\n  //             label: 'Submenu 1.1',\n  //             icon: 'pi pi-fw pi-bookmark',\n  //             items: [\n  //                 {\n  //                     label: 'Submenu 1.1.1',\n  //                     icon: 'pi pi-fw pi-bookmark',\n  //                     routerLink: ['/uikit/misc'],\n  //                 },\n  //                 {\n  //                     label: 'Submenu 1.1.2',\n  //                     icon: 'pi pi-fw pi-bookmark',\n  //                 },\n  //                 {\n  //                     label: 'Submenu 1.1.3',\n  //                     icon: 'pi pi-fw pi-bookmark',\n  //                 },\n  //             ],\n  //         },\n  //         {\n  //             label: 'Submenu 1.2',\n  //             icon: 'pi pi-fw pi-bookmark',\n  //             items: [\n  //                 {\n  //                     label: 'Submenu 1.2.1',\n  //                     icon: 'pi pi-fw pi-bookmark',\n  //                 },\n  //             ],\n  //         },\n  //     ],\n  // },\n  ]\n}];", "map": {"version": 3, "names": ["Path", "MENU_LIST", "label", "items", "icon", "routerLink", "DASHBOARD_CUSTOMER", "DASHBOARD_INVOICE", "DASHBOARD_USER"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\menu-list.ts"], "sourcesContent": ["import { Path } from '../core/enums/path.enum';\r\n\r\nexport const MENU_LIST = [\r\n    {\r\n        label: 'Dashboard',\r\n        items: [\r\n            {\r\n                label: 'Customer',\r\n                icon: 'pi pi-users',\r\n                routerLink: [Path.DASHBOARD_CUSTOMER],\r\n            },\r\n            {\r\n                label: 'Invoice',\r\n                icon: 'pi pi-file',\r\n                routerLink: [Path.DASHBOARD_INVOICE],\r\n            },\r\n            {\r\n                label: 'User',\r\n                icon: 'pi pi-user',\r\n                routerLink: [Path.DASHBOARD_USER],\r\n            },\r\n            // {\r\n            //     label: 'Submenu 1',\r\n            //     icon: 'pi pi-fw pi-bookmark',\r\n            //     items: [\r\n            //         {\r\n            //             label: 'Submenu 1.1',\r\n            //             icon: 'pi pi-fw pi-bookmark',\r\n            //             items: [\r\n            //                 {\r\n            //                     label: 'Submenu 1.1.1',\r\n            //                     icon: 'pi pi-fw pi-bookmark',\r\n            //                     routerLink: ['/uikit/misc'],\r\n            //                 },\r\n            //                 {\r\n            //                     label: 'Submenu 1.1.2',\r\n            //                     icon: 'pi pi-fw pi-bookmark',\r\n            //                 },\r\n            //                 {\r\n            //                     label: 'Submenu 1.1.3',\r\n            //                     icon: 'pi pi-fw pi-bookmark',\r\n            //                 },\r\n            //             ],\r\n            //         },\r\n            //         {\r\n            //             label: 'Submenu 1.2',\r\n            //             icon: 'pi pi-fw pi-bookmark',\r\n            //             items: [\r\n            //                 {\r\n            //                     label: 'Submenu 1.2.1',\r\n            //                     icon: 'pi pi-fw pi-bookmark',\r\n            //                 },\r\n            //             ],\r\n            //         },\r\n            //     ],\r\n            // },\r\n        ],\r\n    },\r\n];\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAyB;AAE9C,OAAO,MAAMC,SAAS,GAAG,CACrB;EACIC,KAAK,EAAE,WAAW;EAClBC,KAAK,EAAE,CACH;IACID,KAAK,EAAE,UAAU;IACjBE,IAAI,EAAE,aAAa;IACnBC,UAAU,EAAE,CAACL,IAAI,CAACM,kBAAkB;GACvC,EACD;IACIJ,KAAK,EAAE,SAAS;IAChBE,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE,CAACL,IAAI,CAACO,iBAAiB;GACtC,EACD;IACIL,KAAK,EAAE,MAAM;IACbE,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE,CAACL,IAAI,CAACQ,cAAc;;EAEpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;CAEP,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}