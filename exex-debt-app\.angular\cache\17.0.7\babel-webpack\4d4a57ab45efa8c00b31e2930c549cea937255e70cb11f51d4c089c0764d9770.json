{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { AppConfigComponent } from './app.config.component';\nimport * as i0 from \"@angular/core\";\nexport class AppConfigModule {\n  static #_ = this.ɵfac = function AppConfigModule_Factory(t) {\n    return new (t || AppConfigModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppConfigModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppConfigModule, {\n    declarations: [AppConfigComponent],\n    imports: [CommonModule, FormsModule, SidebarModule, RadioButtonModule, ButtonModule, InputSwitchModule],\n    exports: [AppConfigComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "SidebarModule", "RadioButtonModule", "ButtonModule", "InputSwitchModule", "AppConfigComponent", "AppConfigModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\config\\config.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { AppConfigComponent } from './app.config.component';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        SidebarModule,\r\n        RadioButtonModule,\r\n        ButtonModule,\r\n        InputSwitchModule\r\n    ],\r\n    declarations: [\r\n        AppConfigComponent\r\n    ],\r\n    exports: [\r\n        AppConfigComponent\r\n    ]\r\n})\r\nexport class AppConfigModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,kBAAkB,QAAQ,wBAAwB;;AAkB3D,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAdpBV,YAAY,EACZC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB;EAAA;;;2EASZE,eAAe;IAAAI,YAAA,GANpBL,kBAAkB;IAAAM,OAAA,GARlBZ,YAAY,EACZC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,iBAAiB;IAAAQ,OAAA,GAMjBP,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}