import { AuthService } from '@app/core/services/auth.service';
//ng generate interceptor my-interceptor --skip-tests

import { inject, Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';
import { Auth } from '../enums/auth.enum';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    intercept(request: HttpRequest<any>, next: HttpHand<PERSON>) {
        const authRequest = request.clone({
            headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`),
        });
        return next.handle(authRequest);
    }
}
