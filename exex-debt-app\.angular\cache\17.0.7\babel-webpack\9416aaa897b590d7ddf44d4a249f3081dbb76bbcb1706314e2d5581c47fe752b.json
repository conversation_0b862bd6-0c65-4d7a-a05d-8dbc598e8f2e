{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthRoutingModule {\n  static #_ = this.ɵfac = function AuthRoutingModule_Factory(t) {\n    return new (t || AuthRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'login',\n      loadChildren: () => import('./login/login.module').then(m => m.LoginModule)\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AuthRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "loadChildren", "then", "m", "LoginModule", "redirectTo", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\auth-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {\r\n                path: 'login',\r\n                loadChildren: () =>\r\n                    import('./login/login.module').then((m) => m.LoginModule),\r\n            },\r\n            { path: '**', redirectTo: '/notfound' },\r\n        ]),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\nexport class AuthRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAe9C,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAXtBJ,YAAY,CAACK,QAAQ,CAAC,CAClB;MACIC,IAAI,EAAE,OAAO;MACbC,YAAY,EAAEA,CAAA,KACV,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW;KAC/D,EACD;MAAEJ,IAAI,EAAE,IAAI;MAAEK,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EAEIX,YAAY;EAAA;;;2EAEbC,iBAAiB;IAAAW,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFhBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}