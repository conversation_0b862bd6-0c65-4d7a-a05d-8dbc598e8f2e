{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomerComponent } from './customer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomerComponent\n}];\nexport class CustomerRoutingModule {\n  static #_ = this.ɵfac = function CustomerRoutingModule_Factory(t) {\n    return new (t || CustomerRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CustomerRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomerComponent", "routes", "path", "component", "CustomerRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CustomerComponent } from './customer.component';\r\n\r\nconst routes: Routes = [{ path: '', component: CustomerComponent }];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule],\r\n})\r\nexport class CustomerRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;;;AAExD,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAiB,CAAE,CAAC;AAMnE,OAAM,MAAOI,qBAAqB;EAAA,QAAAC,CAAA,G;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA,G;UAArBF;EAAqB;EAAA,QAAAG,EAAA,G;cAHpBR,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEbK,qBAAqB;IAAAK,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFpBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}