import { AuthService } from '@app/core/services/auth.service';
import { inject, Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    intercept(request: HttpRequest<any>, next: HttpHandler) {
        return next.handle(request).pipe(
            catchError((error: HttpErrorResponse) => {
                // Handle and log the error here
                console.error('HTTP Error:', error);
                // Optionally rethrow the error to propagate it

                // if (error) {
                //     if (error.status === 401 || error.status === 403) {
                //         this.authService.logout();
                //     }

                //     if (error.status === 429) {
                //         alert(
                //             'Something went wrong, please try again after a few seconds!'
                //         );
                //     }
                // }

                return throwError(error);
            }),
        );
    }
}
