{"ast": null, "code": "/**\n * @license Angular v17.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, ɵRuntimeError, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toObservable` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n\n  if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { takeUntilDestroyed, toObservable, toSignal };", "map": {"version": 3, "names": ["assertInInjectionContext", "inject", "DestroyRef", "Injector", "effect", "untracked", "assertNotInReactiveContext", "signal", "ɵRuntimeError", "computed", "Observable", "ReplaySubject", "takeUntil", "takeUntilDestroyed", "destroyRef", "destroyed$", "observer", "unregisterFn", "onDestroy", "next", "bind", "source", "pipe", "toObservable", "options", "injector", "subject", "watcher", "value", "err", "error", "manualCleanup", "get", "destroy", "complete", "asObservable", "toSignal", "ngDevMode", "requiresCleanup", "cleanupRef", "state", "requireSync", "kind", "initialValue", "sub", "subscribe", "set", "rejectErrors", "unsubscribe", "current"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v17.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, ɵRuntimeError, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable(observer => {\n        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n    !options?.injector && assertInInjectionContext(toObservable);\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toObservable` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });\n    }\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: value => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: error => {\n            if (options?.rejectErrors) {\n                // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n                // the error to end up as an uncaught exception.\n                throw error;\n            }\n            state.set({ kind: 2 /* StateKind.Error */, error });\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n                throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { takeUntilDestroyed, toObservable, toSignal };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,wBAAwB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,eAAe;AACtK,SAASC,UAAU,EAAEC,aAAa,QAAQ,MAAM;AAChD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EACpC,IAAI,CAACA,UAAU,EAAE;IACbd,wBAAwB,CAACa,kBAAkB,CAAC;IAC5CC,UAAU,GAAGb,MAAM,CAACC,UAAU,CAAC;EACnC;EACA,MAAMa,UAAU,GAAG,IAAIL,UAAU,CAACM,QAAQ,IAAI;IAC1C,MAAMC,YAAY,GAAGH,UAAU,CAACI,SAAS,CAACF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACvE,OAAOC,YAAY;EACvB,CAAC,CAAC;EACF,OAAQI,MAAM,IAAK;IACf,OAAOA,MAAM,CAACC,IAAI,CAACV,SAAS,CAACG,UAAU,CAAC,CAAC;EAC7C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAACF,MAAM,EAAEG,OAAO,EAAE;EACnC,CAACA,OAAO,EAAEC,QAAQ,IAAIzB,wBAAwB,CAACuB,YAAY,CAAC;EAC5D,MAAME,QAAQ,GAAGD,OAAO,EAAEC,QAAQ,IAAIxB,MAAM,CAACE,QAAQ,CAAC;EACtD,MAAMuB,OAAO,GAAG,IAAIf,aAAa,CAAC,CAAC,CAAC;EACpC,MAAMgB,OAAO,GAAGvB,MAAM,CAAC,MAAM;IACzB,IAAIwB,KAAK;IACT,IAAI;MACAA,KAAK,GAAGP,MAAM,CAAC,CAAC;IACpB,CAAC,CACD,OAAOQ,GAAG,EAAE;MACRxB,SAAS,CAAC,MAAMqB,OAAO,CAACI,KAAK,CAACD,GAAG,CAAC,CAAC;MACnC;IACJ;IACAxB,SAAS,CAAC,MAAMqB,OAAO,CAACP,IAAI,CAACS,KAAK,CAAC,CAAC;EACxC,CAAC,EAAE;IAAEH,QAAQ;IAAEM,aAAa,EAAE;EAAK,CAAC,CAAC;EACrCN,QAAQ,CAACO,GAAG,CAAC9B,UAAU,CAAC,CAACgB,SAAS,CAAC,MAAM;IACrCS,OAAO,CAACM,OAAO,CAAC,CAAC;IACjBP,OAAO,CAACQ,QAAQ,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAOR,OAAO,CAACS,YAAY,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACf,MAAM,EAAEG,OAAO,EAAE;EAC/Ba,SAAS,IACL/B,0BAA0B,CAAC8B,QAAQ,EAAE,2DAA2D,GAC5F,oGAAoG,CAAC;EAC7G,MAAME,eAAe,GAAG,CAACd,OAAO,EAAEO,aAAa;EAC/CO,eAAe,IAAI,CAACd,OAAO,EAAEC,QAAQ,IAAIzB,wBAAwB,CAACoC,QAAQ,CAAC;EAC3E,MAAMG,UAAU,GAAGD,eAAe,GAAGd,OAAO,EAAEC,QAAQ,EAAEO,GAAG,CAAC9B,UAAU,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,GAAG,IAAI;EACpG;EACA;EACA,IAAIsC,KAAK;EACT,IAAIhB,OAAO,EAAEiB,WAAW,EAAE;IACtB;IACAD,KAAK,GAAGjC,MAAM,CAAC;MAAEmC,IAAI,EAAE,CAAC,CAAC;IAAwB,CAAC,CAAC;EACvD,CAAC,MACI;IACD;IACAF,KAAK,GAAGjC,MAAM,CAAC;MAAEmC,IAAI,EAAE,CAAC,CAAC;MAAuBd,KAAK,EAAEJ,OAAO,EAAEmB;IAAa,CAAC,CAAC;EACnF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,GAAG,GAAGvB,MAAM,CAACwB,SAAS,CAAC;IACzB1B,IAAI,EAAES,KAAK,IAAIY,KAAK,CAACM,GAAG,CAAC;MAAEJ,IAAI,EAAE,CAAC,CAAC;MAAuBd;IAAM,CAAC,CAAC;IAClEE,KAAK,EAAEA,KAAK,IAAI;MACZ,IAAIN,OAAO,EAAEuB,YAAY,EAAE;QACvB;QACA;QACA,MAAMjB,KAAK;MACf;MACAU,KAAK,CAACM,GAAG,CAAC;QAAEJ,IAAI,EAAE,CAAC,CAAC;QAAuBZ;MAAM,CAAC,CAAC;IACvD;IACA;IACA;EACJ,CAAC,CAAC;;EACF,IAAIO,SAAS,IAAIb,OAAO,EAAEiB,WAAW,IAAID,KAAK,CAAC,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,yBAAyB;IACjF,MAAM,IAAIlC,aAAa,CAAC,GAAG,CAAC,wDAAwD,qFAAqF,CAAC;EAC9K;EACA;EACA+B,UAAU,EAAErB,SAAS,CAAC0B,GAAG,CAACI,WAAW,CAAC5B,IAAI,CAACwB,GAAG,CAAC,CAAC;EAChD;EACA;EACA,OAAOnC,QAAQ,CAAC,MAAM;IAClB,MAAMwC,OAAO,GAAGT,KAAK,CAAC,CAAC;IACvB,QAAQS,OAAO,CAACP,IAAI;MAChB,KAAK,CAAC,CAAC;QACH,OAAOO,OAAO,CAACrB,KAAK;MACxB,KAAK,CAAC,CAAC;QACH,MAAMqB,OAAO,CAACnB,KAAK;MACvB,KAAK,CAAC,CAAC;QACH;QACA;QACA,MAAM,IAAItB,aAAa,CAAC,GAAG,CAAC,wDAAwD,qFAAqF,CAAC;IAClL;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;;AAEA,SAASK,kBAAkB,EAAEU,YAAY,EAAEa,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}