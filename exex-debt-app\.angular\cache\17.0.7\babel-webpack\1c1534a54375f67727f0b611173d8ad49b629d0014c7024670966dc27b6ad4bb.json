{"ast": null, "code": "import { catchError, switchMap } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nexport class JwtRefreshInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      if (error.status === 401 && error.error && error.error.message === 'Token expired') {\n        // Token expired; attempt to refresh it\n        return this.authService.refreshToken().pipe(switchMap(() => {\n          // Retry the original request with the new token\n          const updatedRequest = request.clone({\n            setHeaders: {\n              Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`\n            }\n          });\n          return next.handle(updatedRequest);\n        }), catchError(() => {\n          // Refresh token failed; log out the user or handle the error\n          // For example, you can redirect to the login page\n          this.authService.logout();\n          return throwError('Token refresh failed');\n        }));\n      }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function JwtRefreshInterceptor_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtRefreshInterceptor,\n    factory: JwtRefreshInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["catchError", "switchMap", "throwError", "<PERSON><PERSON>", "JwtRefreshInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "status", "message", "refreshToken", "updatedRequest", "clone", "setHeaders", "Authorization", "localStorage", "getItem", "ACCESS_TOKEN", "logout", "_", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\jwt-refresh.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { catchError, switchMap } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\nimport { AuthService } from '../service/auth.service';\r\nimport { Auth } from '../enums/auth.enum';\r\n\r\n@Injectable()\r\nexport class JwtRefreshInterceptor implements HttpInterceptor {\r\n    constructor(private authService: AuthService) {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        return next.handle(request).pipe(\r\n            catchError((error: HttpErrorResponse) => {\r\n                if (error.status === 401 && error.error && error.error.message === 'Token expired') {\r\n                    // Token expired; attempt to refresh it\r\n                    return this.authService.refreshToken().pipe(\r\n                        switchMap(() => {\r\n                            // Retry the original request with the new token\r\n                            const updatedRequest = request.clone({\r\n                                setHeaders: {\r\n                                    Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`,\r\n                                },\r\n                            });\r\n                            return next.handle(updatedRequest);\r\n                        }),\r\n                        catchError(() => {\r\n                            // Refresh token failed; log out the user or handle the error\r\n                            // For example, you can redirect to the login page\r\n                            this.authService.logout();\r\n                            return throwError('Token refresh failed');\r\n                        }),\r\n                    );\r\n                }\r\n                return throwError(error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,SAASC,UAAU,QAAQ,MAAM;AAEjC,SAASC,IAAI,QAAQ,oBAAoB;;AAGzC,OAAM,MAAOC,qBAAqB;EAC9BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BX,UAAU,CAAEY,KAAwB,IAAI;MACpC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACE,OAAO,KAAK,eAAe,EAAE;QAChF;QACA,OAAO,IAAI,CAACR,WAAW,CAACS,YAAY,EAAE,CAACJ,IAAI,CACvCV,SAAS,CAAC,MAAK;UACX;UACA,MAAMe,cAAc,GAAGR,OAAO,CAACS,KAAK,CAAC;YACjCC,UAAU,EAAE;cACRC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAClB,IAAI,CAACmB,YAAY,CAAC;;WAEvE,CAAC;UACF,OAAOb,IAAI,CAACC,MAAM,CAACM,cAAc,CAAC;QACtC,CAAC,CAAC,EACFhB,UAAU,CAAC,MAAK;UACZ;UACA;UACA,IAAI,CAACM,WAAW,CAACiB,MAAM,EAAE;UACzB,OAAOrB,UAAU,CAAC,sBAAsB,CAAC;QAC7C,CAAC,CAAC,CACL;;MAEL,OAAOA,UAAU,CAACU,KAAK,CAAC;IAC5B,CAAC,CAAC,CACL;EACL;EAAC,QAAAY,CAAA,G;;;;WA7BQpB,qBAAqB;IAAAqB,OAAA,EAArBrB,qBAAqB,CAAAsB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}