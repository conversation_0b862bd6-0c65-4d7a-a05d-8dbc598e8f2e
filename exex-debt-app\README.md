https://jsverse.github.io/transloco/docs/translation-in-the-template

https://sakai.primeng.org/

# Rule format code
install extension Prettier - Code formatter

# ver6
add full Interceptors 

# syntax create Interceptors
ng generate interceptor my-Interceptors --skip-tests

# syntax create component have routing && module
ng g m customer --routing=true && ng g c customer --skip-tests=true -m=customer --standalone=false

# Using node >= 20
# Code Formatter: Pretty
# Tham khảo Component User để tạo Component mới
# Remember run: npm run format befor push code