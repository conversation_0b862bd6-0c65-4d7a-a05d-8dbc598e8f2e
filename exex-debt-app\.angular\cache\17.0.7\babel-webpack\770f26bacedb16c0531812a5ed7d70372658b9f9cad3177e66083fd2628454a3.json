{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\n//ng generate interceptor my-interceptor --skip-tests\nimport { inject } from '@angular/core';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    const authRequest = request.clone({\n      headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`)\n    });\n    return next.handle(authRequest);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthService", "inject", "<PERSON><PERSON>", "AuthInterceptor", "constructor", "authService", "intercept", "request", "next", "authRequest", "clone", "headers", "set", "localStorage", "getItem", "ACCESS_TOKEN", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\n//ng generate interceptor my-interceptor --skip-tests\r\n\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';\r\nimport { Auth } from '../enums/auth.enum';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n    authService = inject(AuthService);\r\n    intercept(request: HttpRequest<any>, next: HttpHand<PERSON>) {\r\n        const authRequest = request.clone({\r\n            headers: request.headers.set('Authorization', `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`),\r\n        });\r\n        return next.handle(authRequest);\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D;AAEA,SAASC,MAAM,QAAoB,eAAe;AAElD,SAASC,IAAI,QAAQ,oBAAoB;;AAGzC,OAAM,MAAOC,eAAe;EAD5BC,YAAA;IAEI,KAAAC,WAAW,GAAGJ,MAAM,CAACD,WAAW,CAAC;;EACjCM,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,MAAMC,WAAW,GAAGF,OAAO,CAACG,KAAK,CAAC;MAC9BC,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACa,YAAY,CAAC,EAAE;KACpG,CAAC;IACF,OAAOP,IAAI,CAACQ,MAAM,CAACP,WAAW,CAAC;EACnC;EAAC,QAAAQ,CAAA,G;qBAPQd,eAAe;EAAA;EAAA,QAAAe,EAAA,G;WAAff,eAAe;IAAAgB,OAAA,EAAfhB,eAAe,CAAAiB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}