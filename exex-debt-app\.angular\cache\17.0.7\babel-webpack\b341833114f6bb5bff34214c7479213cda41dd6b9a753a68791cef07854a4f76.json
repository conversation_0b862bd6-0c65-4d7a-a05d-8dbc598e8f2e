{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Pipe, Component, ChangeDetectionStrategy, Optional, Inject, Input, ViewChild, NgModule, makeEnvironmentProviders } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/platform-browser';\nimport { NgIf, NgFor, CommonModule } from '@angular/common';\nconst _c0 = [\"overlay\"];\nfunction NgxSpinnerComponent_div_0_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\nfunction NgxSpinnerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NgxSpinnerComponent_div_0_div_2_div_1_Template, 1, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.spinner.class);\n    i0.ɵɵstyleProp(\"color\", ctx_r2.spinner.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.spinner.divArray);\n  }\n}\nfunction NgxSpinnerComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, ctx_r3.template), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NgxSpinnerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵtemplate(2, NgxSpinnerComponent_div_0_div_2_Template, 2, 5, \"div\", 3)(3, NgxSpinnerComponent_div_0_div_3_Template, 2, 3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵprojection(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r0.spinner.bdColor)(\"z-index\", ctx_r0.spinner.zIndex)(\"position\", ctx_r0.spinner.fullScreen ? \"fixed\" : \"absolute\");\n    i0.ɵɵproperty(\"@.disabled\", ctx_r0.disableAnimation)(\"@fadeIn\", \"in\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.template);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.template);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"z-index\", ctx_r0.spinner.zIndex);\n  }\n}\nconst _c1 = [\"*\"];\nconst LOADERS = {\n  \"ball-8bits\": 16,\n  \"ball-atom\": 4,\n  \"ball-beat\": 3,\n  \"ball-circus\": 5,\n  \"ball-climbing-dot\": 4,\n  \"ball-clip-rotate\": 1,\n  \"ball-clip-rotate-multiple\": 2,\n  \"ball-clip-rotate-pulse\": 2,\n  \"ball-elastic-dots\": 5,\n  \"ball-fall\": 3,\n  \"ball-fussion\": 4,\n  \"ball-grid-beat\": 9,\n  \"ball-grid-pulse\": 9,\n  \"ball-newton-cradle\": 4,\n  \"ball-pulse\": 3,\n  \"ball-pulse-rise\": 5,\n  \"ball-pulse-sync\": 3,\n  \"ball-rotate\": 1,\n  \"ball-running-dots\": 5,\n  \"ball-scale\": 1,\n  \"ball-scale-multiple\": 3,\n  \"ball-scale-pulse\": 2,\n  \"ball-scale-ripple\": 1,\n  \"ball-scale-ripple-multiple\": 3,\n  \"ball-spin\": 8,\n  \"ball-spin-clockwise\": 8,\n  \"ball-spin-clockwise-fade\": 8,\n  \"ball-spin-clockwise-fade-rotating\": 8,\n  \"ball-spin-fade\": 8,\n  \"ball-spin-fade-rotating\": 8,\n  \"ball-spin-rotate\": 2,\n  \"ball-square-clockwise-spin\": 8,\n  \"ball-square-spin\": 8,\n  \"ball-triangle-path\": 3,\n  \"ball-zig-zag\": 2,\n  \"ball-zig-zag-deflect\": 2,\n  cog: 1,\n  \"cube-transition\": 2,\n  fire: 3,\n  \"line-scale\": 5,\n  \"line-scale-party\": 5,\n  \"line-scale-pulse-out\": 5,\n  \"line-scale-pulse-out-rapid\": 5,\n  \"line-spin-clockwise-fade\": 8,\n  \"line-spin-clockwise-fade-rotating\": 8,\n  \"line-spin-fade\": 8,\n  \"line-spin-fade-rotating\": 8,\n  pacman: 6,\n  \"square-jelly-box\": 2,\n  \"square-loader\": 1,\n  \"square-spin\": 1,\n  timer: 1,\n  \"triangle-skew-spin\": 1\n};\nconst DEFAULTS = {\n  BD_COLOR: \"rgba(51,51,51,0.8)\",\n  SPINNER_COLOR: \"#fff\",\n  Z_INDEX: 99999\n};\nconst PRIMARY_SPINNER = \"primary\";\nclass NgxSpinner {\n  constructor(init) {\n    Object.assign(this, init);\n  }\n  static create(init) {\n    if (!init?.template && !init?.type) {\n      console.warn(`[ngx-spinner]: Property \"type\" is missed. Please, provide animation type to <ngx-spinner> component\n        and ensure css is added to angular.json file`);\n    }\n    return new NgxSpinner(init);\n  }\n}\nclass NgxSpinnerService {\n  /**\n   * Creates an instance of NgxSpinnerService.\n   * @memberof NgxSpinnerService\n   */\n  constructor() {\n    /**\n     * Spinner observable\n     *\n     * @memberof NgxSpinnerService\n     */\n    // private spinnerObservable = new ReplaySubject<NgxSpinner>(1);\n    this.spinnerObservable = new BehaviorSubject(null);\n  }\n  /**\n   * Get subscription of desired spinner\n   * @memberof NgxSpinnerService\n   **/\n  getSpinner(name) {\n    return this.spinnerObservable.asObservable().pipe(filter(x => x && x.name === name));\n  }\n  /**\n   * To show spinner\n   *\n   * @memberof NgxSpinnerService\n   */\n  show(name = PRIMARY_SPINNER, spinner) {\n    return new Promise((resolve, _reject) => {\n      setTimeout(() => {\n        if (spinner && Object.keys(spinner).length) {\n          spinner[\"name\"] = name;\n          this.spinnerObservable.next(new NgxSpinner({\n            ...spinner,\n            show: true\n          }));\n          resolve(true);\n        } else {\n          this.spinnerObservable.next(new NgxSpinner({\n            name,\n            show: true\n          }));\n          resolve(true);\n        }\n      }, 10);\n    });\n  }\n  /**\n   * To hide spinner\n   *\n   * @memberof NgxSpinnerService\n   */\n  hide(name = PRIMARY_SPINNER, debounce = 10) {\n    return new Promise((resolve, _reject) => {\n      setTimeout(() => {\n        this.spinnerObservable.next(new NgxSpinner({\n          name,\n          show: false\n        }));\n        resolve(true);\n      }, debounce);\n    });\n  }\n  static #_ = this.ɵfac = function NgxSpinnerService_Factory(t) {\n    return new (t || NgxSpinnerService)();\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxSpinnerService,\n    factory: NgxSpinnerService.ɵfac,\n    providedIn: \"root\"\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: \"root\"\n    }]\n  }], () => [], null);\n})();\nconst NGX_SPINNER_CONFIG = new InjectionToken(\"NGX_SPINNER_CONFIG\");\nclass SafeHtmlPipe {\n  constructor(_sanitizer) {\n    this._sanitizer = _sanitizer;\n  }\n  transform(v) {\n    if (v) {\n      return this._sanitizer.bypassSecurityTrustHtml(v);\n    }\n  }\n  static #_ = this.ɵfac = function SafeHtmlPipe_Factory(t) {\n    return new (t || SafeHtmlPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n  };\n  static #_2 = this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"safeHtml\",\n    type: SafeHtmlPipe,\n    pure: true,\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Pipe,\n    args: [{\n      name: \"safeHtml\",\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }], null);\n})();\nclass NgxSpinnerComponent {\n  // TODO: https://github.com/Napster2210/ngx-spinner/issues/259\n  // @HostListener(\"document:keydown\", [\"$event\"])\n  // handleKeyboardEvent(event: KeyboardEvent) {\n  //   if (this.spinnerDOM && this.spinnerDOM.nativeElement) {\n  //     if (\n  //       this.fullScreen ||\n  //       (!this.fullScreen && this.isSpinnerZone(event.target))\n  //     ) {\n  //       event.returnValue = false;\n  //       event.preventDefault();\n  //     }\n  //   }\n  // }\n  /**\n   * Creates an instance of NgxSpinnerComponent.\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  constructor(spinnerService, changeDetector, elementRef, globalConfig) {\n    this.spinnerService = spinnerService;\n    this.changeDetector = changeDetector;\n    this.elementRef = elementRef;\n    this.globalConfig = globalConfig;\n    /**\n     * To enable/disable animation\n     *\n     * @type {boolean}\n     * @memberof NgxSpinnerComponent\n     */\n    this.disableAnimation = false;\n    /**\n     * Spinner Object\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    this.spinner = new NgxSpinner();\n    /**\n     * Unsubscribe from spinner's observable\n     *\n     * @memberof NgxSpinnerComponent\n     **/\n    this.ngUnsubscribe = new Subject();\n    /**\n     * To set default ngx-spinner options\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    this.setDefaultOptions = () => {\n      const {\n        type\n      } = this.globalConfig ?? {};\n      this.spinner = NgxSpinner.create({\n        name: this.name,\n        bdColor: this.bdColor,\n        size: this.size,\n        color: this.color,\n        type: this.type ?? type,\n        fullScreen: this.fullScreen,\n        divArray: this.divArray,\n        divCount: this.divCount,\n        show: this.show,\n        zIndex: this.zIndex,\n        template: this.template,\n        showSpinner: this.showSpinner\n      });\n    };\n    this.bdColor = DEFAULTS.BD_COLOR;\n    this.zIndex = DEFAULTS.Z_INDEX;\n    this.color = DEFAULTS.SPINNER_COLOR;\n    this.size = \"large\";\n    this.fullScreen = true;\n    this.name = PRIMARY_SPINNER;\n    this.template = null;\n    this.showSpinner = false;\n    this.divArray = [];\n    this.divCount = 0;\n    this.show = false;\n  }\n  initObservable() {\n    this.spinnerService.getSpinner(this.name).pipe(takeUntil(this.ngUnsubscribe)).subscribe(spinner => {\n      this.setDefaultOptions();\n      Object.assign(this.spinner, spinner);\n      if (spinner.show) {\n        this.onInputChange();\n      }\n      this.changeDetector.detectChanges();\n    });\n  }\n  /**\n   * Initialization method\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  ngOnInit() {\n    this.setDefaultOptions();\n    this.initObservable();\n  }\n  /**\n   * To check event triggers inside the Spinner Zone\n   *\n   * @param {*} element\n   * @returns {boolean}\n   * @memberof NgxSpinnerComponent\n   */\n  isSpinnerZone(element) {\n    if (element === this.elementRef.nativeElement.parentElement) {\n      return true;\n    }\n    return element.parentNode && this.isSpinnerZone(element.parentNode);\n  }\n  /**\n   * On changes event for input variables\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  ngOnChanges(changes) {\n    for (const propName in changes) {\n      if (propName) {\n        const changedProp = changes[propName];\n        if (changedProp.isFirstChange()) {\n          return;\n        } else if (typeof changedProp.currentValue !== \"undefined\" && changedProp.currentValue !== changedProp.previousValue) {\n          if (changedProp.currentValue !== \"\") {\n            this.spinner[propName] = changedProp.currentValue;\n            if (propName === \"showSpinner\") {\n              if (changedProp.currentValue) {\n                this.spinnerService.show(this.spinner.name, this.spinner);\n              } else {\n                this.spinnerService.hide(this.spinner.name);\n              }\n            }\n            if (propName === \"name\") {\n              this.initObservable();\n            }\n          }\n        }\n      }\n    }\n  }\n  /**\n   * To get class for spinner\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  getClass(type, size) {\n    this.spinner.divCount = LOADERS[type];\n    this.spinner.divArray = Array(this.spinner.divCount).fill(0).map((_, i) => i);\n    let sizeClass = \"\";\n    switch (size.toLowerCase()) {\n      case \"small\":\n        sizeClass = \"la-sm\";\n        break;\n      case \"medium\":\n        sizeClass = \"la-2x\";\n        break;\n      case \"large\":\n        sizeClass = \"la-3x\";\n        break;\n      default:\n        break;\n    }\n    return \"la-\" + type + \" \" + sizeClass;\n  }\n  /**\n   * Check if input variables have changed\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  onInputChange() {\n    this.spinner.class = this.getClass(this.spinner.type, this.spinner.size);\n  }\n  /**\n   * Component destroy event\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static #_ = this.ɵfac = function NgxSpinnerComponent_Factory(t) {\n    return new (t || NgxSpinnerComponent)(i0.ɵɵdirectiveInject(NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NGX_SPINNER_CONFIG, 8));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: NgxSpinnerComponent,\n    selectors: [[\"ngx-spinner\"]],\n    viewQuery: function NgxSpinnerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.spinnerDOM = _t.first);\n      }\n    },\n    inputs: {\n      bdColor: \"bdColor\",\n      size: \"size\",\n      color: \"color\",\n      type: \"type\",\n      fullScreen: \"fullScreen\",\n      name: \"name\",\n      zIndex: \"zIndex\",\n      template: \"template\",\n      showSpinner: \"showSpinner\",\n      disableAnimation: \"disableAnimation\"\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"ngx-spinner-overlay\", 3, \"background-color\", \"z-index\", \"position\", 4, \"ngIf\"], [1, \"ngx-spinner-overlay\"], [\"overlay\", \"\"], [3, \"class\", \"color\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"ngIf\"], [1, \"loading-text\"], [4, \"ngFor\", \"ngForOf\"], [3, \"innerHTML\"]],\n    template: function NgxSpinnerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, NgxSpinnerComponent_div_0_Template, 6, 12, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.spinner.show);\n      }\n    },\n    dependencies: [SafeHtmlPipe, NgIf, NgFor],\n    styles: [\".ngx-spinner-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text[_ngcontent-%COMP%]{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\"],\n    data: {\n      animation: [trigger(\"fadeIn\", [state(\"in\", style({\n        opacity: 1\n      })), transition(\":enter\", [style({\n        opacity: 0\n      }), animate(300)]), transition(\":leave\", animate(200, style({\n        opacity: 0\n      })))])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerComponent, [{\n    type: Component,\n    args: [{\n      imports: [SafeHtmlPipe, NgIf, NgFor],\n      standalone: true,\n      selector: \"ngx-spinner\",\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [trigger(\"fadeIn\", [state(\"in\", style({\n        opacity: 1\n      })), transition(\":enter\", [style({\n        opacity: 0\n      }), animate(300)]), transition(\":leave\", animate(200, style({\n        opacity: 0\n      })))])],\n      template: \"<div\\n  [@.disabled]=\\\"disableAnimation\\\"\\n  [@fadeIn]=\\\"'in'\\\"\\n  *ngIf=\\\"spinner.show\\\"\\n  class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\"\\n  [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\"\\n  #overlay\\n>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\\n\",\n      styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"]\n    }]\n  }], () => [{\n    type: NgxSpinnerService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NGX_SPINNER_CONFIG]\n    }]\n  }], {\n    bdColor: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    zIndex: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    showSpinner: [{\n      type: Input\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    spinnerDOM: [{\n      type: ViewChild,\n      args: [\"overlay\"]\n    }]\n  });\n})();\nclass NgxSpinnerModule {\n  static forRoot(config) {\n    return {\n      ngModule: NgxSpinnerModule,\n      providers: [{\n        provide: NGX_SPINNER_CONFIG,\n        useValue: config\n      }]\n    };\n  }\n  static #_ = this.ɵfac = function NgxSpinnerModule_Factory(t) {\n    return new (t || NgxSpinnerModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxSpinnerModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, NgxSpinnerComponent, SafeHtmlPipe],\n      exports: [NgxSpinnerComponent]\n    }]\n  }], null, null);\n})();\nconst provideSpinnerConfig = config => {\n  const providers = [{\n    provide: NGX_SPINNER_CONFIG,\n    useValue: config\n  }];\n  return makeEnvironmentProviders(providers);\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULTS, LOADERS, NgxSpinner, NgxSpinnerComponent, NgxSpinnerModule, NgxSpinnerService, PRIMARY_SPINNER, provideSpinnerConfig };", "map": {"version": 3, "names": ["i0", "Injectable", "InjectionToken", "<PERSON><PERSON>", "Component", "ChangeDetectionStrategy", "Optional", "Inject", "Input", "ViewChild", "NgModule", "makeEnvironmentProviders", "BehaviorSubject", "Subject", "filter", "takeUntil", "trigger", "state", "style", "transition", "animate", "i1", "NgIf", "<PERSON><PERSON><PERSON>", "CommonModule", "_c0", "NgxSpinnerComponent_div_0_div_2_div_1_Template", "rf", "ctx", "ɵɵelement", "NgxSpinnerComponent_div_0_div_2_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r2", "ɵɵnextContext", "ɵɵclassMap", "spinner", "class", "ɵɵstyleProp", "color", "ɵɵadvance", "ɵɵproperty", "divArray", "NgxSpinnerComponent_div_0_div_3_Template", "ɵɵpipe", "ctx_r3", "ɵɵpipeBind1", "template", "ɵɵsanitizeHtml", "NgxSpinnerComponent_div_0_Template", "ɵɵprojection", "ctx_r0", "bdColor", "zIndex", "fullScreen", "disableAnimation", "_c1", "LOADERS", "cog", "fire", "pacman", "timer", "DEFAULTS", "BD_COLOR", "SPINNER_COLOR", "Z_INDEX", "PRIMARY_SPINNER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "init", "Object", "assign", "create", "type", "console", "warn", "NgxSpinnerService", "spinnerObservable", "getSpinner", "name", "asObservable", "pipe", "x", "show", "Promise", "resolve", "_reject", "setTimeout", "keys", "length", "next", "hide", "debounce", "_", "ɵfac", "NgxSpinnerService_Factory", "t", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "NGX_SPINNER_CONFIG", "SafeHtmlPipe", "_sanitizer", "transform", "v", "bypassSecurityTrustHtml", "SafeHtmlPipe_Factory", "ɵɵdirectiveInject", "Dom<PERSON><PERSON><PERSON>zer", "ɵpipe", "ɵɵdefinePipe", "pure", "standalone", "NgxSpinnerComponent", "spinnerService", "changeDetector", "elementRef", "globalConfig", "ngUnsubscribe", "setDefaultOptions", "size", "divCount", "showSpinner", "initObservable", "subscribe", "onInputChange", "detectChanges", "ngOnInit", "isSpinnerZone", "element", "nativeElement", "parentElement", "parentNode", "ngOnChanges", "changes", "propName", "changedProp", "isFirstChange", "currentValue", "previousValue", "getClass", "Array", "fill", "map", "i", "sizeClass", "toLowerCase", "ngOnDestroy", "complete", "NgxSpinnerComponent_Factory", "ChangeDetectorRef", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "NgxSpinnerComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "spinnerDOM", "first", "inputs", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "NgxSpinnerComponent_Template", "ɵɵprojectionDef", "dependencies", "styles", "data", "animation", "opacity", "changeDetection", "imports", "selector", "OnPush", "animations", "undefined", "decorators", "NgxSpinnerModule", "forRoot", "config", "ngModule", "providers", "provide", "useValue", "NgxSpinnerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "exports", "provideSpinnerConfig"], "sources": ["C:/DATA/Source/Nissin_Tech/exex_debt/exex-debt-app/node_modules/ngx-spinner/fesm2022/ngx-spinner.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Pipe, Component, ChangeDetectionStrategy, Optional, Inject, Input, ViewChild, NgModule, makeEnvironmentProviders } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/platform-browser';\nimport { NgIf, NgFor, CommonModule } from '@angular/common';\n\nconst LOADERS = {\n    \"ball-8bits\": 16,\n    \"ball-atom\": 4,\n    \"ball-beat\": 3,\n    \"ball-circus\": 5,\n    \"ball-climbing-dot\": 4,\n    \"ball-clip-rotate\": 1,\n    \"ball-clip-rotate-multiple\": 2,\n    \"ball-clip-rotate-pulse\": 2,\n    \"ball-elastic-dots\": 5,\n    \"ball-fall\": 3,\n    \"ball-fussion\": 4,\n    \"ball-grid-beat\": 9,\n    \"ball-grid-pulse\": 9,\n    \"ball-newton-cradle\": 4,\n    \"ball-pulse\": 3,\n    \"ball-pulse-rise\": 5,\n    \"ball-pulse-sync\": 3,\n    \"ball-rotate\": 1,\n    \"ball-running-dots\": 5,\n    \"ball-scale\": 1,\n    \"ball-scale-multiple\": 3,\n    \"ball-scale-pulse\": 2,\n    \"ball-scale-ripple\": 1,\n    \"ball-scale-ripple-multiple\": 3,\n    \"ball-spin\": 8,\n    \"ball-spin-clockwise\": 8,\n    \"ball-spin-clockwise-fade\": 8,\n    \"ball-spin-clockwise-fade-rotating\": 8,\n    \"ball-spin-fade\": 8,\n    \"ball-spin-fade-rotating\": 8,\n    \"ball-spin-rotate\": 2,\n    \"ball-square-clockwise-spin\": 8,\n    \"ball-square-spin\": 8,\n    \"ball-triangle-path\": 3,\n    \"ball-zig-zag\": 2,\n    \"ball-zig-zag-deflect\": 2,\n    cog: 1,\n    \"cube-transition\": 2,\n    fire: 3,\n    \"line-scale\": 5,\n    \"line-scale-party\": 5,\n    \"line-scale-pulse-out\": 5,\n    \"line-scale-pulse-out-rapid\": 5,\n    \"line-spin-clockwise-fade\": 8,\n    \"line-spin-clockwise-fade-rotating\": 8,\n    \"line-spin-fade\": 8,\n    \"line-spin-fade-rotating\": 8,\n    pacman: 6,\n    \"square-jelly-box\": 2,\n    \"square-loader\": 1,\n    \"square-spin\": 1,\n    timer: 1,\n    \"triangle-skew-spin\": 1,\n};\nconst DEFAULTS = {\n    BD_COLOR: \"rgba(51,51,51,0.8)\",\n    SPINNER_COLOR: \"#fff\",\n    Z_INDEX: 99999,\n};\nconst PRIMARY_SPINNER = \"primary\";\nclass NgxSpinner {\n    constructor(init) {\n        Object.assign(this, init);\n    }\n    static create(init) {\n        if (!init?.template && !init?.type) {\n            console.warn(`[ngx-spinner]: Property \"type\" is missed. Please, provide animation type to <ngx-spinner> component\n        and ensure css is added to angular.json file`);\n        }\n        return new NgxSpinner(init);\n    }\n}\n\nclass NgxSpinnerService {\n    /**\n     * Creates an instance of NgxSpinnerService.\n     * @memberof NgxSpinnerService\n     */\n    constructor() {\n        /**\n         * Spinner observable\n         *\n         * @memberof NgxSpinnerService\n         */\n        // private spinnerObservable = new ReplaySubject<NgxSpinner>(1);\n        this.spinnerObservable = new BehaviorSubject(null);\n    }\n    /**\n     * Get subscription of desired spinner\n     * @memberof NgxSpinnerService\n     **/\n    getSpinner(name) {\n        return this.spinnerObservable\n            .asObservable()\n            .pipe(filter((x) => x && x.name === name));\n    }\n    /**\n     * To show spinner\n     *\n     * @memberof NgxSpinnerService\n     */\n    show(name = PRIMARY_SPINNER, spinner) {\n        return new Promise((resolve, _reject) => {\n            setTimeout(() => {\n                if (spinner && Object.keys(spinner).length) {\n                    spinner[\"name\"] = name;\n                    this.spinnerObservable.next(new NgxSpinner({ ...spinner, show: true }));\n                    resolve(true);\n                }\n                else {\n                    this.spinnerObservable.next(new NgxSpinner({ name, show: true }));\n                    resolve(true);\n                }\n            }, 10);\n        });\n    }\n    /**\n     * To hide spinner\n     *\n     * @memberof NgxSpinnerService\n     */\n    hide(name = PRIMARY_SPINNER, debounce = 10) {\n        return new Promise((resolve, _reject) => {\n            setTimeout(() => {\n                this.spinnerObservable.next(new NgxSpinner({ name, show: false }));\n                resolve(true);\n            }, debounce);\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerService, providedIn: \"root\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: \"root\",\n                }]\n        }], ctorParameters: () => [] });\n\nconst NGX_SPINNER_CONFIG = new InjectionToken(\"NGX_SPINNER_CONFIG\");\n\nclass SafeHtmlPipe {\n    constructor(_sanitizer) {\n        this._sanitizer = _sanitizer;\n    }\n    transform(v) {\n        if (v) {\n            return this._sanitizer.bypassSecurityTrustHtml(v);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: SafeHtmlPipe, deps: [{ token: i1.DomSanitizer }], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: SafeHtmlPipe, isStandalone: true, name: \"safeHtml\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: SafeHtmlPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: \"safeHtml\",\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1.DomSanitizer }] });\n\nclass NgxSpinnerComponent {\n    // TODO: https://github.com/Napster2210/ngx-spinner/issues/259\n    // @HostListener(\"document:keydown\", [\"$event\"])\n    // handleKeyboardEvent(event: KeyboardEvent) {\n    //   if (this.spinnerDOM && this.spinnerDOM.nativeElement) {\n    //     if (\n    //       this.fullScreen ||\n    //       (!this.fullScreen && this.isSpinnerZone(event.target))\n    //     ) {\n    //       event.returnValue = false;\n    //       event.preventDefault();\n    //     }\n    //   }\n    // }\n    /**\n     * Creates an instance of NgxSpinnerComponent.\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    constructor(spinnerService, changeDetector, elementRef, globalConfig) {\n        this.spinnerService = spinnerService;\n        this.changeDetector = changeDetector;\n        this.elementRef = elementRef;\n        this.globalConfig = globalConfig;\n        /**\n         * To enable/disable animation\n         *\n         * @type {boolean}\n         * @memberof NgxSpinnerComponent\n         */\n        this.disableAnimation = false;\n        /**\n         * Spinner Object\n         *\n         * @memberof NgxSpinnerComponent\n         */\n        this.spinner = new NgxSpinner();\n        /**\n         * Unsubscribe from spinner's observable\n         *\n         * @memberof NgxSpinnerComponent\n         **/\n        this.ngUnsubscribe = new Subject();\n        /**\n         * To set default ngx-spinner options\n         *\n         * @memberof NgxSpinnerComponent\n         */\n        this.setDefaultOptions = () => {\n            const { type } = this.globalConfig ?? {};\n            this.spinner = NgxSpinner.create({\n                name: this.name,\n                bdColor: this.bdColor,\n                size: this.size,\n                color: this.color,\n                type: this.type ?? type,\n                fullScreen: this.fullScreen,\n                divArray: this.divArray,\n                divCount: this.divCount,\n                show: this.show,\n                zIndex: this.zIndex,\n                template: this.template,\n                showSpinner: this.showSpinner,\n            });\n        };\n        this.bdColor = DEFAULTS.BD_COLOR;\n        this.zIndex = DEFAULTS.Z_INDEX;\n        this.color = DEFAULTS.SPINNER_COLOR;\n        this.size = \"large\";\n        this.fullScreen = true;\n        this.name = PRIMARY_SPINNER;\n        this.template = null;\n        this.showSpinner = false;\n        this.divArray = [];\n        this.divCount = 0;\n        this.show = false;\n    }\n    initObservable() {\n        this.spinnerService\n            .getSpinner(this.name)\n            .pipe(takeUntil(this.ngUnsubscribe))\n            .subscribe((spinner) => {\n            this.setDefaultOptions();\n            Object.assign(this.spinner, spinner);\n            if (spinner.show) {\n                this.onInputChange();\n            }\n            this.changeDetector.detectChanges();\n        });\n    }\n    /**\n     * Initialization method\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnInit() {\n        this.setDefaultOptions();\n        this.initObservable();\n    }\n    /**\n     * To check event triggers inside the Spinner Zone\n     *\n     * @param {*} element\n     * @returns {boolean}\n     * @memberof NgxSpinnerComponent\n     */\n    isSpinnerZone(element) {\n        if (element === this.elementRef.nativeElement.parentElement) {\n            return true;\n        }\n        return element.parentNode && this.isSpinnerZone(element.parentNode);\n    }\n    /**\n     * On changes event for input variables\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnChanges(changes) {\n        for (const propName in changes) {\n            if (propName) {\n                const changedProp = changes[propName];\n                if (changedProp.isFirstChange()) {\n                    return;\n                }\n                else if (typeof changedProp.currentValue !== \"undefined\" &&\n                    changedProp.currentValue !== changedProp.previousValue) {\n                    if (changedProp.currentValue !== \"\") {\n                        this.spinner[propName] = changedProp.currentValue;\n                        if (propName === \"showSpinner\") {\n                            if (changedProp.currentValue) {\n                                this.spinnerService.show(this.spinner.name, this.spinner);\n                            }\n                            else {\n                                this.spinnerService.hide(this.spinner.name);\n                            }\n                        }\n                        if (propName === \"name\") {\n                            this.initObservable();\n                        }\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * To get class for spinner\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    getClass(type, size) {\n        this.spinner.divCount = LOADERS[type];\n        this.spinner.divArray = Array(this.spinner.divCount)\n            .fill(0)\n            .map((_, i) => i);\n        let sizeClass = \"\";\n        switch (size.toLowerCase()) {\n            case \"small\":\n                sizeClass = \"la-sm\";\n                break;\n            case \"medium\":\n                sizeClass = \"la-2x\";\n                break;\n            case \"large\":\n                sizeClass = \"la-3x\";\n                break;\n            default:\n                break;\n        }\n        return \"la-\" + type + \" \" + sizeClass;\n    }\n    /**\n     * Check if input variables have changed\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    onInputChange() {\n        this.spinner.class = this.getClass(this.spinner.type, this.spinner.size);\n    }\n    /**\n     * Component destroy event\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnDestroy() {\n        this.ngUnsubscribe.next();\n        this.ngUnsubscribe.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerComponent, deps: [{ token: NgxSpinnerService }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: NGX_SPINNER_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NgxSpinnerComponent, isStandalone: true, selector: \"ngx-spinner\", inputs: { bdColor: \"bdColor\", size: \"size\", color: \"color\", type: \"type\", fullScreen: \"fullScreen\", name: \"name\", zIndex: \"zIndex\", template: \"template\", showSpinner: \"showSpinner\", disableAnimation: \"disableAnimation\" }, viewQueries: [{ propertyName: \"spinnerDOM\", first: true, predicate: [\"overlay\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n  [@.disabled]=\\\"disableAnimation\\\"\\n  [@fadeIn]=\\\"'in'\\\"\\n  *ngIf=\\\"spinner.show\\\"\\n  class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\"\\n  [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\"\\n  #overlay\\n>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"], dependencies: [{ kind: \"pipe\", type: SafeHtmlPipe, name: \"safeHtml\" }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgFor, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], animations: [\n            trigger(\"fadeIn\", [\n                state(\"in\", style({ opacity: 1 })),\n                transition(\":enter\", [style({ opacity: 0 }), animate(300)]),\n                transition(\":leave\", animate(200, style({ opacity: 0 }))),\n            ]),\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerComponent, decorators: [{\n            type: Component,\n            args: [{ imports: [SafeHtmlPipe, NgIf, NgFor], standalone: true, selector: \"ngx-spinner\", changeDetection: ChangeDetectionStrategy.OnPush, animations: [\n                        trigger(\"fadeIn\", [\n                            state(\"in\", style({ opacity: 1 })),\n                            transition(\":enter\", [style({ opacity: 0 }), animate(300)]),\n                            transition(\":leave\", animate(200, style({ opacity: 0 }))),\n                        ]),\n                    ], template: \"<div\\n  [@.disabled]=\\\"disableAnimation\\\"\\n  [@fadeIn]=\\\"'in'\\\"\\n  *ngIf=\\\"spinner.show\\\"\\n  class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\"\\n  [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\"\\n  #overlay\\n>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\\n\", styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"] }]\n        }], ctorParameters: () => [{ type: NgxSpinnerService }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [NGX_SPINNER_CONFIG]\n                }] }], propDecorators: { bdColor: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], zIndex: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }], showSpinner: [{\n                type: Input\n            }], disableAnimation: [{\n                type: Input\n            }], spinnerDOM: [{\n                type: ViewChild,\n                args: [\"overlay\"]\n            }] } });\n\nclass NgxSpinnerModule {\n    static forRoot(config) {\n        return {\n            ngModule: NgxSpinnerModule,\n            providers: [{ provide: NGX_SPINNER_CONFIG, useValue: config }],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerModule, imports: [CommonModule, NgxSpinnerComponent, SafeHtmlPipe], exports: [NgxSpinnerComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NgxSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, NgxSpinnerComponent, SafeHtmlPipe],\n                    exports: [NgxSpinnerComponent],\n                }]\n        }] });\n\nconst provideSpinnerConfig = (config) => {\n    const providers = [\n        {\n            provide: NGX_SPINNER_CONFIG,\n            useValue: config,\n        },\n    ];\n    return makeEnvironmentProviders(providers);\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULTS, LOADERS, NgxSpinner, NgxSpinnerComponent, NgxSpinnerModule, NgxSpinnerService, PRIMARY_SPINNER, provideSpinnerConfig };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,IAAI,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,eAAe;AAC5K,SAASC,eAAe,EAAEC,OAAO,QAAQ,MAAM;AAC/C,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,IAAI,EAAEC,KAAK,EAAEC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,SAAAC,+CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoIwC3B,EAAE,CAAA6B,SAAA,SA4Nk2B,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Nr2B3B,EAAE,CAAA+B,cAAA,SA4NwyB,CAAC;IA5N3yB/B,EAAE,CAAAgC,UAAA,IAAAN,8CAAA,gBA4Nk2B,CAAC;IA5Nr2B1B,EAAE,CAAAiC,YAAA,CA4N42B,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GA5N/2BlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAoC,UAAA,CAAAF,MAAA,CAAAG,OAAA,CAAAC,KA4NuwB,CAAC;IA5N1wBtC,EAAE,CAAAuC,WAAA,UAAAL,MAAA,CAAAG,OAAA,CAAAG,KA4NuyB,CAAC;IA5N1yBxC,EAAE,CAAAyC,SAAA,EA4Ny1B,CAAC;IA5N51BzC,EAAE,CAAA0C,UAAA,YAAAR,MAAA,CAAAG,OAAA,CAAAM,QA4Ny1B,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5N51B3B,EAAE,CAAA6B,SAAA,YA4Nk7B,CAAC;IA5Nr7B7B,EAAE,CAAA6C,MAAA;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAmB,MAAA,GAAF9C,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA0C,UAAA,cAAF1C,EAAE,CAAA+C,WAAA,OAAAD,MAAA,CAAAE,QAAA,GAAFhD,EAAE,CAAAiD,cA4N26B,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5N96B3B,EAAE,CAAA+B,cAAA,eA4NitB,CAAC;IA5NptB/B,EAAE,CAAAgC,UAAA,IAAAF,wCAAA,gBA4N42B,CAAC,IAAAc,wCAAA,gBAAD,CAAC;IA5N/2B5C,EAAE,CAAA+B,cAAA,YA4Nq/B,CAAC;IA5Nx/B/B,EAAE,CAAAmD,YAAA,EA4NohC,CAAC;IA5NvhCnD,EAAE,CAAAiC,YAAA,CA4N8hC,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAyB,MAAA,GA5NjiCpD,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAuC,WAAA,qBAAAa,MAAA,CAAAf,OAAA,CAAAgB,OA4N0lB,CAAC,YAAAD,MAAA,CAAAf,OAAA,CAAAiB,MAAD,CAAC,aAAAF,MAAA,CAAAf,OAAA,CAAAkB,UAAA,uBAAD,CAAC;IA5N7lBvD,EAAE,CAAA0C,UAAA,eAAAU,MAAA,CAAAI,gBA4Nyd,CAAC,gBAAD,CAAC;IA5N5dxD,EAAE,CAAAyC,SAAA,EA4N2uB,CAAC;IA5N9uBzC,EAAE,CAAA0C,UAAA,UAAAU,MAAA,CAAAJ,QA4N2uB,CAAC;IA5N9uBhD,EAAE,CAAAyC,SAAA,EA4Nq4B,CAAC;IA5Nx4BzC,EAAE,CAAA0C,UAAA,SAAAU,MAAA,CAAAJ,QA4Nq4B,CAAC;IA5Nx4BhD,EAAE,CAAAyC,SAAA,EA4No/B,CAAC;IA5Nv/BzC,EAAE,CAAAuC,WAAA,YAAAa,MAAA,CAAAf,OAAA,CAAAiB,MA4No/B,CAAC;EAAA;AAAA;AAAA,MAAAG,GAAA;AA9V3lC,MAAMC,OAAO,GAAG;EACZ,YAAY,EAAE,EAAE;EAChB,WAAW,EAAE,CAAC;EACd,WAAW,EAAE,CAAC;EACd,aAAa,EAAE,CAAC;EAChB,mBAAmB,EAAE,CAAC;EACtB,kBAAkB,EAAE,CAAC;EACrB,2BAA2B,EAAE,CAAC;EAC9B,wBAAwB,EAAE,CAAC;EAC3B,mBAAmB,EAAE,CAAC;EACtB,WAAW,EAAE,CAAC;EACd,cAAc,EAAE,CAAC;EACjB,gBAAgB,EAAE,CAAC;EACnB,iBAAiB,EAAE,CAAC;EACpB,oBAAoB,EAAE,CAAC;EACvB,YAAY,EAAE,CAAC;EACf,iBAAiB,EAAE,CAAC;EACpB,iBAAiB,EAAE,CAAC;EACpB,aAAa,EAAE,CAAC;EAChB,mBAAmB,EAAE,CAAC;EACtB,YAAY,EAAE,CAAC;EACf,qBAAqB,EAAE,CAAC;EACxB,kBAAkB,EAAE,CAAC;EACrB,mBAAmB,EAAE,CAAC;EACtB,4BAA4B,EAAE,CAAC;EAC/B,WAAW,EAAE,CAAC;EACd,qBAAqB,EAAE,CAAC;EACxB,0BAA0B,EAAE,CAAC;EAC7B,mCAAmC,EAAE,CAAC;EACtC,gBAAgB,EAAE,CAAC;EACnB,yBAAyB,EAAE,CAAC;EAC5B,kBAAkB,EAAE,CAAC;EACrB,4BAA4B,EAAE,CAAC;EAC/B,kBAAkB,EAAE,CAAC;EACrB,oBAAoB,EAAE,CAAC;EACvB,cAAc,EAAE,CAAC;EACjB,sBAAsB,EAAE,CAAC;EACzBC,GAAG,EAAE,CAAC;EACN,iBAAiB,EAAE,CAAC;EACpBC,IAAI,EAAE,CAAC;EACP,YAAY,EAAE,CAAC;EACf,kBAAkB,EAAE,CAAC;EACrB,sBAAsB,EAAE,CAAC;EACzB,4BAA4B,EAAE,CAAC;EAC/B,0BAA0B,EAAE,CAAC;EAC7B,mCAAmC,EAAE,CAAC;EACtC,gBAAgB,EAAE,CAAC;EACnB,yBAAyB,EAAE,CAAC;EAC5BC,MAAM,EAAE,CAAC;EACT,kBAAkB,EAAE,CAAC;EACrB,eAAe,EAAE,CAAC;EAClB,aAAa,EAAE,CAAC;EAChBC,KAAK,EAAE,CAAC;EACR,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,QAAQ,GAAG;EACbC,QAAQ,EAAE,oBAAoB;EAC9BC,aAAa,EAAE,MAAM;EACrBC,OAAO,EAAE;AACb,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,UAAU,CAAC;EACbC,WAAWA,CAACC,IAAI,EAAE;IACdC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEF,IAAI,CAAC;EAC7B;EACA,OAAOG,MAAMA,CAACH,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,EAAEtB,QAAQ,IAAI,CAACsB,IAAI,EAAEI,IAAI,EAAE;MAChCC,OAAO,CAACC,IAAI,CAAE;AAC1B,qDAAqD,CAAC;IAC9C;IACA,OAAO,IAAIR,UAAU,CAACE,IAAI,CAAC;EAC/B;AACJ;AAEA,MAAMO,iBAAiB,CAAC;EACpB;AACJ;AACA;AACA;EACIR,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ;IACA,IAAI,CAACS,iBAAiB,GAAG,IAAIlE,eAAe,CAAC,IAAI,CAAC;EACtD;EACA;AACJ;AACA;AACA;EACImE,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAACF,iBAAiB,CACxBG,YAAY,CAAC,CAAC,CACdC,IAAI,CAACpE,MAAM,CAAEqE,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;EACII,IAAIA,CAACJ,IAAI,GAAGb,eAAe,EAAE9B,OAAO,EAAE;IAClC,OAAO,IAAIgD,OAAO,CAAC,CAACC,OAAO,EAAEC,OAAO,KAAK;MACrCC,UAAU,CAAC,MAAM;QACb,IAAInD,OAAO,IAAIkC,MAAM,CAACkB,IAAI,CAACpD,OAAO,CAAC,CAACqD,MAAM,EAAE;UACxCrD,OAAO,CAAC,MAAM,CAAC,GAAG2C,IAAI;UACtB,IAAI,CAACF,iBAAiB,CAACa,IAAI,CAAC,IAAIvB,UAAU,CAAC;YAAE,GAAG/B,OAAO;YAAE+C,IAAI,EAAE;UAAK,CAAC,CAAC,CAAC;UACvEE,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,MACI;UACD,IAAI,CAACR,iBAAiB,CAACa,IAAI,CAAC,IAAIvB,UAAU,CAAC;YAAEY,IAAI;YAAEI,IAAI,EAAE;UAAK,CAAC,CAAC,CAAC;UACjEE,OAAO,CAAC,IAAI,CAAC;QACjB;MACJ,CAAC,EAAE,EAAE,CAAC;IACV,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIM,IAAIA,CAACZ,IAAI,GAAGb,eAAe,EAAE0B,QAAQ,GAAG,EAAE,EAAE;IACxC,OAAO,IAAIR,OAAO,CAAC,CAACC,OAAO,EAAEC,OAAO,KAAK;MACrCC,UAAU,CAAC,MAAM;QACb,IAAI,CAACV,iBAAiB,CAACa,IAAI,CAAC,IAAIvB,UAAU,CAAC;UAAEY,IAAI;UAAEI,IAAI,EAAE;QAAM,CAAC,CAAC,CAAC;QAClEE,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,EAAEO,QAAQ,CAAC;IAChB,CAAC,CAAC;EACN;EAAC,QAAAC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,0BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFpB,iBAAiB;EAAA,CAAoD;EAAA,QAAAqB,EAAA,GACtK,IAAI,CAACC,KAAK,kBAD6EnG,EAAE,CAAAoG,kBAAA;IAAAC,KAAA,EACYxB,iBAAiB;IAAAyB,OAAA,EAAjBzB,iBAAiB,CAAAkB,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxG,EAAE,CAAAyG,iBAAA,CAGX5B,iBAAiB,EAAc,CAAC;IAC/GH,IAAI,EAAEzE,UAAU;IAChByG,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMI,kBAAkB,GAAG,IAAIzG,cAAc,CAAC,oBAAoB,CAAC;AAEnE,MAAM0G,YAAY,CAAC;EACfvC,WAAWA,CAACwC,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,SAASA,CAACC,CAAC,EAAE;IACT,IAAIA,CAAC,EAAE;MACH,OAAO,IAAI,CAACF,UAAU,CAACG,uBAAuB,CAACD,CAAC,CAAC;IACrD;EACJ;EAAC,QAAAjB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAkB,qBAAAhB,CAAA;IAAA,YAAAA,CAAA,IAAwFW,YAAY,EArBtB5G,EAAE,CAAAkH,iBAAA,CAqBsC7F,EAAE,CAAC8F,YAAY;EAAA,CAAuC;EAAA,QAAAjB,EAAA,GACrL,IAAI,CAACkB,KAAK,kBAtB6EpH,EAAE,CAAAqH,YAAA;IAAArC,IAAA;IAAAN,IAAA,EAsBMkC,YAAY;IAAAU,IAAA;IAAAC,UAAA;EAAA,EAAyC;AACjK;AACA;EAAA,QAAAf,SAAA,oBAAAA,SAAA,KAxBoGxG,EAAE,CAAAyG,iBAAA,CAwBXG,YAAY,EAAc,CAAC;IAC1GlC,IAAI,EAAEvE,IAAI;IACVuG,IAAI,EAAE,CAAC;MACC1B,IAAI,EAAE,UAAU;MAChBuC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7C,IAAI,EAAErD,EAAE,CAAC8F;EAAa,CAAC,CAAC;AAAA;AAE7D,MAAMK,mBAAmB,CAAC;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACInD,WAAWA,CAACoD,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAE;IAClE,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACpE,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACnB,OAAO,GAAG,IAAI+B,UAAU,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyD,aAAa,GAAG,IAAIhH,OAAO,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiH,iBAAiB,GAAG,MAAM;MAC3B,MAAM;QAAEpD;MAAK,CAAC,GAAG,IAAI,CAACkD,YAAY,IAAI,CAAC,CAAC;MACxC,IAAI,CAACvF,OAAO,GAAG+B,UAAU,CAACK,MAAM,CAAC;QAC7BO,IAAI,EAAE,IAAI,CAACA,IAAI;QACf3B,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB0E,IAAI,EAAE,IAAI,CAACA,IAAI;QACfvF,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBkC,IAAI,EAAE,IAAI,CAACA,IAAI,IAAIA,IAAI;QACvBnB,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BZ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBqF,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB5C,IAAI,EAAE,IAAI,CAACA,IAAI;QACf9B,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBN,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBiF,WAAW,EAAE,IAAI,CAACA;MACtB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC5E,OAAO,GAAGU,QAAQ,CAACC,QAAQ;IAChC,IAAI,CAACV,MAAM,GAAGS,QAAQ,CAACG,OAAO;IAC9B,IAAI,CAAC1B,KAAK,GAAGuB,QAAQ,CAACE,aAAa;IACnC,IAAI,CAAC8D,IAAI,GAAG,OAAO;IACnB,IAAI,CAACxE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACyB,IAAI,GAAGb,eAAe;IAC3B,IAAI,CAACnB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACiF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACtF,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACqF,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAC5C,IAAI,GAAG,KAAK;EACrB;EACA8C,cAAcA,CAAA,EAAG;IACb,IAAI,CAACT,cAAc,CACd1C,UAAU,CAAC,IAAI,CAACC,IAAI,CAAC,CACrBE,IAAI,CAACnE,SAAS,CAAC,IAAI,CAAC8G,aAAa,CAAC,CAAC,CACnCM,SAAS,CAAE9F,OAAO,IAAK;MACxB,IAAI,CAACyF,iBAAiB,CAAC,CAAC;MACxBvD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACnC,OAAO,EAAEA,OAAO,CAAC;MACpC,IAAIA,OAAO,CAAC+C,IAAI,EAAE;QACd,IAAI,CAACgD,aAAa,CAAC,CAAC;MACxB;MACA,IAAI,CAACV,cAAc,CAACW,aAAa,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACR,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACI,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAIA,OAAO,KAAK,IAAI,CAACb,UAAU,CAACc,aAAa,CAACC,aAAa,EAAE;MACzD,OAAO,IAAI;IACf;IACA,OAAOF,OAAO,CAACG,UAAU,IAAI,IAAI,CAACJ,aAAa,CAACC,OAAO,CAACG,UAAU,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,MAAMC,QAAQ,IAAID,OAAO,EAAE;MAC5B,IAAIC,QAAQ,EAAE;QACV,MAAMC,WAAW,GAAGF,OAAO,CAACC,QAAQ,CAAC;QACrC,IAAIC,WAAW,CAACC,aAAa,CAAC,CAAC,EAAE;UAC7B;QACJ,CAAC,MACI,IAAI,OAAOD,WAAW,CAACE,YAAY,KAAK,WAAW,IACpDF,WAAW,CAACE,YAAY,KAAKF,WAAW,CAACG,aAAa,EAAE;UACxD,IAAIH,WAAW,CAACE,YAAY,KAAK,EAAE,EAAE;YACjC,IAAI,CAAC5G,OAAO,CAACyG,QAAQ,CAAC,GAAGC,WAAW,CAACE,YAAY;YACjD,IAAIH,QAAQ,KAAK,aAAa,EAAE;cAC5B,IAAIC,WAAW,CAACE,YAAY,EAAE;gBAC1B,IAAI,CAACxB,cAAc,CAACrC,IAAI,CAAC,IAAI,CAAC/C,OAAO,CAAC2C,IAAI,EAAE,IAAI,CAAC3C,OAAO,CAAC;cAC7D,CAAC,MACI;gBACD,IAAI,CAACoF,cAAc,CAAC7B,IAAI,CAAC,IAAI,CAACvD,OAAO,CAAC2C,IAAI,CAAC;cAC/C;YACJ;YACA,IAAI8D,QAAQ,KAAK,MAAM,EAAE;cACrB,IAAI,CAACZ,cAAc,CAAC,CAAC;YACzB;UACJ;QACJ;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIiB,QAAQA,CAACzE,IAAI,EAAEqD,IAAI,EAAE;IACjB,IAAI,CAAC1F,OAAO,CAAC2F,QAAQ,GAAGtE,OAAO,CAACgB,IAAI,CAAC;IACrC,IAAI,CAACrC,OAAO,CAACM,QAAQ,GAAGyG,KAAK,CAAC,IAAI,CAAC/G,OAAO,CAAC2F,QAAQ,CAAC,CAC/CqB,IAAI,CAAC,CAAC,CAAC,CACPC,GAAG,CAAC,CAACxD,CAAC,EAAEyD,CAAC,KAAKA,CAAC,CAAC;IACrB,IAAIC,SAAS,GAAG,EAAE;IAClB,QAAQzB,IAAI,CAAC0B,WAAW,CAAC,CAAC;MACtB,KAAK,OAAO;QACRD,SAAS,GAAG,OAAO;QACnB;MACJ,KAAK,QAAQ;QACTA,SAAS,GAAG,OAAO;QACnB;MACJ,KAAK,OAAO;QACRA,SAAS,GAAG,OAAO;QACnB;MACJ;QACI;IACR;IACA,OAAO,KAAK,GAAG9E,IAAI,GAAG,GAAG,GAAG8E,SAAS;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIpB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC/F,OAAO,CAACC,KAAK,GAAG,IAAI,CAAC6G,QAAQ,CAAC,IAAI,CAAC9G,OAAO,CAACqC,IAAI,EAAE,IAAI,CAACrC,OAAO,CAAC0F,IAAI,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;EACI2B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7B,aAAa,CAAClC,IAAI,CAAC,CAAC;IACzB,IAAI,CAACkC,aAAa,CAAC8B,QAAQ,CAAC,CAAC;EACjC;EAAC,QAAA7D,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA6D,4BAAA3D,CAAA;IAAA,YAAAA,CAAA,IAAwFuB,mBAAmB,EA3N7BxH,EAAE,CAAAkH,iBAAA,CA2N6CrC,iBAAiB,GA3NhE7E,EAAE,CAAAkH,iBAAA,CA2N2ElH,EAAE,CAAC6J,iBAAiB,GA3NjG7J,EAAE,CAAAkH,iBAAA,CA2N4GlH,EAAE,CAAC8J,UAAU,GA3N3H9J,EAAE,CAAAkH,iBAAA,CA2NsIP,kBAAkB;EAAA,CAA4D;EAAA,QAAAT,EAAA,GAC7S,IAAI,CAAC6D,IAAI,kBA5N8E/J,EAAE,CAAAgK,iBAAA;IAAAtF,IAAA,EA4NJ8C,mBAAmB;IAAAyC,SAAA;IAAAC,SAAA,WAAAC,0BAAAxI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA5NjB3B,EAAE,CAAAoK,WAAA,CAAA3I,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAA0I,EAAA;QAAFrK,EAAE,CAAAsK,cAAA,CAAAD,EAAA,GAAFrK,EAAE,CAAAuK,WAAA,QAAA3I,GAAA,CAAA4I,UAAA,GAAAH,EAAA,CAAAI,KAAA;MAAA;IAAA;IAAAC,MAAA;MAAArH,OAAA;MAAA0E,IAAA;MAAAvF,KAAA;MAAAkC,IAAA;MAAAnB,UAAA;MAAAyB,IAAA;MAAA1B,MAAA;MAAAN,QAAA;MAAAiF,WAAA;MAAAzE,gBAAA;IAAA;IAAA+D,UAAA;IAAAoD,QAAA,GAAF3K,EAAE,CAAA4K,oBAAA,EAAF5K,EAAE,CAAA6K,mBAAA;IAAAC,kBAAA,EAAArH,GAAA;IAAAsH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAjI,QAAA,WAAAkI,6BAAAvJ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3B,EAAE,CAAAmL,eAAA;QAAFnL,EAAE,CAAAgC,UAAA,IAAAkB,kCAAA,iBA4NsiC,CAAC;MAAA;MAAA,IAAAvB,EAAA;QA5NziC3B,EAAE,CAAA0C,UAAA,SAAAd,GAAA,CAAAS,OAAA,CAAA+C,IA4NugB,CAAC;MAAA;IAAA;IAAAgG,YAAA,GAAw2BxE,YAAY,EAAiDtF,IAAI,EAA6FC,KAAK;IAAA8J,MAAA;IAAAC,IAAA;MAAAC,SAAA,EAAqG,CACltDvK,OAAO,CAAC,QAAQ,EAAE,CACdC,KAAK,CAAC,IAAI,EAAEC,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EAClCrK,UAAU,CAAC,QAAQ,EAAE,CAACD,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,EAAEpK,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAC3DD,UAAU,CAAC,QAAQ,EAAEC,OAAO,CAAC,GAAG,EAAEF,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAC5D,CAAC;IACL;IAAAC,eAAA;EAAA,EAAuD;AAChE;AACA;EAAA,QAAAjF,SAAA,oBAAAA,SAAA,KApOoGxG,EAAE,CAAAyG,iBAAA,CAoOXe,mBAAmB,EAAc,CAAC;IACjH9C,IAAI,EAAEtE,SAAS;IACfsG,IAAI,EAAE,CAAC;MAAEgF,OAAO,EAAE,CAAC9E,YAAY,EAAEtF,IAAI,EAAEC,KAAK,CAAC;MAAEgG,UAAU,EAAE,IAAI;MAAEoE,QAAQ,EAAE,aAAa;MAAEF,eAAe,EAAEpL,uBAAuB,CAACuL,MAAM;MAAEC,UAAU,EAAE,CAC3I7K,OAAO,CAAC,QAAQ,EAAE,CACdC,KAAK,CAAC,IAAI,EAAEC,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,EAClCrK,UAAU,CAAC,QAAQ,EAAE,CAACD,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,EAAEpK,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAC3DD,UAAU,CAAC,QAAQ,EAAEC,OAAO,CAAC,GAAG,EAAEF,KAAK,CAAC;QAAEsK,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAC5D,CAAC,CACL;MAAExI,QAAQ,EAAE,0nBAA0nB;MAAEqI,MAAM,EAAE,CAAC,mRAAmR;IAAE,CAAC;EACp7B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3G,IAAI,EAAEG;EAAkB,CAAC,EAAE;IAAEH,IAAI,EAAE1E,EAAE,CAAC6J;EAAkB,CAAC,EAAE;IAAEnF,IAAI,EAAE1E,EAAE,CAAC8J;EAAW,CAAC,EAAE;IAAEpF,IAAI,EAAEoH,SAAS;IAAEC,UAAU,EAAE,CAAC;MACrIrH,IAAI,EAAEpE;IACV,CAAC,EAAE;MACCoE,IAAI,EAAEnE,MAAM;MACZmG,IAAI,EAAE,CAACC,kBAAkB;IAC7B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEtD,OAAO,EAAE,CAAC;MACnCqB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEuH,IAAI,EAAE,CAAC;MACPrD,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEgC,KAAK,EAAE,CAAC;MACRkC,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEkE,IAAI,EAAE,CAAC;MACPA,IAAI,EAAElE;IACV,CAAC,CAAC;IAAE+C,UAAU,EAAE,CAAC;MACbmB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEwE,IAAI,EAAE,CAAC;MACPN,IAAI,EAAElE;IACV,CAAC,CAAC;IAAE8C,MAAM,EAAE,CAAC;MACToB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEwC,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEyH,WAAW,EAAE,CAAC;MACdvD,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEgD,gBAAgB,EAAE,CAAC;MACnBkB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEgK,UAAU,EAAE,CAAC;MACb9F,IAAI,EAAEjE,SAAS;MACfiG,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsF,gBAAgB,CAAC;EACnB,OAAOC,OAAOA,CAACC,MAAM,EAAE;IACnB,OAAO;MACHC,QAAQ,EAAEH,gBAAgB;MAC1BI,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE1F,kBAAkB;QAAE2F,QAAQ,EAAEJ;MAAO,CAAC;IACjE,CAAC;EACL;EAAC,QAAApG,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAwG,yBAAAtG,CAAA;IAAA,YAAAA,CAAA,IAAwF+F,gBAAgB;EAAA,CAAkD;EAAA,QAAA9F,EAAA,GACnK,IAAI,CAACsG,IAAI,kBAnR8ExM,EAAE,CAAAyM,gBAAA;IAAA/H,IAAA,EAmRSsH;EAAgB,EAA+F;EAAA,QAAAU,EAAA,GACjN,IAAI,CAACC,IAAI,kBApR8E3M,EAAE,CAAA4M,gBAAA;IAAAlB,OAAA,GAoRqClK,YAAY;EAAA,EAAI;AAC3J;AACA;EAAA,QAAAgF,SAAA,oBAAAA,SAAA,KAtRoGxG,EAAE,CAAAyG,iBAAA,CAsRXuF,gBAAgB,EAAc,CAAC;IAC9GtH,IAAI,EAAEhE,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACCgF,OAAO,EAAE,CAAClK,YAAY,EAAEgG,mBAAmB,EAAEZ,YAAY,CAAC;MAC1DiG,OAAO,EAAE,CAACrF,mBAAmB;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsF,oBAAoB,GAAIZ,MAAM,IAAK;EACrC,MAAME,SAAS,GAAG,CACd;IACIC,OAAO,EAAE1F,kBAAkB;IAC3B2F,QAAQ,EAAEJ;EACd,CAAC,CACJ;EACD,OAAOvL,wBAAwB,CAACyL,SAAS,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;;AAEA,SAASrI,QAAQ,EAAEL,OAAO,EAAEU,UAAU,EAAEoD,mBAAmB,EAAEwE,gBAAgB,EAAEnH,iBAAiB,EAAEV,eAAe,EAAE2I,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}