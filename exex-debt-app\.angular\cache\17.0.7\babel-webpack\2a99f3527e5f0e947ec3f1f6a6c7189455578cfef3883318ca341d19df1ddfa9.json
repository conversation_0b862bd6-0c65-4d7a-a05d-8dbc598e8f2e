{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login/login.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthRoutingModule {\n  static #_ = this.ɵfac = function AuthRoutingModule_Factory(t) {\n    return new (t || AuthRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'login',\n      component: LoginComponent\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "AuthRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\auth-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { LoginComponent } from './login/login.component';\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {\r\n                path: 'login',\r\n                component: LoginComponent,\r\n            },\r\n            { path: '**', redirectTo: '/notfound' },\r\n        ]),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\nexport class AuthRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;;;AAcxD,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAVtBL,YAAY,CAACM,QAAQ,CAAC,CAClB;MACIC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAEP;KACd,EACD;MAAEM,IAAI,EAAE,IAAI;MAAEE,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EAEIT,YAAY;EAAA;;;2EAEbE,iBAAiB;IAAAQ,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFhBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}