import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'money',
    standalone: true,
})
export class MoneyPipe implements PipeTransform {
    transform(value: any): string {
        if (!isNaN(value) && value !== null && value !== '') {
            return new Intl.NumberFormat('vi-VN').format(Number(value)); // Format with Vietnamese thousand separators
        }
        return value; // Return original value if not a valid number
    }
}
