{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomerComponent } from './customer/customer.component';\nimport { UserComponent } from './user/user.component';\nimport { InvoiceComponent } from './invoice/invoice.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardsRoutingModule {\n  static #_ = this.ɵfac = function DashboardsRoutingModule_Factory(t) {\n    return new (t || DashboardsRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardsRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'user',\n      component: UserComponent\n    }, {\n      path: 'customer',\n      component: CustomerComponent\n    }, {\n      path: 'invoice',\n      component: InvoiceComponent\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomerComponent", "UserComponent", "InvoiceComponent", "DashboardsRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "redirectTo", "imports", "i1", "exports"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CustomerComponent } from './customer/customer.component';\r\nimport { UserComponent } from './user/user.component';\r\nimport { InvoiceComponent } from './invoice/invoice.component';\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forChild([\r\n            {\r\n                path: 'user',\r\n                component: UserComponent,\r\n            },\r\n            {\r\n                path: 'customer',\r\n                component: CustomerComponent,\r\n            },\r\n            {\r\n                path: 'invoice',\r\n                component: InvoiceComponent,\r\n            },\r\n            { path: '**', redirectTo: '/notfound' },\r\n        ]),\r\n    ],\r\n    exports: [RouterModule],\r\n})\r\nexport class DashboardsRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,gBAAgB,QAAQ,6BAA6B;;;AAsB9D,OAAM,MAAOC,uBAAuB;EAAA,QAAAC,CAAA,G;qBAAvBD,uBAAuB;EAAA;EAAA,QAAAE,EAAA,G;UAAvBF;EAAuB;EAAA,QAAAG,EAAA,G;cAlB5BP,YAAY,CAACQ,QAAQ,CAAC,CAClB;MACIC,IAAI,EAAE,MAAM;MACZC,SAAS,EAAER;KACd,EACD;MACIO,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAET;KACd,EACD;MACIQ,IAAI,EAAE,SAAS;MACfC,SAAS,EAAEP;KACd,EACD;MAAEM,IAAI,EAAE,IAAI;MAAEE,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EAEIX,YAAY;EAAA;;;2EAEbI,uBAAuB;IAAAQ,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFtBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}