{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class UserComponent {\n  constructor() {\n    this.http = inject(HttpClient);\n    this.http.get('https://jsonplaceholder.typicode.com/users').subscribe(res => {\n      console.log(res);\n    });\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function UserComponent_Factory(t) {\n    return new (t || UserComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserComponent,\n    selectors: [[\"app-user\"]],\n    decls: 2,\n    vars: 0,\n    template: function UserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"user works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["HttpClient", "inject", "UserComponent", "constructor", "http", "get", "subscribe", "res", "console", "log", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "template", "UserComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, inject, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-user',\r\n  templateUrl: './user.component.html',\r\n  styleUrls: ['./user.component.scss']\r\n})\r\nexport class UserComponent implements OnInit {\r\n  http = inject(HttpClient);\r\n  constructor() { \r\n    this.http.get('https://jsonplaceholder.typicode.com/users').subscribe((res) => {\r\n      console.log(res);\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<p>user works!</p>\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAAoBC,MAAM,QAAgB,eAAe;;AAOzD,OAAM,MAAOC,aAAa;EAExBC,YAAA;IADA,KAAAC,IAAI,GAAGH,MAAM,CAACD,UAAU,CAAC;IAEvB,IAAI,CAACI,IAAI,CAACC,GAAG,CAAC,4CAA4C,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;MAC5EC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEAG,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBATUT,aAAa;EAAA;EAAA,QAAAU,EAAA,G;UAAbV,aAAa;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR1BE,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}