{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserRoutingModule } from './user-routing.module';\nimport { UserComponent } from './user.component';\nimport * as i0 from \"@angular/core\";\nexport class UserModule {\n  static #_ = this.ɵfac = function UserModule_Factory(t) {\n    return new (t || UserModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UserModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, UserRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserModule, {\n    declarations: [UserComponent],\n    imports: [CommonModule, UserRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UserRoutingModule", "UserComponent", "UserModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\DATA\\Source\\PD EXEX ADD ON PC\\pd_exex_add_on_pc\\ADDON\\exex-debt-app\\src\\app\\core\\components\\dashboard\\user\\user.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { UserRoutingModule } from './user-routing.module';\nimport { UserComponent } from './user.component';\n\n@NgModule({\n    imports: [CommonModule, UserRoutingModule],\n    declarations: [UserComponent],\n})\nexport class UserModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,kBAAkB;;AAMhD,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAHTN,YAAY,EAAEC,iBAAiB;EAAA;;;2EAGhCE,UAAU;IAAAI,YAAA,GAFJL,aAAa;IAAAM,OAAA,GADlBR,YAAY,EAAEC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}