{"ast": null, "code": "import { retry } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class RetryInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    // Define the maximum number of retries\n    const maxRetries = 2;\n    return next.handle(request).pipe(retry(maxRetries));\n  }\n  static #_ = this.ɵfac = function RetryInterceptor_Factory(t) {\n    return new (t || RetryInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RetryInterceptor,\n    factory: RetryInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["retry", "RetryInterceptor", "constructor", "intercept", "request", "next", "maxRetries", "handle", "pipe", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\retry.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';\r\nimport { retry } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class RetryInterceptor implements HttpInterceptor {\r\n    constructor() {}\r\n\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {\r\n        // Define the maximum number of retries\r\n        const maxRetries = 2;\r\n        return next.handle(request).pipe(retry(maxRetries));\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ,gBAAgB;;AAGtC,OAAM,MAAOC,gBAAgB;EACzBC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,MAAMC,UAAU,GAAG,CAAC;IACpB,OAAOD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAACI,IAAI,CAACR,KAAK,CAACM,UAAU,CAAC,CAAC;EACvD;EAAC,QAAAG,CAAA,G;qBAPQR,gBAAgB;EAAA;EAAA,QAAAS,EAAA,G;WAAhBT,gBAAgB;IAAAU,OAAA,EAAhBV,gBAAgB,CAAAW;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}