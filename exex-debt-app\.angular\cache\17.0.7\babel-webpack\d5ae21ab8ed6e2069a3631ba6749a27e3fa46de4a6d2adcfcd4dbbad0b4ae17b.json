{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CustomerService {\n  constructor() {}\n  getCustomerData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        customerId: 'COD' + i,\n        customerName: 'Customer' + i,\n        phoneNumber: '03987654' + i,\n        email: 'nguyenvan' + i + '@gmail.com',\n        address: 'HCM',\n        status: 'Active',\n        creditLimit: 100000,\n        currentBalance: 4900 + i,\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getCustomers() {\n    return Promise.resolve(this.getCustomerData());\n  }\n  static #_ = this.ɵfac = function CustomerService_Factory(t) {\n    return new (t || CustomerService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomerService,\n    factory: CustomerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["CustomerService", "constructor", "getCustomerData", "mockData", "Array", "from", "length", "map", "_", "i", "id", "customerId", "customerName", "phoneNumber", "email", "address", "status", "creditLimit", "currentBalance", "currencyId", "getCustomers", "Promise", "resolve", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n    constructor() {}\r\n\r\n    getCustomerData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                customerId: 'COD' + i,\r\n                customerName: 'Customer' + i,\r\n                phoneNumber: '03987654' + i,\r\n                email: 'nguyenvan' + i + '@gmail.com',\r\n                address: 'HCM',\r\n                status: 'Active',\r\n                creditLimit: 100000,\r\n                currentBalance: 4900 + i,\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getCustomers() {\r\n        return Promise.resolve(this.getCustomerData());\r\n    }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EACxBC,YAAA,GAAe;EAEfC,eAAeA,CAAA;IACX,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLE,UAAU,EAAE,KAAK,GAAGF,CAAC;QACrBG,YAAY,EAAE,UAAU,GAAGH,CAAC;QAC5BI,WAAW,EAAE,UAAU,GAAGJ,CAAC;QAC3BK,KAAK,EAAE,WAAW,GAAGL,CAAC,GAAG,YAAY;QACrCM,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,MAAM;QACnBC,cAAc,EAAE,IAAI,GAAGT,CAAC;QACxBU,UAAU,EAAE,KAAK,GAAGV;OACvB;IACL,CAAC,CAAC;IAEF,OAAON,QAAQ;EACnB;EAEAiB,YAAYA,CAAA;IACR,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACpB,eAAe,EAAE,CAAC;EAClD;EAAC,QAAAM,CAAA,G;qBAxBQR,eAAe;EAAA;EAAA,QAAAuB,EAAA,G;WAAfvB,eAAe;IAAAwB,OAAA,EAAfxB,eAAe,CAAAyB,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}