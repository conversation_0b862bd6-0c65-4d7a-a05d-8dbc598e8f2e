{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Path } from './../core/enums/path.enum';\nimport { Component, ViewChild } from '@angular/core';\nlet AppTopBarComponent = class AppTopBarComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.pathUser = Path.DASHBOARD_USER;\n  }\n  signout() {\n    this.authService.logout();\n  }\n};\n__decorate([ViewChild('menubutton')], AppTopBarComponent.prototype, \"menuButton\", void 0);\n__decorate([ViewChild('topbarmenubutton')], AppTopBarComponent.prototype, \"topbarMenuButton\", void 0);\n__decorate([ViewChild('topbarmenu')], AppTopBarComponent.prototype, \"menu\", void 0);\nAppTopBarComponent = __decorate([Component({\n  selector: 'app-topbar',\n  template: `<div class=\"layout-topbar justify-content-between\">\n        <div class=\"flex align-items-center\">\n            <!-- <button #menubutton class=\"p-link layout-topbar-button\" (click)=\"layoutService.onMenuToggle()\">\n                <i class=\"pi pi-bars\"></i>\n            </button> -->\n            <p-image\n                #menubutton\n                src=\"assets/images/exex.png\"\n                alt=\"Image\"\n                width=\"100\"\n                class=\"p-link\"\n                (click)=\"layoutService.onMenuToggle()\" />\n        </div>\n        <div>\n            <p-avatar\n                [routerLink]=\"pathUser\"\n                class=\"custom-avatar mr-4 p-link\"\n                icon=\"pi pi-user\"\n                image=\"assets/user-avatar.png\"\n                shape=\"circle\"\n                size=\"large\"></p-avatar>\n\n            <i class=\"pi pi-bell mr-3 text-2xl p-link\" pBadge badgeSize=\"small\" severity=\"danger\" value=\"2\"></i>\n\n            <button class=\"p-link layout-topbar-button\" (click)=\"signout()\">\n                <i class=\"pi pi-sign-in text-2xl\"></i>\n            </button>\n        </div>\n    </div>`\n})], AppTopBarComponent);\nexport { AppTopBarComponent };", "map": {"version": 3, "names": ["Path", "Component", "ViewChild", "AppTopBarComponent", "constructor", "layoutService", "authService", "pathUser", "DASHBOARD_USER", "signout", "logout", "__decorate", "selector", "template"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\layout\\app.topbar.component.ts"], "sourcesContent": ["import { Path } from './../core/enums/path.enum';\r\nimport { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { LayoutService } from './app.layout.service';\r\nimport { AuthService } from '../core/service/auth.service';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    template: `<div class=\"layout-topbar justify-content-between\">\r\n        <div class=\"flex align-items-center\">\r\n            <!-- <button #menubutton class=\"p-link layout-topbar-button\" (click)=\"layoutService.onMenuToggle()\">\r\n                <i class=\"pi pi-bars\"></i>\r\n            </button> -->\r\n            <p-image\r\n                #menubutton\r\n                src=\"assets/images/exex.png\"\r\n                alt=\"Image\"\r\n                width=\"100\"\r\n                class=\"p-link\"\r\n                (click)=\"layoutService.onMenuToggle()\" />\r\n        </div>\r\n        <div>\r\n            <p-avatar\r\n                [routerLink]=\"pathUser\"\r\n                class=\"custom-avatar mr-4 p-link\"\r\n                icon=\"pi pi-user\"\r\n                image=\"assets/user-avatar.png\"\r\n                shape=\"circle\"\r\n                size=\"large\"></p-avatar>\r\n\r\n            <i class=\"pi pi-bell mr-3 text-2xl p-link\" pBadge badgeSize=\"small\" severity=\"danger\" value=\"2\"></i>\r\n\r\n            <button class=\"p-link layout-topbar-button\" (click)=\"signout()\">\r\n                <i class=\"pi pi-sign-in text-2xl\"></i>\r\n            </button>\r\n        </div>\r\n    </div>`,\r\n})\r\nexport class AppTopBarComponent {\r\n    pathUser = Path.DASHBOARD_USER;\r\n\r\n    items!: MenuItem[];\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;\r\n\r\n    @ViewChild('topbarmenu') menu!: ElementRef;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {}\r\n\r\n    signout() {\r\n        this.authService.logout();\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,2BAA2B;AAChD,SAASC,SAAS,EAAcC,SAAS,QAAQ,eAAe;AAqCzD,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAW3BC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IAZvB,KAAAC,QAAQ,GAAGP,IAAI,CAACQ,cAAc;EAa3B;EAEHC,OAAOA,CAAA;IACH,IAAI,CAACH,WAAW,CAACI,MAAM,EAAE;EAC7B;CACH;AAd4BC,UAAA,EAAxBT,SAAS,CAAC,YAAY,CAAC,C,qDAAyB;AAElBS,UAAA,EAA9BT,SAAS,CAAC,kBAAkB,CAAC,C,2DAA+B;AAEpCS,UAAA,EAAxBT,SAAS,CAAC,YAAY,CAAC,C,+CAAmB;AATlCC,kBAAkB,GAAAQ,UAAA,EAhC9BV,SAAS,CAAC;EACPW,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6Bb,CAAC,C,EACWV,kBAAkB,CAmB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}