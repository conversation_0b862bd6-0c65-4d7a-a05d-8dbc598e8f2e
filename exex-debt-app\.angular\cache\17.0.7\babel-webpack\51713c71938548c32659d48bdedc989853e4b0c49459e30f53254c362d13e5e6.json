{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { tap } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport { Path } from '../enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-spinner\";\nexport class AuthService {\n  constructor(http, spinner) {\n    this.http = http;\n    this.spinner = spinner;\n    this.redirectTo = inject(Router);\n    const token = localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (token) {\n      this.jwtToken = token;\n    }\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\n    return this.http.post(`/refresh`, {\n      refreshToken\n    }).pipe(tap(response => {\n      localStorage.setItem(Auth.ACCESS_TOKEN, response.access_token);\n    }));\n  }\n  isAuthenticated() {\n    return this.jwtToken != null;\n  }\n  redirectToDashboard() {\n    this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\n    this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n  }\n  login() {\n    this.spinner.show();\n    this.jwtToken = 'ponynguyen';\n    localStorage.setItem(Auth.ACCESS_TOKEN, this.jwtToken);\n    this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n    setTimeout(() => {\n      this.spinner.hide();\n    }, 1000);\n  }\n  logout() {\n    this.jwtToken = null;\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n    this.redirectTo.navigate([Path.AUTH_LOGIN]);\n  }\n  mockUser() {\n    return this.http.get('https://jsonplaceholder.typicode.com/users');\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "Router", "tap", "<PERSON><PERSON>", "Path", "AuthService", "constructor", "http", "spinner", "redirectTo", "token", "localStorage", "getItem", "ACCESS_TOKEN", "jwtToken", "refreshToken", "REFRESH_TOKEN", "post", "pipe", "response", "setItem", "access_token", "isAuthenticated", "redirectToDashboard", "navigate", "DASHBOARD_CUSTOMER", "login", "show", "setTimeout", "hide", "logout", "removeItem", "AUTH_LOGIN", "mockUser", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "NgxSpinnerService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\nimport { Observable, tap } from 'rxjs';\r\nimport { Auth } from '../enums/auth.enum';\r\nimport { Path } from '../enums/path.enum';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n    private jwtToken;\r\n    redirectTo = inject(Router);\r\n\r\n    constructor(\r\n        private http: HttpClient,\r\n        private spinner: NgxSpinnerService,\r\n    ) {\r\n        const token: any = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n        if (token) {\r\n            this.jwtToken = token;\r\n        }\r\n    }\r\n\r\n    refreshToken(): Observable<any> {\r\n        const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\r\n        return this.http.post<any>(`/refresh`, { refreshToken }).pipe(\r\n            tap((response) => {\r\n                localStorage.setItem(Auth.ACCESS_TOKEN, response.access_token);\r\n            }),\r\n        );\r\n    }\r\n\r\n    public isAuthenticated(): boolean {\r\n        return this.jwtToken != null;\r\n    }\r\n\r\n    redirectToDashboard() {\r\n        this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n    }\r\n\r\n    login() {\r\n        this.spinner.show();\r\n        this.jwtToken = 'ponynguyen';\r\n        localStorage.setItem(Auth.ACCESS_TOKEN, this.jwtToken);\r\n        this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n        setTimeout(() => {\r\n            this.spinner.hide();\r\n        }, 1000);\r\n    }\r\n\r\n    logout() {\r\n        this.jwtToken = null;\r\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\r\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\r\n        this.redirectTo.navigate([Path.AUTH_LOGIN]);\r\n    }\r\n\r\n    mockUser(): Observable<any> {\r\n        return this.http.get('https://jsonplaceholder.typicode.com/users');\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,MAAM,QAAoB,eAAe;AAClD,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAAqBC,GAAG,QAAQ,MAAM;AACtC,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,oBAAoB;;;;AAKzC,OAAM,MAAOC,WAAW;EAIpBC,YACYC,IAAgB,EAChBC,OAA0B;IAD1B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IAJnB,KAAAC,UAAU,GAAGT,MAAM,CAACC,MAAM,CAAC;IAMvB,MAAMS,KAAK,GAAQC,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IAC1D,IAAIH,KAAK,EAAE;MACP,IAAI,CAACI,QAAQ,GAAGJ,KAAK;;EAE7B;EAEAK,YAAYA,CAAA;IACR,MAAMA,YAAY,GAAGJ,YAAY,CAACC,OAAO,CAACT,IAAI,CAACa,aAAa,CAAC;IAC7D,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAM,UAAU,EAAE;MAAEF;IAAY,CAAE,CAAC,CAACG,IAAI,CACzDhB,GAAG,CAAEiB,QAAQ,IAAI;MACbR,YAAY,CAACS,OAAO,CAACjB,IAAI,CAACU,YAAY,EAAEM,QAAQ,CAACE,YAAY,CAAC;IAClE,CAAC,CAAC,CACL;EACL;EAEOC,eAAeA,CAAA;IAClB,OAAO,IAAI,CAACR,QAAQ,IAAI,IAAI;EAChC;EAEAS,mBAAmBA,CAAA;IACf,IAAI,CAACT,QAAQ,GAAGH,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IACvD,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACL,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAACqB,kBAAkB,CAAC,CAAC;EACxE;EAEAC,KAAKA,CAAA;IACD,IAAI,CAAClB,OAAO,CAACmB,IAAI,EAAE;IACnB,IAAI,CAACb,QAAQ,GAAG,YAAY;IAC5BH,YAAY,CAACS,OAAO,CAACjB,IAAI,CAACU,YAAY,EAAE,IAAI,CAACC,QAAQ,CAAC;IACtD,IAAI,CAACL,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAACqB,kBAAkB,CAAC,CAAC;IACnDG,UAAU,CAAC,MAAK;MACZ,IAAI,CAACpB,OAAO,CAACqB,IAAI,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACZ;EAEAC,MAAMA,CAAA;IACF,IAAI,CAAChB,QAAQ,GAAG,IAAI;IACpBH,YAAY,CAACoB,UAAU,CAAC5B,IAAI,CAACU,YAAY,CAAC;IAC1CF,YAAY,CAACoB,UAAU,CAAC5B,IAAI,CAACa,aAAa,CAAC;IAC3C,IAAI,CAACP,UAAU,CAACe,QAAQ,CAAC,CAACpB,IAAI,CAAC4B,UAAU,CAAC,CAAC;EAC/C;EAEAC,QAAQA,CAAA;IACJ,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,GAAG,CAAC,4CAA4C,CAAC;EACtE;EAAC,QAAAC,CAAA,G;qBAnDQ9B,WAAW,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXrC,WAAW;IAAAsC,OAAA,EAAXtC,WAAW,CAAAuC,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}