{"ast": null, "code": "import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(subscriber => {\n    const obs = obj[Symbol_observable]();\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(subscriber => {\n    for (let i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(subscriber => {\n    promise.then(value => {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, err => subscriber.error(err)).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(subscriber => {\n    for (const value of iterable) {\n      subscriber.next(value);\n      if (subscriber.closed) {\n        return;\n      }\n    }\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(subscriber => {\n    process(asyncIterable, subscriber).catch(err => subscriber.error(err));\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n  var e_1, _a;\n  return __awaiter(this, void 0, void 0, function* () {\n    try {\n      for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n        const value = asyncIterable_1_1.value;\n        subscriber.next(value);\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "names": ["__asyncValues", "__awaiter", "isArrayLike", "isPromise", "Observable", "isInteropObservable", "isAsyncIterable", "createInvalidObservableTypeError", "isIterable", "isReadableStreamLike", "readableStreamLikeToAsyncGenerator", "isFunction", "reportUnhandledError", "observable", "Symbol_observable", "innerFrom", "input", "fromInteropObservable", "fromArrayLike", "fromPromise", "fromAsyncIterable", "fromIterable", "fromReadableStreamLike", "obj", "subscriber", "obs", "subscribe", "TypeError", "array", "i", "length", "closed", "next", "complete", "promise", "then", "value", "err", "error", "iterable", "asyncIterable", "process", "catch", "readableStream", "asyncIterable_1", "asyncIterable_1_1", "e_1", "_a", "done", "e_1_1", "return", "call"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/rxjs/dist/esm/internal/observable/innerFrom.js"], "sourcesContent": ["import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n    if (input instanceof Observable) {\n        return input;\n    }\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return fromInteropObservable(input);\n        }\n        if (isArrayLike(input)) {\n            return fromArrayLike(input);\n        }\n        if (isPromise(input)) {\n            return fromPromise(input);\n        }\n        if (isAsyncIterable(input)) {\n            return fromAsyncIterable(input);\n        }\n        if (isIterable(input)) {\n            return fromIterable(input);\n        }\n        if (isReadableStreamLike(input)) {\n            return fromReadableStreamLike(input);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n    return new Observable((subscriber) => {\n        const obs = obj[Symbol_observable]();\n        if (isFunction(obs.subscribe)) {\n            return obs.subscribe(subscriber);\n        }\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    });\n}\nexport function fromArrayLike(array) {\n    return new Observable((subscriber) => {\n        for (let i = 0; i < array.length && !subscriber.closed; i++) {\n            subscriber.next(array[i]);\n        }\n        subscriber.complete();\n    });\n}\nexport function fromPromise(promise) {\n    return new Observable((subscriber) => {\n        promise\n            .then((value) => {\n            if (!subscriber.closed) {\n                subscriber.next(value);\n                subscriber.complete();\n            }\n        }, (err) => subscriber.error(err))\n            .then(null, reportUnhandledError);\n    });\n}\nexport function fromIterable(iterable) {\n    return new Observable((subscriber) => {\n        for (const value of iterable) {\n            subscriber.next(value);\n            if (subscriber.closed) {\n                return;\n            }\n        }\n        subscriber.complete();\n    });\n}\nexport function fromAsyncIterable(asyncIterable) {\n    return new Observable((subscriber) => {\n        process(asyncIterable, subscriber).catch((err) => subscriber.error(err));\n    });\n}\nexport function fromReadableStreamLike(readableStream) {\n    return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n    var asyncIterable_1, asyncIterable_1_1;\n    var e_1, _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n                const value = asyncIterable_1_1.value;\n                subscriber.next(value);\n                if (subscriber.closed) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        subscriber.complete();\n    });\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gCAAgC,QAAQ,gCAAgC;AACjF,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,EAAEC,kCAAkC,QAAQ,8BAA8B;AACvG,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AACtE,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,YAAYZ,UAAU,EAAE;IAC7B,OAAOY,KAAK;EAChB;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,IAAIX,mBAAmB,CAACW,KAAK,CAAC,EAAE;MAC5B,OAAOC,qBAAqB,CAACD,KAAK,CAAC;IACvC;IACA,IAAId,WAAW,CAACc,KAAK,CAAC,EAAE;MACpB,OAAOE,aAAa,CAACF,KAAK,CAAC;IAC/B;IACA,IAAIb,SAAS,CAACa,KAAK,CAAC,EAAE;MAClB,OAAOG,WAAW,CAACH,KAAK,CAAC;IAC7B;IACA,IAAIV,eAAe,CAACU,KAAK,CAAC,EAAE;MACxB,OAAOI,iBAAiB,CAACJ,KAAK,CAAC;IACnC;IACA,IAAIR,UAAU,CAACQ,KAAK,CAAC,EAAE;MACnB,OAAOK,YAAY,CAACL,KAAK,CAAC;IAC9B;IACA,IAAIP,oBAAoB,CAACO,KAAK,CAAC,EAAE;MAC7B,OAAOM,sBAAsB,CAACN,KAAK,CAAC;IACxC;EACJ;EACA,MAAMT,gCAAgC,CAACS,KAAK,CAAC;AACjD;AACA,OAAO,SAASC,qBAAqBA,CAACM,GAAG,EAAE;EACvC,OAAO,IAAInB,UAAU,CAAEoB,UAAU,IAAK;IAClC,MAAMC,GAAG,GAAGF,GAAG,CAACT,iBAAiB,CAAC,CAAC,CAAC;IACpC,IAAIH,UAAU,CAACc,GAAG,CAACC,SAAS,CAAC,EAAE;MAC3B,OAAOD,GAAG,CAACC,SAAS,CAACF,UAAU,CAAC;IACpC;IACA,MAAM,IAAIG,SAAS,CAAC,gEAAgE,CAAC;EACzF,CAAC,CAAC;AACN;AACA,OAAO,SAAST,aAAaA,CAACU,KAAK,EAAE;EACjC,OAAO,IAAIxB,UAAU,CAAEoB,UAAU,IAAK;IAClC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,IAAI,CAACN,UAAU,CAACO,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzDL,UAAU,CAACQ,IAAI,CAACJ,KAAK,CAACC,CAAC,CAAC,CAAC;IAC7B;IACAL,UAAU,CAACS,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,SAASd,WAAWA,CAACe,OAAO,EAAE;EACjC,OAAO,IAAI9B,UAAU,CAAEoB,UAAU,IAAK;IAClCU,OAAO,CACFC,IAAI,CAAEC,KAAK,IAAK;MACjB,IAAI,CAACZ,UAAU,CAACO,MAAM,EAAE;QACpBP,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtBZ,UAAU,CAACS,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAGI,GAAG,IAAKb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC,CAAC,CAC7BF,IAAI,CAAC,IAAI,EAAEvB,oBAAoB,CAAC;EACzC,CAAC,CAAC;AACN;AACA,OAAO,SAASS,YAAYA,CAACkB,QAAQ,EAAE;EACnC,OAAO,IAAInC,UAAU,CAAEoB,UAAU,IAAK;IAClC,KAAK,MAAMY,KAAK,IAAIG,QAAQ,EAAE;MAC1Bf,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;MACtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;QACnB;MACJ;IACJ;IACAP,UAAU,CAACS,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,SAASb,iBAAiBA,CAACoB,aAAa,EAAE;EAC7C,OAAO,IAAIpC,UAAU,CAAEoB,UAAU,IAAK;IAClCiB,OAAO,CAACD,aAAa,EAAEhB,UAAU,CAAC,CAACkB,KAAK,CAAEL,GAAG,IAAKb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC,CAAC;EAC5E,CAAC,CAAC;AACN;AACA,OAAO,SAASf,sBAAsBA,CAACqB,cAAc,EAAE;EACnD,OAAOvB,iBAAiB,CAACV,kCAAkC,CAACiC,cAAc,CAAC,CAAC;AAChF;AACA,SAASF,OAAOA,CAACD,aAAa,EAAEhB,UAAU,EAAE;EACxC,IAAIoB,eAAe,EAAEC,iBAAiB;EACtC,IAAIC,GAAG,EAAEC,EAAE;EACX,OAAO9C,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,IAAI;MACA,KAAK2C,eAAe,GAAG5C,aAAa,CAACwC,aAAa,CAAC,EAAEK,iBAAiB,GAAG,MAAMD,eAAe,CAACZ,IAAI,CAAC,CAAC,EAAE,CAACa,iBAAiB,CAACG,IAAI,GAAG;QAC7H,MAAMZ,KAAK,GAAGS,iBAAiB,CAACT,KAAK;QACrCZ,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;UACnB;QACJ;MACJ;IACJ,CAAC,CACD,OAAOkB,KAAK,EAAE;MAAEH,GAAG,GAAG;QAAER,KAAK,EAAEW;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIJ,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,IAAI,KAAKD,EAAE,GAAGH,eAAe,CAACM,MAAM,CAAC,EAAE,MAAMH,EAAE,CAACI,IAAI,CAACP,eAAe,CAAC;MACrH,CAAC,SACO;QAAE,IAAIE,GAAG,EAAE,MAAMA,GAAG,CAACR,KAAK;MAAE;IACxC;IACAd,UAAU,CAACS,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}