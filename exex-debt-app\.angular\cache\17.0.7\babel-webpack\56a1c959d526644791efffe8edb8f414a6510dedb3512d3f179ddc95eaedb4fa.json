{"ast": null, "code": "export const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined))();\nexport function errorNotification(error) {\n  return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n  return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n  return {\n    kind,\n    value,\n    error\n  };\n}", "map": {"version": 3, "names": ["COMPLETE_NOTIFICATION", "createNotification", "undefined", "errorNotification", "error", "nextNotification", "value", "kind"], "sources": ["C:/DATA/Source/PD EXEX ADD ON PC/pd_exex_add_on_pc/ADDON/exex-debt-app/node_modules/rxjs/dist/esm/internal/NotificationFactories.js"], "sourcesContent": ["export const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined))();\nexport function errorNotification(error) {\n    return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n    return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n    return {\n        kind,\n        value,\n        error,\n    };\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG,CAAC,MAAMC,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEA,SAAS,CAAC,EAAE,CAAC;AAC5F,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACrC,OAAOH,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEE,KAAK,CAAC;AACpD;AACA,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACpC,OAAOL,kBAAkB,CAAC,GAAG,EAAEK,KAAK,EAAEJ,SAAS,CAAC;AACpD;AACA,OAAO,SAASD,kBAAkBA,CAACM,IAAI,EAAED,KAAK,EAAEF,KAAK,EAAE;EACnD,OAAO;IACHG,IAAI;IACJD,KAAK;IACLF;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}