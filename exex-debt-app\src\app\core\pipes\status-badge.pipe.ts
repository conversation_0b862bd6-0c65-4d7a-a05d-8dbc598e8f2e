import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'statusBadge'
})
export class StatusBadgePipe implements PipeTransform {
    transform(status: string): string {
        if (!status) return '';
        
        const statusLower = status.toLowerCase();
        let badgeClass = '';
        let icon = '';
        
        switch (statusLower) {
            case 'active':
                badgeClass = 'status-badge-active';
                icon = 'pi pi-check-circle';
                break;
            case 'inactive':
                badgeClass = 'status-badge-inactive';
                icon = 'pi pi-times-circle';
                break;
            case 'pending':
                badgeClass = 'status-badge-pending';
                icon = 'pi pi-clock';
                break;
            case 'suspended':
                badgeClass = 'status-badge-suspended';
                icon = 'pi pi-ban';
                break;
            case 'vip':
                badgeClass = 'status-badge-vip';
                icon = 'pi pi-star-fill';
                break;
            default:
                badgeClass = 'status-badge-default';
                icon = 'pi pi-info-circle';
        }
        
        return `<span class="status-badge ${badgeClass}">
                    <i class="${icon}"></i>
                    <span class="status-text">${status}</span>
                </span>`;
    }
}
